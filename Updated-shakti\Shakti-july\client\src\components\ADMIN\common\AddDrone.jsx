import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Bot,
  Calendar,
  Settings,
  Zap,
  Weight,
  Package,
  Radio,
  Cpu,
  RotateCcw,
  Building,
  Phone,
  Upload,
  CheckCircle,
  AlertCircle,
  Save,
  RefreshCw,
  X,
  Download,
  Award,
  Plus
} from "lucide-react";

import AdminSidebar from "../common/AdminSidebar";
import droneService from '../../../services/droneService';
import { useAuth } from '../../../context/AuthContext';

const AddDrone = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  // Enhanced state management
  const [droneData, setDroneData] = useState({
    droneName: "",
    droneType: "",
    droneId: "",
    manufacturer: "",
    droneStatus: "",
    deploymentDate: "",
    maxWeight: "",
    payload: "",
    sprayType: "",
    nozzleCount: "",
    remoteType: "",
    remoteSerial: "",
    proportionType: "",
    flightController: "",
    propCW1: "",
    motorCW1: "",
    propCW2: "",
    motorCW2: "",
    propCCW1: "",
    motorCCW1: "",
    propCCW2: "",
    motorCCW2: "",
  });

  const [allocationData, setAllocationData] = useState({
    organization: "",
    pocContact: "",
    allocationFile: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showAllocationDetails, setShowAllocationDetails] = useState(false);
  const [showCertificate, setShowCertificate] = useState(false);
  const [certificateData, setCertificateData] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);

  // Validation functions
  const validateDroneId = (id) => {
    const droneIdRegex = /^[A-Z0-9]{6,12}$/;
    return droneIdRegex.test(id);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const validateForm = () => {
    const newErrors = {};

    // Basic drone information validation (matching backend requirements)
    if (!droneData.droneName.trim()) {
      newErrors.droneName = 'Drone name is required';
    } else if (droneData.droneName.trim().length < 2 || droneData.droneName.trim().length > 100) {
      newErrors.droneName = 'Drone name must be between 2 and 100 characters';
    }

    if (!droneData.droneType) {
      newErrors.droneType = 'Drone type is required';
    } else {
      const validTypes = ['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'];
      if (!validTypes.includes(droneData.droneType)) {
        newErrors.droneType = 'Please select a valid drone type';
      }
    }

    if (!droneData.droneId.trim()) {
      newErrors.droneId = 'Drone ID is required';
    } else if (droneData.droneId.trim().length < 3 || droneData.droneId.trim().length > 50) {
      newErrors.droneId = 'Drone ID must be between 3 and 50 characters';
    }

    if (!droneData.manufacturer.trim()) {
      newErrors.manufacturer = 'Manufacturer is required';
    } else if (droneData.manufacturer.trim().length < 2 || droneData.manufacturer.trim().length > 100) {
      newErrors.manufacturer = 'Manufacturer must be between 2 and 100 characters';
    }

    // Weight validation (backend requires 0.1 to 25 kg)
    if (!droneData.maxWeight || droneData.maxWeight.toString().trim() === '') {
      newErrors.maxWeight = 'Weight is required';
    } else {
      const weight = parseFloat(droneData.maxWeight);
      if (isNaN(weight) || weight < 0.1 || weight > 25) {
        newErrors.maxWeight = 'Weight must be between 0.1 and 25 kg';
      }
    }

    // Payload validation (backend requires 0 to 10 kg)
    if (!droneData.payload || droneData.payload.toString().trim() === '') {
      newErrors.payload = 'Payload is required';
    } else {
      const payload = parseFloat(droneData.payload);
      if (isNaN(payload) || payload < 0 || payload > 10) {
        newErrors.payload = 'Payload must be between 0 and 10 kg';
      }
    }

    setErrors(newErrors);

    // Debug logging to see what's failing
    if (Object.keys(newErrors).length > 0) {
      console.log('❌ VALIDATION ERRORS:', newErrors);
      console.log('📋 CURRENT FORM DATA:', droneData);
      console.log('🔍 REQUIRED FIELDS CHECK:');
      console.log('- Drone Name:', droneData.droneName ? '✅' : '❌', droneData.droneName);
      console.log('- Drone Type:', droneData.droneType ? '✅' : '❌', droneData.droneType);
      console.log('- Drone ID:', droneData.droneId ? '✅' : '❌', droneData.droneId);
      console.log('- Manufacturer:', droneData.manufacturer ? '✅' : '❌', droneData.manufacturer);
      console.log('- Max Weight:', droneData.maxWeight ? '✅' : '❌', droneData.maxWeight);
      console.log('- Payload:', droneData.payload ? '✅' : '❌', droneData.payload);
    } else {
      console.log('✅ VALIDATION PASSED');
      console.log('📤 READY TO SUBMIT:', {
        serialNumber: droneData.droneId,
        model: droneData.droneName,
        manufacturer: droneData.manufacturer,
        type: droneData.droneType,
        weight: droneData.maxWeight,
        payload: droneData.payload
      });
    }

    return Object.keys(newErrors).length === 0;
  };

  const validateAllocation = () => {
    const newErrors = {};

    if (!allocationData.organization) newErrors.organization = 'Organization selection is required';
    if (!allocationData.pocContact.trim()) {
      newErrors.pocContact = 'POC contact is required';
    } else if (!validatePhone(allocationData.pocContact)) {
      newErrors.pocContact = 'Please enter a valid 10-digit mobile number';
    }

    setErrors(prev => ({ ...prev, ...newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Format drone ID to uppercase
    const formattedValue = name === 'droneId' ? value.toUpperCase() : value;

    setDroneData((prev) => ({
      ...prev,
      [name]: formattedValue,
    }));
  };

  const handleAllocationChange = (e) => {
    const { name, value } = e.target;

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    setAllocationData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];

    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          allocationFile: 'File size should be less than 5MB'
        }));
        return;
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          allocationFile: 'Only PDF, JPG, JPEG, and PNG files are allowed'
        }));
        return;
      }

      // Clear error and set file
      if (errors.allocationFile) {
        setErrors(prev => ({
          ...prev,
          allocationFile: ''
        }));
      }

      setUploadedFile(file);
      setAllocationData(prev => ({
        ...prev,
        allocationFile: file
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare drone data for API (matching backend validation requirements exactly)
      const apiData = {
        name: droneData.droneName.trim(), // Required: Drone name (2-100 chars)
        model: droneData.droneName.trim(), // Required: Drone model (2-100 chars)
        serialNumber: droneData.droneId.trim(), // Required: Serial number (3-50 chars)
        registrationNumber: `REG-${droneData.droneId.trim()}-${Date.now()}`, // Required: Registration number (3-50 chars)
        manufacturer: droneData.manufacturer.trim(), // Required: Manufacturer (2-100 chars)
        specifications: {
          type: droneData.droneType, // Required: Drone type (enum)
          weight: Number(parseFloat(droneData.maxWeight).toFixed(2)) || 1.0, // Required: Weight (0.1-25 kg)
          maxPayload: Number(parseFloat(droneData.payload).toFixed(2)) || 0.5, // Required: Max payload (0-10 kg)
          maxFlightTime: parseInt(30), // Required: Flight time (1-300 minutes) - integer
          maxRange: Number(parseFloat(5.0).toFixed(1)), // Required: Range (0.1-100 km) - float
          maxAltitude: parseInt(120), // Required: Altitude (1-500 meters) - integer
          maxSpeed: parseInt(50), // Required: Speed (1-200 km/h) - integer
          batteryCapacity: parseInt(5000), // Required: Battery (100-50000 mAh) - integer
          hasGPS: true,
          hasGimbal: false,
          hasObstacleAvoidance: false
        },
        status: 'active', // Optional: Default status (enum)
        condition: 'excellent', // Optional: Default condition (enum)
        purchase: {
          purchaseDate: new Date().toISOString(), // Required: ISO8601 date
          purchasePrice: Number((parseFloat(droneData.maxWeight) * 10000 || 50000).toFixed(2)), // Required: Price (>= 0)
          vendor: droneData.manufacturer.trim() // Required: Vendor (2-100 chars)
        },
        currentLocation: {
          latitude: 28.6139, // Default latitude for New Delhi
          longitude: 77.2090, // Default longitude for New Delhi
          altitude: 0, // Default altitude
          lastUpdated: new Date()
        }
      };

      // Only add organizationId if user has one (backend will handle default)
      if (user?.profile?.organizationId) {
        apiData.organizationId = user.profile.organizationId;
      }

      console.log('🚀 Sending drone data to API:', JSON.stringify(apiData, null, 2));

      // Validate the API data structure before sending
      console.log('🔍 API Data Validation Check:');
      console.log('- name:', typeof apiData.name, apiData.name?.length, 'chars');
      console.log('- serialNumber:', typeof apiData.serialNumber, apiData.serialNumber?.length, 'chars');
      console.log('- registrationNumber:', typeof apiData.registrationNumber, apiData.registrationNumber?.length, 'chars');
      console.log('- manufacturer:', typeof apiData.manufacturer, apiData.manufacturer?.length, 'chars');
      console.log('- specifications.type:', apiData.specifications?.type);
      console.log('- specifications.weight:', typeof apiData.specifications?.weight, apiData.specifications?.weight);
      console.log('- specifications.maxPayload:', typeof apiData.specifications?.maxPayload, apiData.specifications?.maxPayload);

      // Call the API
      const response = await droneService.createDrone(apiData);

      console.log('✅ API Response:', response);

      if (response.success) {
        console.log('✅ Drone created successfully:', response.data);
        setShowSuccess(true);

        // Reset form data
        handleReset();

        // Show success message briefly then navigate
        setTimeout(() => {
          setShowSuccess(false);
          navigate('/dronepage');
        }, 2000);
      }

    } catch (error) {
      console.error('❌ Drone creation error:', error);

      // Handle specific error types
      let errorMessage = 'Failed to add drone. Please try again.';

      if (error.message.includes('Serial number') || error.message.includes('serialNumber')) {
        errorMessage = 'A drone with this serial number already exists. Please use a different drone ID.';
      } else if (error.message.includes('Weight') || error.message.includes('weight')) {
        errorMessage = 'Invalid weight. Must be between 0.1 and 25 kg.';
      } else if (error.message.includes('payload') || error.message.includes('Payload')) {
        errorMessage = 'Invalid payload. Must be between 0 and 10 kg.';
      } else if (error.message.includes('Organization') || error.message.includes('organization')) {
        errorMessage = 'Organization validation failed. Please contact administrator.';
      } else if (error.message.includes('Validation') || error.message.includes('validation')) {
        errorMessage = 'Form validation failed. Please check all required fields and try again.';
      } else if (error.message.includes('Authentication') || error.message.includes('authentication')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.message.includes('permission') || error.message.includes('Permission')) {
        errorMessage = 'You do not have permission to add drones. Please contact administrator.';
      } else {
        errorMessage = error.message || errorMessage;
      }

      setErrors({ submit: errorMessage });

      // Scroll to top to show error message
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAllocateClick = () => {
    if (!validateForm()) {
      return;
    }
    setShowAllocationDetails(true);
  };

  const handleReset = () => {
    setDroneData({
      droneName: "",
      droneType: "",
      droneId: "",
      manufacturer: "",
      droneStatus: "",
      deploymentDate: "",
      maxWeight: "",
      payload: "",
      sprayType: "",
      nozzleCount: "",
      remoteType: "",
      remoteSerial: "",
      proportionType: "",
      flightController: "",
      propCW1: "",
      motorCW1: "",
      propCW2: "",
      motorCW2: "",
      propCCW1: "",
      motorCCW1: "",
      propCCW2: "",
      motorCCW2: "",
    });
    setAllocationData({
      organization: "",
      pocContact: "",
      allocationFile: null
    });
    setUploadedFile(null);
    setErrors({});
    setShowAllocationDetails(false);
    setShowCertificate(false);
    setCertificateData(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 text-black">
      <AdminSidebar />
      <div className="ml-[250px]">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Bot className="text-blue-600" />
                Add New Drone
              </h2>
              <p className="text-gray-600 mt-1">
                Register a new drone in the SHAKTI system
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                Reset Form
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {/* Success Message */}
          {showSuccess && (
            <div className="max-w-4xl mx-auto mb-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <div>
                      <h3 className="text-green-800 font-medium">Drone Added Successfully!</h3>
                      <p className="text-green-700 text-sm">The drone has been registered in the SHAKTI system.</p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setShowSuccess(false);
                      navigate('/dronepage');
                    }}
                    className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
                  >
                    View Drones
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit}>
              {/* Basic Drone Information */}
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Bot className="w-5 h-5 text-blue-600" />
                  Basic Drone Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Drone Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Name *
                    </label>
                    <input
                      type="text"
                      name="droneName"
                      value={droneData.droneName}
                      onChange={handleChange}
                      placeholder="Enter drone name"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.droneName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.droneName && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.droneName}
                      </p>
                    )}
                  </div>

                  {/* Drone Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Type *
                    </label>
                    <select
                      name="droneType"
                      value={droneData.droneType}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.droneType ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select Drone Type</option>
                      <option value="quadcopter">Quadcopter</option>
                      <option value="hexacopter">Hexacopter</option>
                      <option value="octocopter">Octocopter</option>
                      <option value="fixed-wing">Fixed Wing</option>
                      <option value="hybrid">Hybrid</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.droneType && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.droneType}
                      </p>
                    )}
                  </div>

                  {/* Drone ID */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone ID *
                    </label>
                    <input
                      type="text"
                      name="droneId"
                      value={droneData.droneId}
                      onChange={handleChange}
                      placeholder="DRONE001"
                      maxLength="12"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors uppercase ${
                        errors.droneId ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.droneId && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.droneId}
                      </p>
                    )}
                  </div>

                  {/* Manufacturer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Manufacturer *
                    </label>
                    <input
                      type="text"
                      name="manufacturer"
                      value={droneData.manufacturer}
                      onChange={handleChange}
                      placeholder="Enter manufacturer name"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.manufacturer ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.manufacturer && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.manufacturer}
                      </p>
                    )}
                  </div>

                  {/* Drone Status */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Status *
                    </label>
                    <select
                      name="droneStatus"
                      value={droneData.droneStatus}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.droneStatus ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select Status</option>
                      <option value="Active">Active</option>
                      <option value="Inactive">Inactive</option>
                      <option value="Maintenance">Maintenance</option>
                    </select>
                    {errors.droneStatus && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.droneStatus}
                      </p>
                    )}
                  </div>

                  {/* Deployment Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Deployment Date *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="date"
                        name="deploymentDate"
                        value={droneData.deploymentDate}
                        onChange={handleChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.deploymentDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.deploymentDate && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.deploymentDate}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Drone Specifications */}
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Settings className="w-5 h-5 text-blue-600" />
                  Drone Specifications
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Max Weight */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Max Take-off Weight (kg) *
                    </label>
                    <div className="relative">
                      <Weight className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        name="maxWeight"
                        value={droneData.maxWeight}
                        onChange={handleChange}
                        placeholder="Enter max weight"
                        min="0"
                        step="0.1"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.maxWeight ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.maxWeight && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.maxWeight}
                      </p>
                    )}
                  </div>

                  {/* Payload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payload Capacity (kg) *
                    </label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        name="payload"
                        value={droneData.payload}
                        onChange={handleChange}
                        placeholder="Enter payload capacity"
                        min="0"
                        step="0.1"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.payload ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.payload && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.payload}
                      </p>
                    )}
                  </div>

                  {/* Spray System Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Spray System Type *
                    </label>
                    <div className="relative">
                      <Zap className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <select
                        name="sprayType"
                        value={droneData.sprayType}
                        onChange={handleChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.sprayType ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select Spray Type</option>
                        <option value="Manual">Manual</option>
                        <option value="Automatic">Automatic</option>
                        <option value="Hybrid">Hybrid</option>
                      </select>
                    </div>
                    {errors.sprayType && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.sprayType}
                      </p>
                    )}
                  </div>

                  {/* Number of Nozzles */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number of Nozzles *
                    </label>
                    <input
                      type="number"
                      name="nozzleCount"
                      value={droneData.nozzleCount}
                      onChange={handleChange}
                      placeholder="Enter nozzle count"
                      min="1"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.nozzleCount ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.nozzleCount && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.nozzleCount}
                      </p>
                    )}
                  </div>

                  {/* Remote Controller Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Remote Controller Type
                    </label>
                    <div className="relative">
                      <Radio className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="remoteType"
                        value={droneData.remoteType}
                        onChange={handleChange}
                        placeholder="Enter remote controller type"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                    </div>
                  </div>

                  {/* Remote Controller Serial */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Remote Controller Serial No
                    </label>
                    <input
                      type="text"
                      name="remoteSerial"
                      value={droneData.remoteSerial}
                      onChange={handleChange}
                      placeholder="Enter serial number"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    />
                  </div>

                  {/* Proportion Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Proportion Type
                    </label>
                    <select
                      name="proportionType"
                      value={droneData.proportionType}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    >
                      <option value="">Select Proportion Type</option>
                      <option value="Fixed">Fixed</option>
                      <option value="Variable">Variable</option>
                    </select>
                  </div>

                  {/* Flight Controller Serial */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Flight Controller Serial No
                    </label>
                    <div className="relative">
                      <Cpu className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="flightController"
                        value={droneData.flightController}
                        onChange={handleChange}
                        placeholder="Enter flight controller serial"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Motor and Propeller Details */}
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <RotateCcw className="w-5 h-5 text-blue-600" />
                  Motor & Propeller Configuration
                </h3>

                {/* Responsive Grid Layout */}
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                  {/* CW-1 Section */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="text-center mb-4">
                      <h4 className="font-semibold text-gray-800 text-sm">Clockwise 1 (CW-1)</h4>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Propeller</label>
                        <input
                          type="text"
                          name="propCW1"
                          value={droneData.propCW1}
                          onChange={handleChange}
                          placeholder="Propeller CW-1"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Motor</label>
                        <input
                          type="text"
                          name="motorCW1"
                          value={droneData.motorCW1}
                          onChange={handleChange}
                          placeholder="Motor CW-1"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* CW-2 Section */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="text-center mb-4">
                      <h4 className="font-semibold text-gray-800 text-sm">Clockwise 2 (CW-2)</h4>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Propeller</label>
                        <input
                          type="text"
                          name="propCW2"
                          value={droneData.propCW2}
                          onChange={handleChange}
                          placeholder="Propeller CW-2"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Motor</label>
                        <input
                          type="text"
                          name="motorCW2"
                          value={droneData.motorCW2}
                          onChange={handleChange}
                          placeholder="Motor CW-2"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* CCW-1 Section */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="text-center mb-4">
                      <h4 className="font-semibold text-gray-800 text-sm">Counter-Clockwise 1 (CCW-1)</h4>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Propeller</label>
                        <input
                          type="text"
                          name="propCCW1"
                          value={droneData.propCCW1}
                          onChange={handleChange}
                          placeholder="Propeller CCW-1"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Motor</label>
                        <input
                          type="text"
                          name="motorCCW1"
                          value={droneData.motorCCW1}
                          onChange={handleChange}
                          placeholder="Motor CCW-1"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* CCW-2 Section */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="text-center mb-4">
                      <h4 className="font-semibold text-gray-800 text-sm">Counter-Clockwise 2 (CCW-2)</h4>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Propeller</label>
                        <input
                          type="text"
                          name="propCCW2"
                          value={droneData.propCCW2}
                          onChange={handleChange}
                          placeholder="Propeller CCW-2"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Motor</label>
                        <input
                          type="text"
                          name="motorCCW2"
                          value={droneData.motorCCW2}
                          onChange={handleChange}
                          placeholder="Motor CCW-2"
                          className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Visual Guide */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <RotateCcw className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Configuration Guide</span>
                  </div>
                  <p className="text-xs text-blue-700">
                    CW (Clockwise) and CCW (Counter-Clockwise) refer to the rotation direction of the propellers.
                    Ensure proper motor and propeller pairing for optimal drone performance.
                  </p>
                </div>
              </div>

              {/* Submit Section */}
              <div className="p-6">
                {errors.submit && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <AlertCircle className="w-5 h-5 text-red-600" />
                    <p className="text-red-800">{errors.submit}</p>
                  </div>
                )}

                <div className="flex items-center justify-center gap-4">
                  <button
                    type="button"
                    onClick={handleReset}
                    className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reset Form
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔍 DEBUG - Current Form Data:', droneData);
                      console.log('🔍 DEBUG - Validation Result:', validateForm());
                    }}
                    className="flex items-center gap-2 px-4 py-2 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors text-sm text-blue-600"
                  >
                    🔍 Debug
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        Adding Drone...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Add Drone
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleAllocateClick}
                    className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    <Plus className="w-4 h-4" />
                    Allocate Drone
                  </button>
                </div>
              </div>
            </form>
          </div>

          {/* Allocation Details */}
          {showAllocationDetails && (
            <div className="max-w-4xl mx-auto mt-6 bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Building className="w-5 h-5 text-blue-600" />
                  Drone Allocation Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Organization Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Organization *
                    </label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <select
                        name="organization"
                        value={allocationData.organization}
                        onChange={handleAllocationChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.organization ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select Organization</option>
                        <option value="Sky Drone Solutions">Sky Drone Solutions</option>
                        <option value="EcoDrone Solutions">EcoDrone Solutions</option>
                        <option value="SkyFarm Ltd.">SkyFarm Ltd.</option>
                        <option value="AgriTech India">AgriTech India</option>
                        <option value="FarmVue Aerial">FarmVue Aerial</option>
                      </select>
                    </div>
                    {errors.organization && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.organization}
                      </p>
                    )}
                  </div>

                  {/* POC Contact */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC Contact Number *
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        name="pocContact"
                        value={allocationData.pocContact}
                        onChange={handleAllocationChange}
                        placeholder="Enter 10-digit mobile number"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.pocContact ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.pocContact && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocContact}
                      </p>
                    )}
                  </div>

                  {/* File Upload */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Allocation Document
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      errors.allocationFile ? 'border-red-300 bg-red-50' :
                      uploadedFile ? 'border-green-300 bg-green-50' :
                      'border-gray-300 hover:border-gray-400'
                    }`}>
                      {uploadedFile ? (
                        <div className="flex items-center justify-center gap-3">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <div>
                            <p className="text-sm font-medium text-green-800">{uploadedFile.name}</p>
                            <p className="text-xs text-green-600">
                              {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFile(null);
                              setAllocationData(prev => ({ ...prev, allocationFile: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-5 h-5" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <p className="text-sm text-gray-600 mb-2">Drop your file here or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline font-medium">browse</span>
                            <input
                              type="file"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-2">PDF, JPG, JPEG, PNG (Max 5MB)</p>
                        </>
                      )}
                    </div>
                    {errors.allocationFile && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.allocationFile}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-center">
                  <button
                    type="button"
                    onClick={() => {
                      if (!validateAllocation()) {
                        return;
                      }
                      const cert = {
                        certificateNumber: `PRYMCERT-${Date.now().toString().slice(-4)}`,
                        organizationName: allocationData.organization,
                        droneId: droneData.droneId,
                        shaktiId: `SHAKTI${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
                        allocationDate: new Date().toLocaleDateString("en-GB", {
                          day: '2-digit', month: 'short', year: 'numeric'
                        })
                      };
                      setCertificateData(cert);
                      setShowCertificate(true);
                    }}
                    className="flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    <Award className="w-4 h-4" />
                    Generate Certificate
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Certificate Preview */}
          {showCertificate && certificateData && (
            <div className="max-w-4xl mx-auto mt-6 bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Award className="w-5 h-5 text-blue-600" />
                    Certificate Preview
                  </h3>
                  <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <Download className="w-4 h-4" />
                    Download Certificate
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="border-2 border-gray-200 rounded-lg p-8 bg-gradient-to-br from-blue-50 to-indigo-50">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">Drone Allocation Certificate</h3>
                    <p className="text-sm text-gray-600 font-medium">
                      Certificate No: <span className="text-blue-600 font-mono">{certificateData.certificateNumber}</span>
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div className="space-y-4">
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-600 font-medium mb-1">Organization Name</p>
                        <p className="text-gray-900 font-semibold">{certificateData.organizationName}</p>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-600 font-medium mb-1">Drone ID</p>
                        <p className="text-gray-900 font-semibold font-mono">{certificateData.droneId}</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-600 font-medium mb-1">Allocation Date</p>
                        <p className="text-gray-900 font-semibold">{certificateData.allocationDate}</p>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-600 font-medium mb-1">S.H.A.K.T.I ID</p>
                        <p className="text-gray-900 font-semibold font-mono">{certificateData.shaktiId}</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200 text-center">
                    <p className="text-xs text-gray-500">
                      This certificate is issued by the SHAKTI Drone Management System
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex items-center justify-center gap-4">
                  <button
                    type="button"
                    onClick={() => setShowCertificate(false)}
                    className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    <X className="w-4 h-4" />
                    Close Preview
                  </button>
                  <button
                    type="button"
                    className="flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Finalize Allocation
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddDrone;
