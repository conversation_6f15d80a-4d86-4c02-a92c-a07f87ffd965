const mongoose = require('mongoose');

const organizationSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Organization name is required'],
    trim: true,
    minlength: [2, 'Organization name must be at least 2 characters long'],
    maxlength: [100, 'Organization name cannot exceed 100 characters']
  },
  displayName: {
    type: String,
    trim: true,
    maxlength: [100, 'Display name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  type: {
    type: String,
    required: [true, 'Organization type is required'],
    enum: {
      values: ['government', 'private', 'ngo', 'research', 'military', 'other'],
      message: 'Organization type must be one of: government, private, ngo, research, military, other'
    }
  },
  
  // Contact Information
  contact: {
    primaryEmail: {
      type: String,
      required: [true, 'Primary email is required'],
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address'
      ]
    },
    secondaryEmail: {
      type: String,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address'
      ]
    },
    phone: {
      type: String,
      required: [true, 'Phone number is required'],
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number'
      ]
    },
    alternatePhone: {
      type: String,
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number'
      ]
    },
    website: {
      type: String,
      trim: true,
      match: [
        /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
        'Please enter a valid website URL'
      ]
    }
  },

  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true,
      maxlength: [200, 'Street address cannot exceed 200 characters']
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
      maxlength: [100, 'City name cannot exceed 100 characters']
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
      maxlength: [100, 'State name cannot exceed 100 characters']
    },
    country: {
      type: String,
      required: [true, 'Country is required'],
      trim: true,
      maxlength: [100, 'Country name cannot exceed 100 characters']
    },
    postalCode: {
      type: String,
      required: [true, 'Postal code is required'],
      trim: true,
      maxlength: [20, 'Postal code cannot exceed 20 characters']
    },
    coordinates: {
      latitude: {
        type: Number,
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
      },
      longitude: {
        type: Number,
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
      }
    }
  },

  // Registration and Legal Information
  registration: {
    registrationNumber: {
      type: String,
      required: [true, 'Registration number is required'],
      trim: true,
      maxlength: [50, 'Registration number cannot exceed 50 characters']
    },
    registrationDate: {
      type: Date,
      required: [true, 'Registration date is required']
    },
    taxId: {
      type: String,
      trim: true,
      maxlength: [50, 'Tax ID cannot exceed 50 characters']
    },
    licenseNumber: {
      type: String,
      trim: true,
      maxlength: [50, 'License number cannot exceed 50 characters']
    },
    licenseExpiryDate: {
      type: Date
    }
  },

  // Primary Contact Person
  primaryContact: {
    name: {
      type: String,
      required: [true, 'Primary contact name is required'],
      trim: true,
      maxlength: [100, 'Contact name cannot exceed 100 characters']
    },
    designation: {
      type: String,
      required: [true, 'Primary contact designation is required'],
      trim: true,
      maxlength: [100, 'Designation cannot exceed 100 characters']
    },
    email: {
      type: String,
      required: [true, 'Primary contact email is required'],
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address'
      ]
    },
    phone: {
      type: String,
      required: [true, 'Primary contact phone is required'],
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number'
      ]
    }
  },

  // Status and Metadata
  status: {
    type: String,
    required: [true, 'Organization status is required'],
    enum: {
      values: ['active', 'inactive', 'suspended', 'pending'],
      message: 'Status must be one of: active, inactive, suspended, pending'
    },
    default: 'pending'
  },
  
  isVerified: {
    type: Boolean,
    default: false
  },
  
  verificationDate: {
    type: Date,
    default: null
  },
  
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  // Operational Information
  operationalAreas: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    coordinates: {
      type: {
        type: String,
        enum: ['Polygon'],
        required: true
      },
      coordinates: {
        type: [[[Number]]], // Array of arrays of arrays of numbers
        required: true
      }
    }
  }],

  // Subscription and Limits
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'standard', 'premium', 'enterprise'],
      default: 'basic'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: {
      type: Date
    },
    maxDrones: {
      type: Number,
      default: 5,
      min: [1, 'Maximum drones must be at least 1']
    },
    maxUsers: {
      type: Number,
      default: 3,
      min: [1, 'Maximum users must be at least 1']
    }
  },

  // Drone Allocation
  allocatedDrones: {
    type: Number,
    default: 0,
    min: [0, 'Allocated drones cannot be negative'],
    max: [1000, 'Allocated drones cannot exceed 1000']
  },

  // Statistics
  stats: {
    totalDrones: {
      type: Number,
      default: 0,
      min: [0, 'Total drones cannot be negative']
    },
    activeDrones: {
      type: Number,
      default: 0,
      min: [0, 'Active drones cannot be negative']
    },
    totalUsers: {
      type: Number,
      default: 0,
      min: [0, 'Total users cannot be negative']
    },
    totalFlightHours: {
      type: Number,
      default: 0,
      min: [0, 'Total flight hours cannot be negative']
    },
    lastActivity: {
      type: Date,
      default: null
    }
  },

  // Additional Settings
  settings: {
    timezone: {
      type: String,
      default: 'UTC'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: true
      }
    },
    dataRetention: {
      type: Number,
      default: 365, // days
      min: [30, 'Data retention must be at least 30 days']
    }
  },

  // Soft Delete Fields
  isDeleted: {
    type: Boolean,
    default: false
  },

  deletedAt: {
    type: Date,
    default: null
  },

  deletedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  permanentDeleteAt: {
    type: Date,
    default: null
  },

  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Created by user is required']
  },

  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  notes: [{
    content: {
      type: String,
      required: true,
      trim: true,
      maxlength: [1000, 'Note content cannot exceed 1000 characters']
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['general', 'verification', 'compliance', 'support'],
      default: 'general'
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
organizationSchema.index({ name: 1 });
organizationSchema.index({ 'registration.registrationNumber': 1 });
organizationSchema.index({ status: 1 });
organizationSchema.index({ type: 1 });
organizationSchema.index({ 'contact.primaryEmail': 1 });
organizationSchema.index({ createdBy: 1 });
organizationSchema.index({ 'address.coordinates': '2dsphere' });
organizationSchema.index({ isDeleted: 1 });
organizationSchema.index({ deletedAt: 1 });
organizationSchema.index({ permanentDeleteAt: 1 });

// Virtual for full address
organizationSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  return `${addr.street}, ${addr.city}, ${addr.state}, ${addr.country} ${addr.postalCode}`;
});

// Virtual for checking if subscription is active
organizationSchema.virtual('isSubscriptionActive').get(function() {
  if (!this.subscription.endDate) return true;
  return this.subscription.endDate > new Date();
});

// Virtual for delete timer (time remaining until permanent deletion)
organizationSchema.virtual('deleteTimer').get(function() {
  if (!this.isDeleted || !this.permanentDeleteAt) return null;
  const now = new Date();
  const timeRemaining = this.permanentDeleteAt.getTime() - now.getTime();
  return timeRemaining > 0 ? timeRemaining : 0;
});

// Virtual for days remaining until permanent deletion
organizationSchema.virtual('daysUntilPermanentDelete').get(function() {
  if (!this.isDeleted || !this.permanentDeleteAt) return null;
  const now = new Date();
  const timeRemaining = this.permanentDeleteAt.getTime() - now.getTime();
  const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));
  return daysRemaining > 0 ? daysRemaining : 0;
});

// Virtual for hours remaining until permanent deletion
organizationSchema.virtual('hoursUntilPermanentDelete').get(function() {
  if (!this.isDeleted || !this.permanentDeleteAt) return null;
  const now = new Date();
  const timeRemaining = this.permanentDeleteAt.getTime() - now.getTime();
  const hoursRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60));
  return hoursRemaining > 0 ? hoursRemaining : 0;
});

// Pre-save middleware to update stats
organizationSchema.pre('save', function(next) {
  if (this.isModified('lastModifiedBy')) {
    this.stats.lastActivity = new Date();
  }
  next();
});

// Static method to find organizations by location
organizationSchema.statics.findByLocation = function(coordinates, maxDistance = 10000) {
  return this.find({
    'address.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: coordinates
        },
        $maxDistance: maxDistance
      }
    }
  });
};

// Static method to get organization statistics
organizationSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      // Only include non-deleted organizations
      $match: { isDeleted: false }
    },
    {
      $group: {
        _id: null,
        totalOrganizations: { $sum: 1 },
        activeOrganizations: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        pendingOrganizations: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        verifiedOrganizations: {
          $sum: { $cond: ['$isVerified', 1, 0] }
        },
        totalDrones: { $sum: '$stats.totalDrones' },
        totalAllocatedDrones: { $sum: { $ifNull: ['$allocatedDrones', 0] } },
        totalUsers: { $sum: '$stats.totalUsers' },
        totalFlightHours: { $sum: '$stats.totalFlightHours' }
      }
    }
  ]);

  return stats[0] || {
    totalOrganizations: 0,
    activeOrganizations: 0,
    pendingOrganizations: 0,
    verifiedOrganizations: 0,
    totalDrones: 0,
    totalAllocatedDrones: 0,
    totalUsers: 0,
    totalFlightHours: 0
  };
};

// Instance method to add note
organizationSchema.methods.addNote = function(content, addedBy, type = 'general') {
  this.notes.push({
    content,
    addedBy,
    type,
    addedAt: new Date()
  });
  return this.save();
};

// Instance method to update statistics
organizationSchema.methods.updateStats = async function(statsUpdate) {
  Object.assign(this.stats, statsUpdate);
  this.stats.lastActivity = new Date();
  return this.save();
};

// Instance method to soft delete organization
organizationSchema.methods.softDelete = function(deletedBy) {
  const now = new Date();
  const permanentDeleteDate = new Date(now.getTime() + (2 * 24 * 60 * 60 * 1000)); // 48 hours (2 days) from now

  this.isDeleted = true;
  this.deletedAt = now;
  this.deletedBy = deletedBy;
  this.permanentDeleteAt = permanentDeleteDate;
  this.status = 'inactive';
  this.lastModifiedBy = deletedBy;

  return this.save();
};

// Instance method to restore organization
organizationSchema.methods.restore = function(restoredBy) {
  this.isDeleted = false;
  this.deletedAt = null;
  this.deletedBy = null;
  this.permanentDeleteAt = null;
  this.status = 'active';
  this.lastModifiedBy = restoredBy;

  return this.save();
};

module.exports = mongoose.model('Organization', organizationSchema);
