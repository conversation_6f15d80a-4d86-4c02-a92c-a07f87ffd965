import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Legend,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "recharts";
import { MapPin, TrendingUp, Users, BarChart3 } from "lucide-react";

// Enhanced dataset with more details
const initialData = [
  {
    name: "Maharashtra",
    value: 45,
    drones: 28,
    area: 1250,
    efficiency: 92,
    color: "#3B82F6"
  },
  {
    name: "Gujarat",
    value: 30,
    drones: 18,
    area: 850,
    efficiency: 88,
    color: "#10B981"
  },
  {
    name: "Haryana",
    value: 10,
    drones: 8,
    area: 320,
    efficiency: 85,
    color: "#F59E0B"
  },
  {
    name: "Telangana",
    value: 10,
    drones: 6,
    area: 280,
    efficiency: 90,
    color: "#EF4444"
  },
  {
    name: "Karnataka",
    value: 5,
    drones: 4,
    area: 150,
    efficiency: 87,
    color: "#8B5CF6"
  },
];

const COLORS = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"];

const DonutChart = () => {
  const [data, setData] = useState(initialData);
  const [selectedSegment, setSelectedSegment] = useState(null);
  const [viewMode, setViewMode] = useState('percentage');

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      const updated = data.map((item) => ({
        ...item,
        value: Math.max(5, Math.floor(item.value + (Math.random() * 6 - 3))),
        efficiency: Math.max(75, Math.min(100, item.efficiency + (Math.random() * 4 - 2))),
        area: Math.max(100, Math.floor(item.area + (Math.random() * 100 - 50)))
      }));
      setData(updated);
    }, 7000);

    return () => clearInterval(interval);
  }, [data]);

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg min-w-[200px]">
          <div className="flex items-center gap-2 mb-3">
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: data.color }}
            ></div>
            <h3 className="font-semibold text-gray-800">{data.name}</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Coverage:</span>
              <span className="font-medium">{data.value}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Drones:</span>
              <span className="font-medium">{data.drones}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Area:</span>
              <span className="font-medium">{data.area} ha</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Efficiency:</span>
              <span className="font-medium text-green-600">{data.efficiency}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => {
    return (
      <div className="space-y-2">
        {payload.map((entry, index) => {
          const item = data[index];
          return (
            <div
              key={index}
              className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors ${
                selectedSegment === index ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedSegment(selectedSegment === index ? null : index)}
            >
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-sm font-medium text-gray-700">{entry.value}</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-800">
                  {viewMode === 'percentage' ? `${item.value}%` :
                   viewMode === 'drones' ? `${item.drones} drones` :
                   `${item.area} ha`}
                </div>
                <div className="text-xs text-gray-500">
                  {item.efficiency}% efficiency
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const totalArea = data.reduce((sum, item) => sum + item.area, 0);
  const totalDrones = data.reduce((sum, item) => sum + item.drones, 0);
  const avgEfficiency = Math.round(data.reduce((sum, item) => sum + item.efficiency, 0) / data.length);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-3 gap-2">
        <div className="flex-1">
          <h2 className="text-base sm:text-lg font-bold text-gray-800 mb-1">Regional Distribution</h2>
          <p className="text-xs sm:text-sm text-gray-600">Coverage across states</p>
        </div>

        <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setViewMode('percentage')}
            className={`px-1.5 py-1 text-xs font-medium rounded-md transition-colors ${
              viewMode === 'percentage' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
            }`}
          >
            %
          </button>
          <button
            onClick={() => setViewMode('drones')}
            className={`px-1.5 py-1 text-xs font-medium rounded-md transition-colors ${
              viewMode === 'drones' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
            }`}
          >
            <Users size={10} />
          </button>
          <button
            onClick={() => setViewMode('area')}
            className={`px-1.5 py-1 text-xs font-medium rounded-md transition-colors ${
              viewMode === 'area' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
            }`}
          >
            <MapPin size={10} />
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-2 mb-3">
        <div className="text-center p-1.5 bg-blue-50 rounded-lg">
          <div className="text-sm sm:text-base font-bold text-blue-600">{totalDrones}</div>
          <div className="text-xs text-gray-600">Drones</div>
        </div>
        <div className="text-center p-1.5 bg-green-50 rounded-lg">
          <div className="text-sm sm:text-base font-bold text-green-600">{totalArea.toLocaleString()}</div>
          <div className="text-xs text-gray-600">Area (ha)</div>
        </div>
        <div className="text-center p-1.5 bg-purple-50 rounded-lg">
          <div className="text-sm sm:text-base font-bold text-purple-600">{avgEfficiency}%</div>
          <div className="text-xs text-gray-600">Efficiency</div>
        </div>
      </div>

      {/* Chart and Legend - Responsive Layout */}
      <div className="flex-1 flex flex-col lg:flex-row items-center min-h-0">
        {/* Chart Container */}
        <div className="w-full lg:w-1/2 h-40 sm:h-48 lg:h-full flex items-center justify-center">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={30}
                outerRadius={60}
                paddingAngle={2}
                dataKey="value"
                onMouseEnter={(_, index) => setSelectedSegment(index)}
                onMouseLeave={() => setSelectedSegment(null)}
              >
                {data.map((entry, index) => (
                  <Cell
                    key={index}
                    fill={entry.color}
                    stroke={selectedSegment === index ? '#374151' : 'none'}
                    strokeWidth={selectedSegment === index ? 2 : 0}
                    style={{
                      filter: selectedSegment === index ? 'brightness(1.1)' : 'none',
                      transform: selectedSegment === index ? 'scale(1.05)' : 'scale(1)',
                      transformOrigin: 'center',
                      transition: 'all 0.2s ease'
                    }}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Legend Container */}
        <div className="w-full lg:w-1/2 lg:pl-3 mt-2 lg:mt-0 flex-1 min-h-0">
          <div className="h-full overflow-y-auto custom-scrollbar">
            <CustomLegend payload={data.map((item) => ({
              value: item.name,
              color: item.color
            }))} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DonutChart;
