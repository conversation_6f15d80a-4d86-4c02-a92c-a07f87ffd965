require('dotenv').config();
const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Simple authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// In-memory storage
let drones = [];
let organizations = [];
let users = [];
let droneIdCounter = 1;

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  console.log('Login attempt:', { email, password });
  
  // Accept any credentials for testing
  const token = jwt.sign(
    { 
      _id: 'test-admin-id',
      email: email,
      role: 'admin',
      profile: {
        organizationId: 'test-org-id'
      }
    },
    process.env.JWT_SECRET || 'default_secret',
    { expiresIn: '7d' }
  );
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      token,
      user: {
        _id: 'test-admin-id',
        email: email,
        role: 'admin',
        profile: {
          organizationId: 'test-org-id'
        }
      }
    }
  });
});

// Drone endpoints
app.post('/api/drones', authenticateToken, (req, res) => {
  try {
    console.log('📥 Creating drone with data:', JSON.stringify(req.body, null, 2));
    
    const droneData = req.body;
    
    // Basic validation
    if (!droneData.name || !droneData.serialNumber) {
      return res.status(400).json({
        success: false,
        message: 'Name and serial number are required'
      });
    }
    
    // Check for duplicate serial number
    const existingDrone = drones.find(d => d.serialNumber === droneData.serialNumber);
    if (existingDrone) {
      return res.status(409).json({
        success: false,
        message: 'Drone with this serial number already exists'
      });
    }
    
    // Create drone
    const newDrone = {
      _id: `drone-${droneIdCounter++}`,
      ...droneData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: req.user._id
    };
    
    drones.push(newDrone);
    
    console.log('✅ Drone created successfully:', newDrone._id);
    
    res.status(201).json({
      success: true,
      message: 'Drone created successfully',
      data: newDrone
    });
    
  } catch (error) {
    console.error('❌ Error creating drone:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

app.get('/api/drones', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Drones retrieved successfully',
    data: {
      drones: drones,
      totalCount: drones.length,
      currentPage: 1,
      totalPages: 1
    }
  });
});

// Organization endpoints (basic)
app.get('/api/organizations', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: organizations,
    total: organizations.length
  });
});

// Catch all other routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Simple server started successfully!');
  console.log(`📍 Server running on: http://localhost:${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log('🔑 Any email/password combination will work for login');
  console.log('📝 Using in-memory storage (data will not persist)');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔒 Shutting down server gracefully...');
  process.exit(0);
});
