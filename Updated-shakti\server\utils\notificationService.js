const Notification = require('../models/Notification');

class NotificationService {
  /**
   * Create a notification for admin dashboard
   * @param {Object} data - Notification data
   * @param {string} data.type - Notification type
   * @param {string} data.title - Notification title
   * @param {string} data.message - Notification message
   * @param {Object} data.relatedEntity - Related entity information
   * @param {Object} data.triggeredBy - User who triggered the action
   * @param {Object} data.metadata - Additional metadata
   * @param {string} data.priority - Notification priority (optional)
   * @param {string} data.category - Notification category (optional)
   */
  static async createNotification(data) {
    try {
      const notification = await Notification.createNotification(data);
      console.log(`✅ Notification created: ${notification.type} - ${notification.title}`);
      return notification;
    } catch (error) {
      console.error('❌ Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Create organization-related notifications
   */
  static async createOrganizationNotification(action, organization, user = null, previousValues = null) {
    const typeMap = {
      'created': 'organization_created',
      'updated': 'organization_updated',
      'deleted': 'organization_deleted',
      'restored': 'organization_restored'
    };

    const titleMap = {
      'created': `New Organization Created: ${organization.name}`,
      'updated': `Organization Updated: ${organization.name}`,
      'deleted': `Organization Deleted: ${organization.name}`,
      'restored': `Organization Restored: ${organization.name}`
    };

    const messageMap = {
      'created': `A new organization "${organization.name}" has been created with ${organization.stats?.totalDrones || 0} drones allocated.`,
      'updated': `Organization "${organization.name}" has been updated. Changes may include contact information, status, or drone allocation.`,
      'deleted': `Organization "${organization.name}" has been moved to trash and will be permanently deleted in 48 hours.`,
      'restored': `Organization "${organization.name}" has been restored from trash and is now active again.`
    };

    const priorityMap = {
      'created': 'medium',
      'updated': 'low',
      'deleted': 'high',
      'restored': 'medium'
    };

    const notificationData = {
      type: typeMap[action],
      title: titleMap[action],
      message: messageMap[action],
      category: 'admin',
      priority: priorityMap[action],
      relatedEntity: {
        entityType: 'organization',
        entityId: organization._id,
        entityName: organization.name
      },
      metadata: {
        action,
        previousValues,
        newValues: {
          name: organization.name,
          status: organization.status,
          totalDrones: organization.stats?.totalDrones,
          contactEmail: organization.contactInfo?.email
        }
      }
    };

    if (user) {
      notificationData.triggeredBy = {
        userId: user._id,
        username: user.username,
        role: user.role
      };
    }

    return await this.createNotification(notificationData);
  }

  /**
   * Create drone-related notifications
   */
  static async createDroneNotification(action, drone, user = null, previousValues = null) {
    const droneIdentifier = `${drone.name} (${drone.serialNumber})`;

    const typeMap = {
      'created': 'drone_created',
      'updated': 'drone_updated',
      'deleted': 'drone_deleted',
      'status_changed': 'drone_status_changed'
    };

    const titleMap = {
      'created': `New Drone Added: ${droneIdentifier}`,
      'updated': `Drone Updated: ${droneIdentifier}`,
      'deleted': `Drone Removed: ${droneIdentifier}`,
      'status_changed': `Drone Status Changed: ${droneIdentifier}`
    };

    const messageMap = {
      'created': `A new drone "${droneIdentifier}" (${drone.model}) has been added to the system and assigned to ${drone.organization?.name || 'Unknown Organization'}.`,
      'updated': `Drone "${droneIdentifier}" has been updated. Changes may include specifications, status, or assignment.`,
      'deleted': `Drone "${droneIdentifier}" has been removed from the system.`,
      'status_changed': `Drone "${droneIdentifier}" status changed from ${previousValues?.status || 'Unknown'} to ${drone.status}.`
    };

    const priorityMap = {
      'created': 'medium',
      'updated': 'low',
      'deleted': 'medium',
      'status_changed': 'medium'
    };

    const notificationData = {
      type: typeMap[action],
      title: titleMap[action],
      message: messageMap[action],
      category: 'operations',
      priority: priorityMap[action],
      relatedEntity: {
        entityType: 'drone',
        entityId: drone._id,
        entityName: droneIdentifier
      },
      metadata: {
        action,
        previousValues,
        newValues: {
          name: drone.name,
          serialNumber: drone.serialNumber,
          model: drone.model,
          status: drone.status,
          organization: drone.organization?.name,
          batteryLevel: drone.batteryLevel
        }
      }
    };

    if (user) {
      notificationData.triggeredBy = {
        userId: user._id,
        username: user.username,
        role: user.role
      };
    }

    return await this.createNotification(notificationData);
  }

  /**
   * Create individual-related notifications
   */
  static async createIndividualNotification(action, individual, user = null, previousValues = null) {
    const individualName = `${individual.personalInfo.firstName} ${individual.personalInfo.lastName}`;

    const typeMap = {
      'created': 'individual_created',
      'updated': 'individual_updated',
      'deleted': 'individual_deleted'
    };

    const titleMap = {
      'created': `New Individual Registered: ${individualName}`,
      'updated': `Individual Updated: ${individualName}`,
      'deleted': `Individual Removed: ${individualName}`
    };

    const messageMap = {
      'created': `A new individual "${individualName}" has been registered in the system with ${individual.dronesAllocated || 0} drones allocated.`,
      'updated': `Individual "${individualName}" profile has been updated. Changes may include contact information, status, or drone allocation.`,
      'deleted': `Individual "${individualName}" has been removed from the system.`
    };

    const priorityMap = {
      'created': 'medium',
      'updated': 'low',
      'deleted': 'medium'
    };

    const notificationData = {
      type: typeMap[action],
      title: titleMap[action],
      message: messageMap[action],
      category: 'user_management',
      priority: priorityMap[action],
      relatedEntity: {
        entityType: 'individual',
        entityId: individual._id,
        entityName: individualName
      },
      metadata: {
        action,
        previousValues,
        newValues: {
          name: individualName,
          email: individual.contact?.primaryEmail,
          phone: individual.contact?.primaryPhone,
          dronesAllocated: individual.dronesAllocated,
          status: individual.status
        }
      }
    };

    if (user) {
      notificationData.triggeredBy = {
        userId: user._id,
        username: user.username,
        role: user.role
      };
    }

    return await this.createNotification(notificationData);
  }

  /**
   * Create user-related notifications
   */
  static async createUserNotification(action, targetUser, user = null) {
    const typeMap = {
      'created': 'user_created',
      'updated': 'user_updated'
    };

    const titleMap = {
      'created': `New User Created: ${targetUser.username}`,
      'updated': `User Updated: ${targetUser.username}`
    };

    const messageMap = {
      'created': `A new user "${targetUser.username}" with role "${targetUser.role}" has been created in the system.`,
      'updated': `User "${targetUser.username}" profile has been updated.`
    };

    const notificationData = {
      type: typeMap[action],
      title: titleMap[action],
      message: messageMap[action],
      category: 'user_management',
      priority: 'medium',
      relatedEntity: {
        entityType: 'user',
        entityId: targetUser._id,
        entityName: targetUser.username
      },
      metadata: {
        action,
        newValues: {
          username: targetUser.username,
          email: targetUser.email,
          role: targetUser.role
        }
      }
    };

    if (user) {
      notificationData.triggeredBy = {
        userId: user._id,
        username: user.username,
        role: user.role
      };
    }

    return await this.createNotification(notificationData);
  }

  /**
   * Create system alert notifications
   */
  static async createSystemAlert(title, message, priority = 'medium', metadata = {}) {
    const notificationData = {
      type: 'system_alert',
      title,
      message,
      category: 'system',
      priority,
      relatedEntity: {
        entityType: 'system',
        entityId: new Date().getTime().toString(), // Use timestamp as ID for system alerts
        entityName: 'SHAKTI System'
      },
      metadata: {
        action: 'system_alert',
        ...metadata
      }
    };

    return await this.createNotification(notificationData);
  }
}

module.exports = NotificationService;
