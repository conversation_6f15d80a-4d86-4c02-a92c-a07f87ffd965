import React, { useState, useEffect } from 'react'
import { FaCogs, FaCheckCircle, FaWrench, FaBuilding } from 'react-icons/fa'
import { TrendingUp, TrendingDown, Activity, BarChart3 } from 'lucide-react'

const initialStats = [
  {
    label: 'Total Drones',
    value: 128,
    target: 150,
    change: '+12%',
    changeValue: 12,
    bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
    text: 'text-blue-700',
    circle: 'bg-gradient-to-br from-blue-500 to-blue-600',
    icon: <FaCogs className="text-white text-xl" />,
    trend: 'up',
    description: 'Fleet size increased'
  },
  {
    label: 'Active Drones',
    value: 94,
    target: 128,
    change: '+8%',
    changeValue: 8,
    bg: 'bg-gradient-to-br from-green-50 to-green-100',
    text: 'text-green-700',
    circle: 'bg-gradient-to-br from-green-500 to-green-600',
    icon: <FaCheckCircle className="text-white text-xl" />,
    trend: 'up',
    description: 'Currently operational'
  },
  {
    label: 'In Maintenance',
    value: 18,
    target: 25,
    change: '-5%',
    changeValue: -5,
    bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
    text: 'text-yellow-700',
    circle: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
    icon: <FaWrench className="text-white text-xl" />,
    trend: 'down',
    description: 'Scheduled maintenance'
  },
  {
    label: 'Organizations',
    value: 16,
    target: 20,
    change: '+2',
    changeValue: 2,
    bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
    text: 'text-purple-700',
    circle: 'bg-gradient-to-br from-purple-500 to-purple-600',
    icon: <FaBuilding className="text-white text-xl" />,
    trend: 'up',
    description: 'Partner organizations'
  }
]

const DroneStats = () => {
  const [stats, setStats] = useState(initialStats)
  const [animatedValues, setAnimatedValues] = useState(initialStats.map(() => 0))

  // Animate counter values on mount
  useEffect(() => {
    const timers = stats.map((stat, index) => {
      let current = 0
      const increment = stat.value / 50 // 50 steps for smooth animation

      return setInterval(() => {
        current += increment
        if (current >= stat.value) {
          current = stat.value
          clearInterval(timers[index])
        }

        setAnimatedValues(prev => {
          const newValues = [...prev]
          newValues[index] = Math.floor(current)
          return newValues
        })
      }, 30)
    })

    // Simulate real-time updates
    const updateInterval = setInterval(() => {
      setStats(prevStats =>
        prevStats.map(stat => ({
          ...stat,
          value: Math.max(0, stat.value + Math.floor(Math.random() * 6 - 3))
        }))
      )
    }, 10000)

    return () => {
      timers.forEach(timer => clearInterval(timer))
      clearInterval(updateInterval)
    }
  }, [])

  const AnimatedCounter = ({ value, duration = 2000 }) => {
    const [count, setCount] = useState(0)

    useEffect(() => {
      let start = 0
      const end = value
      const incrementTime = duration / end

      const timer = setInterval(() => {
        start += 1
        setCount(start)
        if (start === end) clearInterval(timer)
      }, incrementTime)

      return () => clearInterval(timer)
    }, [value, duration])

    return count
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, idx) => (
        <div
          key={idx}
          className={`${stat.bg} rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 group cursor-pointer`}
        >
          {/* Header with Icon and Trend */}
          <div className="flex items-center justify-between mb-4">
            <div className={`w-14 h-14 flex items-center justify-center rounded-xl ${stat.circle} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
              {stat.icon}
            </div>

            <div className="flex items-center gap-1">
              {stat.trend === 'up' ? (
                <TrendingUp size={16} className="text-green-600" />
              ) : (
                <TrendingDown size={16} className="text-red-600" />
              )}
              <span className={`text-sm font-semibold ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change}
              </span>
            </div>
          </div>

          {/* Main Stats */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-600 mb-2">{stat.label}</h4>
            <div className={`text-3xl font-bold ${stat.text} mb-1`}>
              <AnimatedCounter value={stat.value} />
            </div>
            <p className="text-xs text-gray-500">{stat.description}</p>
          </div>

          {/* Progress Bar */}
          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{Math.round((stat.value / stat.target) * 100)}%</span>
            </div>
            <div className="w-full bg-white/30 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-1000 ease-out ${
                  stat.trend === 'up' ? 'bg-green-500' : 'bg-yellow-500'
                }`}
                style={{
                  width: `${Math.min(100, (stat.value / stat.target) * 100)}%`,
                  transition: 'width 2s ease-out'
                }}
              ></div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Target: {stat.target}</span>
            <div className="flex items-center gap-1">
              <Activity size={12} />
              <span>Live</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default DroneStats
