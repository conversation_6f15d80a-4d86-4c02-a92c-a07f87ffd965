const User = require('../models/User');
const { verifyToken, extractTokenFromHeader } = require('../utils/jwt');
const { sendAuthError, sendForbiddenError } = require('../utils/response');

/**
 * Authentication middleware - Verify JWT token and attach user to request
 */
const authenticate = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return sendAuthError(res, 'Access token is required');
    }

    // Verify token
    const decoded = verifyToken(token);
    
    // Find user in database
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return sendAuthError(res, 'User not found');
    }

    if (!user.isActive) {
      return sendAuthError(res, 'Account is deactivated');
    }

    // Attach user to request object
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error.message);
    return sendAuthError(res, error.message);
  }
};

/**
 * Authorization middleware - Check if user has required role
 * @param {Array} allowedRoles - Array of allowed roles
 */
const authorize = (allowedRoles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return sendAuthError(res, 'Authentication required');
    }

    if (allowedRoles.length === 0) {
      return next(); // No role restriction
    }

    if (!allowedRoles.includes(req.user.role)) {
      return sendForbiddenError(res, 'Insufficient permissions');
    }

    next();
  };
};

/**
 * Optional authentication middleware - Attach user if token is provided
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && user.isActive) {
        req.user = user;
        req.token = token;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

/**
 * Admin only middleware
 */
const adminOnly = authorize(['admin']);

/**
 * Organization only middleware
 */
const orgOnly = authorize(['org']);

/**
 * Maintenance/QC only middleware
 */
const maintenanceOnly = authorize(['maintenance']);

/**
 * Admin or Organization middleware
 */
const adminOrOrg = authorize(['admin', 'org']);

/**
 * All authenticated users middleware
 */
const allRoles = authorize(['admin', 'org', 'maintenance']);

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  adminOnly,
  orgOnly,
  maintenanceOnly,
  adminOrOrg,
  allRoles
};
