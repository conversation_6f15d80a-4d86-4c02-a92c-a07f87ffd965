const OrganizationData = require('../models/OrganizationData');
const Organization = require('../models/Organization');

class OrganizationDroneService {
  /**
   * Generate unique drone ID
   */
  static generateDroneId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `DRN-${new Date().getFullYear()}-${random}-${timestamp.toString().slice(-6)}`;
  }

  /**
   * Validate drone data
   */
  static validateDroneData(droneData) {
    const errors = [];

    // Required fields
    if (!droneData.name || droneData.name.trim().length < 2) {
      errors.push('Drone name is required and must be at least 2 characters');
    }

    if (!droneData.model || droneData.model.trim().length < 2) {
      errors.push('Model is required and must be at least 2 characters');
    }

    if (!droneData.manufacturer || droneData.manufacturer.trim().length < 2) {
      errors.push('Manufacturer is required and must be at least 2 characters');
    }

    if (!droneData.serialNumber || droneData.serialNumber.trim().length < 5) {
      errors.push('Serial number is required and must be at least 5 characters');
    }

    if (!droneData.registrationNumber || droneData.registrationNumber.trim().length < 5) {
      errors.push('Registration number is required and must be at least 5 characters');
    }

    // Purchase information
    if (!droneData.purchase) {
      errors.push('Purchase information is required');
    } else {
      if (!droneData.purchase.purchaseDate) {
        errors.push('Purchase date is required');
      }
      if (!droneData.purchase.purchasePrice || droneData.purchase.purchasePrice < 0) {
        errors.push('Purchase price is required and must be positive');
      }
      if (!droneData.purchase.vendor || droneData.purchase.vendor.trim().length < 2) {
        errors.push('Vendor is required and must be at least 2 characters');
      }
    }

    return errors;
  }

  /**
   * Create a new drone for organization
   */
  static async createDrone(organizationId, droneData, createdBy) {
    try {
      // Validate drone data
      const validationErrors = this.validateDroneData(droneData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Get or create organization data
      let orgData = await OrganizationData.findOne({ organizationId });
      
      if (!orgData) {
        const organization = await Organization.findById(organizationId);
        if (!organization) {
          throw new Error('Organization not found');
        }

        orgData = await OrganizationData.createForOrganization(organizationId, {
          name: organization.name,
          displayName: organization.displayName,
          type: organization.type,
          status: organization.status
        });
      }

      // Check for duplicates
      const existingSerial = orgData.drones.find(d => d.serialNumber === droneData.serialNumber);
      if (existingSerial) {
        throw new Error('Drone with this serial number already exists');
      }

      const existingReg = orgData.drones.find(d => d.registrationNumber === droneData.registrationNumber);
      if (existingReg) {
        throw new Error('Drone with this registration number already exists');
      }

      // Prepare drone data
      const newDroneData = {
        droneId: this.generateDroneId(),
        name: droneData.name.trim(),
        model: droneData.model.trim(),
        manufacturer: droneData.manufacturer.trim(),
        serialNumber: droneData.serialNumber.trim(),
        registrationNumber: droneData.registrationNumber.trim(),
        specifications: {
          type: droneData.specifications?.type || 'quadcopter',
          weight: droneData.specifications?.weight || 0,
          maxPayload: droneData.specifications?.maxPayload || 0,
          maxFlightTime: droneData.specifications?.maxFlightTime || 0,
          maxRange: droneData.specifications?.maxRange || 0,
          maxAltitude: droneData.specifications?.maxAltitude || 0,
          maxSpeed: droneData.specifications?.maxSpeed || 0,
          batteryCapacity: droneData.specifications?.batteryCapacity || 0,
          cameraResolution: droneData.specifications?.cameraResolution || '',
          hasGimbal: droneData.specifications?.hasGimbal || false,
          hasGPS: droneData.specifications?.hasGPS !== false,
          hasObstacleAvoidance: droneData.specifications?.hasObstacleAvoidance || false
        },
        status: droneData.status || 'active',
        condition: droneData.condition || 'excellent',
        currentLocation: {
          latitude: droneData.currentLocation?.latitude || 0,
          longitude: droneData.currentLocation?.longitude || 0,
          altitude: droneData.currentLocation?.altitude || 0,
          lastUpdated: new Date()
        },
        flightStats: {
          totalFlights: 0,
          totalFlightTime: 0,
          totalDistance: 0,
          averageFlightTime: 0,
          lastFlightDate: null
        },
        maintenance: {
          lastMaintenanceDate: droneData.maintenance?.lastMaintenanceDate || null,
          nextMaintenanceDate: droneData.maintenance?.nextMaintenanceDate || null,
          maintenanceIntervalHours: droneData.maintenance?.maintenanceIntervalHours || 50,
          maintenanceHistory: []
        },
        purchase: {
          purchaseDate: new Date(droneData.purchase.purchaseDate),
          purchasePrice: droneData.purchase.purchasePrice,
          vendor: droneData.purchase.vendor.trim(),
          warrantyExpiryDate: droneData.purchase.warrantyExpiryDate ? 
            new Date(droneData.purchase.warrantyExpiryDate) : null
        },
        assignedTo: droneData.assignedTo || null,
        notes: [],
        createdBy,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add drone to organization
      await orgData.addDrone(newDroneData, createdBy);

      // Return the newly created drone
      const newDrone = orgData.drones[orgData.drones.length - 1];
      return newDrone;

    } catch (error) {
      console.error('❌ Create drone error:', error);
      throw error;
    }
  }

  /**
   * Update drone information
   */
  static async updateDrone(organizationId, droneId, updateData) {
    try {
      const orgData = await OrganizationData.findOne({ organizationId });
      if (!orgData) {
        throw new Error('Organization data not found');
      }

      const drone = orgData.getDrone(droneId);
      if (!drone) {
        throw new Error('Drone not found');
      }

      // Check for duplicate serial number if being updated
      if (updateData.serialNumber && updateData.serialNumber !== drone.serialNumber) {
        const existingDrone = orgData.drones.find(d => 
          d.serialNumber === updateData.serialNumber && d._id.toString() !== droneId
        );
        if (existingDrone) {
          throw new Error('Drone with this serial number already exists');
        }
      }

      // Check for duplicate registration number if being updated
      if (updateData.registrationNumber && updateData.registrationNumber !== drone.registrationNumber) {
        const existingDrone = orgData.drones.find(d => 
          d.registrationNumber === updateData.registrationNumber && d._id.toString() !== droneId
        );
        if (existingDrone) {
          throw new Error('Drone with this registration number already exists');
        }
      }

      // Update drone
      await orgData.updateDrone(droneId, updateData);

      return orgData.getDrone(droneId);

    } catch (error) {
      console.error('❌ Update drone error:', error);
      throw error;
    }
  }

  /**
   * Delete drone
   */
  static async deleteDrone(organizationId, droneId) {
    try {
      const orgData = await OrganizationData.findOne({ organizationId });
      if (!orgData) {
        throw new Error('Organization data not found');
      }

      const drone = orgData.getDrone(droneId);
      if (!drone) {
        throw new Error('Drone not found');
      }

      await orgData.removeDrone(droneId);
      return true;

    } catch (error) {
      console.error('❌ Delete drone error:', error);
      throw error;
    }
  }

  /**
   * Get organization drone statistics
   */
  static async getDroneStatistics(organizationId) {
    try {
      const orgData = await OrganizationData.findOne({ organizationId });
      if (!orgData) {
        return {
          totalDrones: 0,
          activeDrones: 0,
          inactiveDrones: 0,
          maintenanceDrones: 0,
          totalFlightHours: 0,
          totalFlights: 0,
          totalDistance: 0,
          averageFlightTime: 0
        };
      }

      return orgData.statistics;

    } catch (error) {
      console.error('❌ Get drone statistics error:', error);
      throw error;
    }
  }

  /**
   * Update drone location
   */
  static async updateDroneLocation(organizationId, droneId, location) {
    try {
      const orgData = await OrganizationData.findOne({ organizationId });
      if (!orgData) {
        throw new Error('Organization data not found');
      }

      const drone = orgData.getDrone(droneId);
      if (!drone) {
        throw new Error('Drone not found');
      }

      drone.currentLocation = {
        latitude: location.latitude,
        longitude: location.longitude,
        altitude: location.altitude || 0,
        lastUpdated: new Date()
      };

      await orgData.save();
      return drone;

    } catch (error) {
      console.error('❌ Update drone location error:', error);
      throw error;
    }
  }

  /**
   * Add maintenance record
   */
  static async addMaintenanceRecord(organizationId, droneId, maintenanceData) {
    try {
      const orgData = await OrganizationData.findOne({ organizationId });
      if (!orgData) {
        throw new Error('Organization data not found');
      }

      const drone = orgData.getDrone(droneId);
      if (!drone) {
        throw new Error('Drone not found');
      }

      drone.maintenance.maintenanceHistory.push({
        date: new Date(maintenanceData.date),
        type: maintenanceData.type,
        description: maintenanceData.description,
        cost: maintenanceData.cost || 0,
        performedBy: maintenanceData.performedBy
      });

      // Update last maintenance date
      drone.maintenance.lastMaintenanceDate = new Date(maintenanceData.date);

      // Calculate next maintenance date
      if (drone.maintenance.maintenanceIntervalHours) {
        const nextMaintenanceMs = drone.maintenance.maintenanceIntervalHours * 60 * 60 * 1000;
        drone.maintenance.nextMaintenanceDate = new Date(Date.now() + nextMaintenanceMs);
      }

      await orgData.save();
      return drone;

    } catch (error) {
      console.error('❌ Add maintenance record error:', error);
      throw error;
    }
  }
}

module.exports = OrganizationDroneService;
