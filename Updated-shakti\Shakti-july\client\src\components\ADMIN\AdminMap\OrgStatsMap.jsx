import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import {
    Bell,
    Search,
    RefreshCw,
    Loader2,
    Filter,
    MapPin,
    Activity,
    AlertTriangle,
    Settings,
    Pause,
    Battery,
    Gauge,
    Clock,
    ArrowLeft,
    Maximize2,
    Building,
    CheckCircle
} from 'lucide-react';

import { useNavigate } from 'react-router-dom';
import AdminSidebar from "../common/AdminSidebar";
import mapDataService from './MapDataService';
import {
  OrganizationDetailsSkeleton,
  MapSkeleton,
  EmptyDroneList,
  MapOverlayLoading,
  ConnectionStatus,
  MapErrorDisplay
} from './MapLoadingComponents';

// Create dynamic drone icon based on status
const createDroneIcon = (status, battery = 100) => {
  const colors = {
    'ACTIVE': '#10b981',
    'FLYING': '#3b82f6',
    'INACTIVE': '#6b7280',
    'CRASHED': '#ef4444',
    'MAINTENANCE': '#f59e0b'
  };

  const color = colors[status] || '#6b7280';

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${color};
        width: 28px;
        height: 28px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        ${status === 'ACTIVE' || status === 'FLYING' ? 'animation: pulse 2s infinite;' : ''}
      ">
        <div style="
          width: 12px;
          height: 12px;
          background-color: white;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div style="
            width: 6px;
            height: 6px;
            background-color: ${color};
            border-radius: 1px;
          "></div>
        </div>
        ${battery < 20 ? `
          <div style="
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 10px;
            height: 10px;
            background-color: #ef4444;
            border: 2px solid white;
            border-radius: 50%;
          "></div>
        ` : ''}
      </div>
      <style>
        @keyframes pulse {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.7; transform: scale(1.1); }
        }
      </style>
    `,
    className: 'custom-drone-icon',
    iconSize: [28, 28],
    iconAnchor: [14, 14],
    popupAnchor: [0, -14],
  });
};

// Status Colors and Icons
const getStatusConfig = (status) => {
  const configs = {
    ACTIVE: {
      color: "text-green-600 bg-green-50",
      icon: <Activity className="w-4 h-4 text-green-600" />,
      bgColor: "bg-green-100"
    },
    FLYING: {
      color: "text-blue-600 bg-blue-50",
      icon: <MapPin className="w-4 h-4 text-blue-600" />,
      bgColor: "bg-blue-100"
    },
    INACTIVE: {
      color: "text-gray-600 bg-gray-50",
      icon: <Pause className="w-4 h-4 text-gray-600" />,
      bgColor: "bg-gray-100"
    },
    CRASHED: {
      color: "text-red-600 bg-red-50",
      icon: <AlertTriangle className="w-4 h-4 text-red-600" />,
      bgColor: "bg-red-100"
    },
    MAINTENANCE: {
      color: "text-amber-600 bg-amber-50",
      icon: <Settings className="w-4 h-4 text-amber-600" />,
      bgColor: "bg-amber-100"
    }
  };
  return configs[status] || configs.INACTIVE;
};


const OrgStatsMap = () => {
    const navigate = useNavigate();
    const [orgData, setOrgData] = useState(null);
    const [drones, setDrones] = useState([]);
    const [filter, setFilter] = useState("All");
    const [isLoading, setIsLoading] = useState(true);
    const [isUpdating, setIsUpdating] = useState(false);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [lastUpdate, setLastUpdate] = useState(null);
    const [selectedDrone, setSelectedDrone] = useState(null);

    // Initialize component and load data
    useEffect(() => {
        console.log('OrgStatsMap component mounted');

        document.body.style.overflow = "auto";
        document.body.style.display = "block";
        document.body.style.justifyContent = "unset";
        document.body.style.alignItems = "unset";
        document.body.style.height = "auto";
        document.body.style.background = "#f5f5f5";

        loadOrganizationData();

        // Subscribe to drone updates
        const unsubscribe = mapDataService.subscribe('drones-ORG-001', (updatedDrones) => {
            console.log('Received drone updates:', updatedDrones);
            setDrones(updatedDrones);
            setLastUpdate(new Date().toLocaleTimeString());
        });

        return () => {
            console.log('OrgStatsMap component unmounting');
            unsubscribe();
        };
    }, []);

    const loadOrganizationData = async () => {
        try {
            console.log('Loading organization data...');
            setIsLoading(true);
            setError(null);

            // Get organization data
            const org = mapDataService.getOrganizationById('ORG-001');
            console.log('Organization data:', org);
            setOrgData(org);

            // Get drones for this organization
            const orgDrones = await mapDataService.fetchOrganizationDrones('ORG-001');
            console.log('Drone data:', orgDrones);
            setDrones(orgDrones);
            setLastUpdate(new Date().toLocaleTimeString());
        } catch (err) {
            console.error('Error loading organization data:', err);
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefresh = async () => {
        setIsUpdating(true);
        try {
            console.log('Refreshing drone data...');
            await mapDataService.fetchOrganizationDrones('ORG-001');
        } catch (err) {
            console.error('Error refreshing drone data:', err);
            setError(err.message);
        } finally {
            setIsUpdating(false);
        }
    };

    // Filter drones based on status and search
    const filteredDrones = drones.filter(drone => {
        const matchesFilter = filter === "All" || drone.status === filter;
        const matchesSearch = !searchQuery ||
            drone.id.toLowerCase().includes(searchQuery.toLowerCase());
        return matchesFilter && matchesSearch;
    });

    // Get drone statistics
    const droneStats = {
        total: drones.length,
        active: drones.filter(d => d.status === 'ACTIVE').length,
        flying: drones.filter(d => d.status === 'FLYING').length,
        inactive: drones.filter(d => d.status === 'INACTIVE').length,
        crashed: drones.filter(d => d.status === 'CRASHED').length,
        maintenance: drones.filter(d => d.status === 'MAINTENANCE').length
    };

    if (error) {
        return (
            <div className="relative flex w-full h-screen">
                <div className="relative z-50">
                    <AdminSidebar />
                </div>
                <div className="lg:pl-[250px] w-full">
                    <MapErrorDisplay
                        title="Failed to load drone data"
                        message={error}
                        onRetry={loadOrganizationData}
                    />
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="relative flex w-full h-screen">
                <div className="relative z-50">
                    <AdminSidebar />
                </div>
                <div className="relative z-10 flex-1 flex flex-col lg:pl-[250px]">
                    <div className="h-16 bg-gradient-to-r from-[#91d0f5] to-[#7ab9e3] shadow-md"></div>
                    <div className="flex flex-1">
                        <OrganizationDetailsSkeleton />
                        <MapSkeleton />
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative flex w-full min-h-screen font-sans text-left bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9]">
            {/* Sidebar - Fixed positioning with higher z-index */}
            <div className="relative z-50">
                <AdminSidebar />
            </div>

            {/* Loading overlay */}
            {isUpdating && <MapOverlayLoading message="Updating drone positions..." />}

            {/* Main Content - Full width */}
            <div className="relative z-10 flex-1 flex flex-col lg:pl-[250px] transition-all duration-300 w-full min-h-screen pointer-events-auto">
                {/* Header - Full width */}
                <div className="relative z-20 flex flex-col lg:flex-row items-center justify-between px-4 sm:px-6 py-4 bg-gradient-to-r from-[#91d0f5] to-[#7ab9e3] shadow-md gap-4 mt-0 lg:mt-0 w-full">
                    <div className="flex items-center gap-4 w-full lg:w-auto mt-8 lg:mt-0">
                        <button
                            onClick={() => navigate('/map')}
                            className="flex items-center gap-2 px-3 py-2 bg-white/20 text-black rounded-lg hover:bg-white/30 transition-colors"
                        >
                            <ArrowLeft size={16} />
                            <span className="hidden sm:inline text-black">Back to Map</span>
                        </button>

                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-2.5 text-gray-600" size={18} />
                            <input
                                type="text"
                                placeholder="Search drones by ID..."
                                className="pl-10 pr-4 py-2 rounded-full border-none w-full focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all text-black"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                    </div>

                    <div className="flex items-center gap-3">
                        <button
                            onClick={handleRefresh}
                            disabled={isUpdating}
                            className="flex items-center justify-center bg-white text-blue-600 p-2 rounded-full shadow hover:bg-blue-50 transition-all disabled:opacity-50"
                            title="Refresh drone data"
                        >
                            {isUpdating ? <Loader2 size={18} className="animate-spin" /> : <RefreshCw size={18} />}
                        </button>
                        <button className="flex items-center gap-2 px-3 py-2 bg-white/20 text-black rounded-lg hover:bg-white/30 transition-colors">
                            <Maximize2 size={16} />
                            <span className="hidden sm:inline text-black">Fullscreen</span>
                        </button>
                        <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-blue-600 shadow hover:bg-blue-50 cursor-pointer transition-all">
                            <Bell size={20} />
                        </div>
                    </div>
                </div>

                {/* Stats Bar - Full width */}
                <div className="px-4 sm:px-6 py-3 bg-white/80 backdrop-blur-sm border-b border-white/20 w-full">
                    <div className="flex flex-wrap gap-4 text-sm">
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-black font-medium">Active: {droneStats.active}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-black font-medium">Flying: {droneStats.flying}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                            <span className="text-black font-medium">Inactive: {droneStats.inactive}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span className="text-black font-medium">Crashed: {droneStats.crashed}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                            <span className="text-black font-medium">Maintenance: {droneStats.maintenance}</span>
                        </div>
                    </div>
                </div>

                {/* Map and Sidebar Container */}
                <div className="flex flex-col sm:flex-row flex-1 relative">
                    {/* Background Map */}
                    <MapContainer
                        center={[19.853, 75.895]}
                        zoom={15}
                        scrollWheelZoom={true}
                        className="absolute top-0 left-0 w-full h-full z-0 order-1 sm:order-2"
                        style={{ height: "100%", width: "100%" }}
                    >
                        <TileLayer
                            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        />
                        {filteredDrones && filteredDrones.length > 0 && filteredDrones.map((drone) => (
                            <Marker
                                key={drone.id}
                                position={[drone.lat, drone.lng]}
                                icon={createDroneIcon(drone.status, drone.battery)}
                                eventHandlers={{
                                    click: () => {
                                        console.log('Drone clicked:', drone);
                                        setSelectedDrone(drone);
                                    }
                                }}
                            >
                                <Popup className="custom-popup">
                                    <div className="p-2 min-w-[200px]">
                                        <div className="flex items-center gap-2 mb-3">
                                            {getStatusConfig(drone.status).icon}
                                            <h3 className="font-bold text-gray-800">{drone.id}</h3>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusConfig(drone.status).color}`}>
                                                {drone.status}
                                            </span>
                                        </div>

                                        <div className="space-y-2 text-sm">
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600 flex items-center gap-1">
                                                    <Battery className="w-3 h-3" />
                                                    Battery:
                                                </span>
                                                <span className={`font-medium ${drone.battery < 20 ? 'text-red-600' : 'text-green-600'}`}>
                                                    {drone.battery}%
                                                </span>
                                            </div>
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600 flex items-center gap-1">
                                                    <Gauge className="w-3 h-3" />
                                                    Altitude:
                                                </span>
                                                <span className="font-medium">{drone.altitude}m</span>
                                            </div>
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600 flex items-center gap-1">
                                                    <Clock className="w-3 h-3" />
                                                    Last Update:
                                                </span>
                                                <span className="text-blue-600">{drone.lastUpdate}</span>
                                            </div>
                                        </div>
                                    </div>
                                </Popup>
                            </Marker>
                        ))}
                    </MapContainer>

                    {/* Organization Details Sidebar */}
                    <div className="w-full sm:w-1/3 lg:w-1/4 h-1/3 sm:h-full bg-white/90 backdrop-blur-md p-3 sm:p-4 overflow-y-auto shadow-md relative z-10 order-2 sm:order-1">
                        <div className="mb-3 sm:mb-4">
                            <h2 className="text-lg sm:text-xl font-bold text-black mb-1">{orgData?.name}</h2>
                            <p className="text-xs sm:text-sm text-black truncate">{orgData?.orgId}</p>
                            <div className="flex items-center text-xs sm:text-sm text-black mt-1 mb-2">
                                <MapPin className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                <span className="truncate">{orgData?.state}, {orgData?.district}</span>
                            </div>
                            <p className="text-sm sm:text-base text-black mb-2 sm:mb-3">
                                Total Drones: <span className="font-semibold">{orgData?.totalDrones}</span>
                            </p>
                        </div>

                        {/* Filter */}
                        <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                                <Filter className="w-4 h-4 text-black" />
                                <label className="text-sm font-medium text-black">Filter by Status</label>
                            </div>
                            <select
                                onChange={(e) => setFilter(e.target.value)}
                                value={filter}
                                className="w-full px-3 py-2 rounded-lg border border-gray-300 text-black bg-white focus:outline-none focus:ring-2 focus:ring-blue-300"
                            >
                                <option value="All">All ({droneStats.total})</option>
                                <option value="ACTIVE">Active ({droneStats.active})</option>
                                <option value="FLYING">Flying ({droneStats.flying})</option>
                                <option value="INACTIVE">Inactive ({droneStats.inactive})</option>
                                <option value="CRASHED">Crashed ({droneStats.crashed})</option>
                                <option value="MAINTENANCE">Maintenance ({droneStats.maintenance})</option>
                            </select>
                        </div>

                        {/* Drone List */}
                        <div>
                            <h3 className="font-semibold text-black mb-3 flex items-center gap-2">
                                <Activity className="w-4 h-4" />
                                Drone List ({filteredDrones.length})
                            </h3>

                            {filteredDrones.length === 0 ? (
                                <EmptyDroneList
                                    title="No drones found"
                                    message={filter === "All" ? "No drones available" : `No ${filter.toLowerCase()} drones`}
                                />
                            ) : (
                                <div className="space-y-1.5 sm:space-y-2">
                                    {filteredDrones.map((drone) => {
                                        const statusConfig = getStatusConfig(drone.status);
                                        return (
                                            <div
                                                key={drone.id}
                                                className={`bg-white shadow border rounded-lg p-2 sm:p-3 transition-all duration-200 cursor-pointer hover:shadow-md ${
                                                    selectedDrone?.id === drone.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                                                }`}
                                                onClick={() => setSelectedDrone(drone)}
                                            >
                                                <div className="flex items-center justify-between mb-1 sm:mb-2">
                                                    <p className="text-xs sm:text-sm font-semibold text-black truncate">
                                                        {drone.id}
                                                    </p>
                                                    <span className={`px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium ${statusConfig.color}`}>
                                                        {drone.status}
                                                    </span>
                                                </div>

                                                <div className="flex items-center justify-between text-[10px] sm:text-xs text-black">
                                                    <div className="flex items-center gap-0.5 sm:gap-1">
                                                        <Battery className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                                                        <span className={drone.battery < 20 ? 'text-red-600' : 'text-green-600'}>
                                                            {drone.battery}%
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-0.5 sm:gap-1">
                                                        <Gauge className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                                                        <span className="text-black">{drone.altitude}m</span>
                                                    </div>
                                                    <div className="flex items-center gap-0.5 sm:gap-1">
                                                        <Clock className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                                                        <span className="truncate max-w-[60px] sm:max-w-none text-black">{drone.lastUpdate}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Connection Status */}
                <ConnectionStatus isConnected={true} lastUpdate={lastUpdate} />
            </div>
        </div>
    );
};

export default OrgStatsMap;
