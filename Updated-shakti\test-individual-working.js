const axios = require('axios');

async function testIndividualWorking() {
  try {
    console.log('🔑 Step 1: Getting JWT Token...');
    
    // Login to get token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'Admin123!'
    });

    if (!loginResponse.data.success) {
      console.error('❌ Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!');

    console.log('\n👤 Step 2: Creating Individual...');
    
    // Create individual with exact same data structure as frontend
    const individualData = {
      fullName: "<PERSON>",
      gender: "female",
      dateOfBirth: "1985-03-20",
      contact: {
        primaryEmail: "<EMAIL>",
        phone: "9876543210",
        alternativePhone: "9876543211"
      },
      address: {
        street: "456 Oak Avenue, Block B",
        city: "Mumbai",
        state: "Maharashtra",
        country: "India",
        postalCode: "400001"
      },
      documents: {
        panNumber: "**********",
        aadharNumber: "************",
        idProofPath: "id-proof-jane.pdf",
        kycDocumentPath: "kyc-jane.pdf"
      },
      status: "pending"
    };

    console.log('📤 Sending individual data...');

    const individualResponse = await axios.post('http://localhost:5000/api/individuals', individualData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (individualResponse.data.success) {
      console.log('✅ Individual created successfully!');
      console.log('🔗 Individual ID:', individualResponse.data.data.individual._id);
      console.log('📋 Individual Name:', individualResponse.data.data.individual.fullName);
      
      // Test getting all individuals
      console.log('\n📋 Step 3: Getting All Individuals...');
      const getAllResponse = await axios.get('http://localhost:5000/api/individuals', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (getAllResponse.data.success) {
        console.log('✅ Retrieved individuals successfully!');
        console.log('📊 Total individuals:', getAllResponse.data.data.individuals.length);
        
        if (getAllResponse.data.data.individuals.length > 0) {
          console.log('👤 First individual:', getAllResponse.data.data.individuals[0].fullName);
        }
      }
      
      // Test getting individual stats
      console.log('\n📊 Step 4: Getting Individual Stats...');
      const statsResponse = await axios.get('http://localhost:5000/api/individuals/stats/overview', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (statsResponse.data.success) {
        console.log('✅ Retrieved individual stats successfully!');
        console.log('📊 Stats:', JSON.stringify(statsResponse.data.data, null, 2));
      }
      
      console.log('\n🎉 SUCCESS: Individual form is now fully working!');
      console.log('✅ Data saves to MongoDB');
      console.log('✅ Data shows in API responses');
      console.log('✅ UI should now display the data');
      
    } else {
      console.error('❌ Individual creation failed:', individualResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.data?.errors) {
      console.error('🔍 Validation Errors:', error.response.data.errors);
    }
  }
}

testIndividualWorking();
