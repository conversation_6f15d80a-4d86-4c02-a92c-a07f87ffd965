const getOrgDroneModel = require('../models/OrgDrone');
const Organization = require('../models/Organization');
const { sendSuccess, sendError } = require('../utils/response');

/**
 * Initialize organization data collection
 */
const initializeOrganizationData = async (req, res) => {
  try {
    const organizationId = req.user.organizationId || req.user.organization;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    // Get organization info (try database first, then use fallback)
    let organization = await Organization.findById(organizationId);

    if (!organization) {
      console.log('⚠️ Organization not found in database, using fallback data');
      // Use fallback organization data for development/testing
      organization = {
        _id: organizationId,
        name: req.user.profile?.organizationName || 'Test Organization',
        displayName: req.user.profile?.organizationName || 'Test Organization',
        type: 'private',
        status: 'active'
      };
    }

    return sendSuccess(res, {
      organization: {
        id: organization._id,
        name: organization.name,
        displayName: organization.displayName || organization.name,
        type: organization.type,
        status: organization.status
      }
    }, 'Organization data initialized successfully', 201);

  } catch (error) {
    console.error('❌ Initialize organization data error:', error);
    return sendError(res, 'Failed to initialize organization data', 500);
  }
};

/**
 * Get dashboard data for organization
 */
const getDashboardData = async (req, res) => {
  try {
    const organizationId = req.user.organizationId || req.user.organization;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const OrgDrone = getOrgDroneModel();

    // Get organization info (try database first, then use fallback)
    let organization = await Organization.findById(organizationId);

    if (!organization) {
      console.log('⚠️ Organization not found in database, using fallback data');
      // Use fallback organization data for development/testing
      organization = {
        _id: organizationId,
        name: req.user.profile?.organizationName || 'Test Organization',
        displayName: req.user.profile?.organizationName || 'Test Organization',
        type: 'private',
        status: 'active'
      };
    }

    // Get drones for this organization
    const drones = await OrgDrone.find({ organizationId }).sort({ createdAt: -1 });

    // Calculate statistics
    const statistics = {
      totalDrones: drones.length,
      activeDrones: drones.filter(d => d.status === 'active').length,
      inactiveDrones: drones.filter(d => d.status === 'inactive').length,
      maintenanceDrones: drones.filter(d => d.status === 'maintenance').length,
      totalFlightHours: drones.reduce((sum, d) => sum + (d.flightStats.totalFlightTime || 0), 0),
      totalFlights: drones.reduce((sum, d) => sum + (d.flightStats.totalFlights || 0), 0),
      totalDistance: drones.reduce((sum, d) => sum + (d.flightStats.totalDistance || 0), 0),
      averageFlightTime: 0
    };

    if (statistics.totalFlights > 0) {
      statistics.averageFlightTime = statistics.totalFlightHours / statistics.totalFlights;
    }

    // Prepare dashboard data
    const dashboardData = {
      organization: {
        name: organization.name,
        displayName: organization.displayName || organization.name,
        type: organization.type,
        status: organization.status
      },
      statistics,
      recentDrones: drones.slice(0, 5),
      activeDrones: drones.filter(d => d.status === 'active'),
      alerts: [],
      activities: []
    };

    // Add alerts for maintenance due
    const maintenanceDue = drones.filter(d =>
      d.maintenance.nextMaintenanceDate &&
      new Date(d.maintenance.nextMaintenanceDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    );

    if (maintenanceDue.length > 0) {
      dashboardData.alerts.push({
        type: 'maintenance',
        message: `${maintenanceDue.length} drone(s) require maintenance within 7 days`,
        count: maintenanceDue.length
      });
    }

    return sendSuccess(res, dashboardData, 'Dashboard data retrieved successfully');

  } catch (error) {
    console.error('❌ Get dashboard data error:', error);
    return sendError(res, 'Failed to retrieve dashboard data', 500);
  }
};

/**
 * Get organization's drones
 */
const getDrones = async (req, res) => {
  try {
    const organizationId = req.user.organizationId || req.user.organization;
    const { page = 1, limit = 10, status, search } = req.query;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const OrgDrone = getOrgDroneModel();

    // Build query
    let query = { organizationId };

    // Apply status filter
    if (status) {
      query.status = status;
    }

    // Apply search filter
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { name: searchRegex },
        { model: searchRegex },
        { manufacturer: searchRegex },
        { serialNumber: searchRegex },
        { registrationNumber: searchRegex }
      ];
    }

    // Get total count
    const total = await OrgDrone.countDocuments(query);

    // Get paginated results
    const drones = await OrgDrone.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    return sendSuccess(res, {
      drones,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    }, 'Drones retrieved successfully');

  } catch (error) {
    console.error('❌ Get drones error:', error);
    return sendError(res, 'Failed to retrieve drones', 500);
  }
};

/**
 * Get specific drone by ID
 */
const getDroneById = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;
    const { droneId } = req.params;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId })
      .populate('drones.assignedTo', 'username email')
      .populate('drones.createdBy', 'username');

    if (!orgData) {
      return sendError(res, 'Organization data not found', 404);
    }

    const drone = orgData.getDrone(droneId);
    if (!drone) {
      return sendError(res, 'Drone not found', 404);
    }

    return sendSuccess(res, { drone }, 'Drone retrieved successfully');

  } catch (error) {
    console.error('❌ Get drone by ID error:', error);
    return sendError(res, 'Failed to retrieve drone', 500);
  }
};

/**
 * Add new drone to organization
 */
const addDrone = async (req, res) => {
  try {
    const organizationId = req.user.organizationId || req.user.organization;
    const droneData = req.body;

    console.log('🚁 Adding drone for organization:', organizationId);
    console.log('📝 Drone data received:', JSON.stringify(droneData, null, 2));
    console.log('👤 User info:', {
      id: req.user._id,
      username: req.user.username,
      role: req.user.role
    });

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const OrgDrone = getOrgDroneModel();

    // Get organization info (try database first, then use fallback)
    let organization = await Organization.findById(organizationId);

    if (!organization) {
      console.log('⚠️ Organization not found in database, using fallback data');
      // Use fallback organization data for development/testing
      organization = {
        _id: organizationId,
        name: req.user.profile?.organizationName || 'Test Organization',
        displayName: req.user.profile?.organizationName || 'Test Organization',
        type: 'private',
        status: 'active'
      };
    }

    // Check for duplicate serial number
    if (droneData.serialNumber) {
      const existingSerial = await OrgDrone.findOne({
        serialNumber: droneData.serialNumber,
        organizationId
      });
      if (existingSerial) {
        return sendError(res, 'Drone with this serial number already exists in your organization', 409);
      }
    }

    // Check for duplicate registration number
    if (droneData.registrationNumber) {
      const existingReg = await OrgDrone.findOne({
        registrationNumber: droneData.registrationNumber,
        organizationId
      });
      if (existingReg) {
        return sendError(res, 'Drone with this registration number already exists in your organization', 409);
      }
    }

    // Generate drone ID if not provided
    const droneId = droneData.droneId || OrgDrone.generateDroneId();

    // Create new drone
    const newDrone = new OrgDrone({
      droneId,
      name: droneData.name,
      model: droneData.model,
      manufacturer: droneData.manufacturer,
      serialNumber: droneData.serialNumber || `SN-${droneId}`,
      registrationNumber: droneData.registrationNumber || `REG-${droneId}`,
      organizationId,
      organizationName: organization.name,
      droneType: droneData.specifications?.type || droneData.droneType || 'quadcopter',
      status: droneData.status || 'active',
      condition: droneData.condition || 'excellent',
      specifications: {
        weight: droneData.specifications?.weight,
        maxPayload: droneData.specifications?.maxPayload,
        maxFlightTime: droneData.specifications?.maxFlightTime,
        maxRange: droneData.specifications?.maxRange,
        maxAltitude: droneData.specifications?.maxAltitude,
        maxSpeed: droneData.specifications?.maxSpeed,
        batteryCapacity: droneData.specifications?.batteryCapacity,
        cameraResolution: droneData.specifications?.cameraResolution,
        hasGimbal: droneData.specifications?.hasGimbal || false,
        hasGPS: droneData.specifications?.hasGPS !== false,
        hasObstacleAvoidance: droneData.specifications?.hasObstacleAvoidance || false
      },
      purchase: {
        purchaseDate: droneData.purchase?.purchaseDate || new Date(),
        purchasePrice: droneData.purchase?.purchasePrice || 0,
        vendor: droneData.purchase?.vendor || 'Unknown',
        warrantyExpiryDate: droneData.purchase?.warrantyExpiryDate
      },
      currentLocation: {
        latitude: droneData.currentLocation?.latitude || 0,
        longitude: droneData.currentLocation?.longitude || 0,
        altitude: droneData.currentLocation?.altitude || 0,
        lastUpdated: new Date()
      },
      flightStats: {
        totalFlights: 0,
        totalFlightTime: 0,
        totalDistance: 0,
        averageFlightTime: 0
      },
      maintenance: {
        maintenanceIntervalHours: 50
      },
      createdBy: req.user._id,
      createdByName: req.user.username || req.user.email || 'Unknown'
    });

    // Save the drone
    const savedDrone = await newDrone.save();

    console.log('✅ Drone saved successfully:', savedDrone._id);

    return sendSuccess(res, { drone: savedDrone }, 'Drone added successfully', 201);

  } catch (error) {
    console.error('❌ Add drone error:', error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return sendError(res, `Validation failed: ${validationErrors.join(', ')}`, 400);
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return sendError(res, `Drone with this ${field} already exists`, 409);
    }

    return sendError(res, 'Failed to add drone', 500);
  }
};

/**
 * Update drone
 */
const updateDrone = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;
    const { droneId } = req.params;
    const updateData = req.body;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendError(res, 'Organization data not found', 404);
    }

    const drone = orgData.getDrone(droneId);
    if (!drone) {
      return sendError(res, 'Drone not found', 404);
    }

    // Check for duplicate serial number if being updated
    if (updateData.serialNumber && updateData.serialNumber !== drone.serialNumber) {
      const existingDrone = orgData.drones.find(d => 
        d.serialNumber === updateData.serialNumber && d._id.toString() !== droneId
      );
      if (existingDrone) {
        return sendError(res, 'Drone with this serial number already exists', 409);
      }
    }

    // Update drone
    await orgData.updateDrone(droneId, updateData);

    const updatedDrone = orgData.getDrone(droneId);

    return sendSuccess(res, { drone: updatedDrone }, 'Drone updated successfully');

  } catch (error) {
    console.error('❌ Update drone error:', error);
    return sendError(res, 'Failed to update drone', 500);
  }
};

/**
 * Remove drone from organization
 */
const removeDrone = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;
    const { droneId } = req.params;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendError(res, 'Organization data not found', 404);
    }

    const drone = orgData.getDrone(droneId);
    if (!drone) {
      return sendError(res, 'Drone not found', 404);
    }

    // Remove drone
    await orgData.removeDrone(droneId);

    return sendSuccess(res, {}, 'Drone removed successfully');

  } catch (error) {
    console.error('❌ Remove drone error:', error);
    return sendError(res, 'Failed to remove drone', 500);
  }
};

/**
 * Get organization statistics
 */
const getStatistics = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendSuccess(res, {
        totalDrones: 0,
        activeDrones: 0,
        inactiveDrones: 0,
        maintenanceDrones: 0,
        totalFlightHours: 0,
        totalFlights: 0,
        totalDistance: 0,
        averageFlightTime: 0
      }, 'Statistics retrieved successfully');
    }

    return sendSuccess(res, orgData.statistics, 'Statistics retrieved successfully');

  } catch (error) {
    console.error('❌ Get statistics error:', error);
    return sendError(res, 'Failed to retrieve statistics', 500);
  }
};

/**
 * Get organization analytics
 */
const getAnalytics = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendSuccess(res, { analytics: [] }, 'Analytics retrieved successfully');
    }

    // Generate analytics data
    const analytics = {
      droneStatusDistribution: {
        active: orgData.statistics.activeDrones,
        inactive: orgData.statistics.inactiveDrones,
        maintenance: orgData.statistics.maintenanceDrones,
        retired: orgData.drones.filter(d => d.status === 'retired').length,
        lost: orgData.drones.filter(d => d.status === 'lost').length
      },
      flightTimeAnalytics: {
        totalHours: orgData.statistics.totalFlightHours,
        averagePerDrone: orgData.statistics.totalDrones > 0 ?
          orgData.statistics.totalFlightHours / orgData.statistics.totalDrones : 0,
        topPerformers: orgData.drones
          .sort((a, b) => (b.flightStats.totalFlightTime || 0) - (a.flightStats.totalFlightTime || 0))
          .slice(0, 5)
          .map(d => ({
            name: d.name,
            flightTime: d.flightStats.totalFlightTime || 0
          }))
      },
      maintenanceAnalytics: {
        upcomingMaintenance: orgData.drones.filter(d =>
          d.maintenance.nextMaintenanceDate &&
          new Date(d.maintenance.nextMaintenanceDate) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        ).length,
        overdueMaintenance: orgData.drones.filter(d =>
          d.maintenance.nextMaintenanceDate &&
          new Date(d.maintenance.nextMaintenanceDate) < new Date()
        ).length
      }
    };

    return sendSuccess(res, analytics, 'Analytics retrieved successfully');

  } catch (error) {
    console.error('❌ Get analytics error:', error);
    return sendError(res, 'Failed to retrieve analytics', 500);
  }
};

/**
 * Add note to drone
 */
const addDroneNote = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;
    const { droneId } = req.params;
    const { content, type = 'general' } = req.body;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendError(res, 'Organization data not found', 404);
    }

    const drone = orgData.getDrone(droneId);
    if (!drone) {
      return sendError(res, 'Drone not found', 404);
    }

    // Add note
    drone.notes.push({
      content,
      type,
      addedBy: req.user._id,
      addedAt: new Date()
    });

    await orgData.save();

    return sendSuccess(res, { note: drone.notes[drone.notes.length - 1] }, 'Note added successfully', 201);

  } catch (error) {
    console.error('❌ Add drone note error:', error);
    return sendError(res, 'Failed to add note', 500);
  }
};

/**
 * Update drone status
 */
const updateDroneStatus = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;
    const { droneId } = req.params;
    const { status } = req.body;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendError(res, 'Organization data not found', 404);
    }

    const drone = orgData.getDrone(droneId);
    if (!drone) {
      return sendError(res, 'Drone not found', 404);
    }

    // Update status
    drone.status = status;
    drone.updatedAt = new Date();

    await orgData.save();

    return sendSuccess(res, { drone }, 'Drone status updated successfully');

  } catch (error) {
    console.error('❌ Update drone status error:', error);
    return sendError(res, 'Failed to update drone status', 500);
  }
};

/**
 * Get map data for organization
 */
const getMapData = async (req, res) => {
  try {
    const organizationId = req.user.organizationId;

    if (!organizationId) {
      return sendError(res, 'User is not associated with an organization', 400);
    }

    const orgData = await OrganizationData.findOne({ organizationId });
    if (!orgData) {
      return sendSuccess(res, { drones: [] }, 'Map data retrieved successfully');
    }

    // Prepare map data
    const mapData = {
      drones: orgData.drones.map(drone => ({
        id: drone._id,
        name: drone.name,
        status: drone.status,
        location: drone.currentLocation,
        battery: drone.flightStats.batteryLevel || 100,
        altitude: drone.currentLocation.altitude || 0,
        lastUpdate: drone.currentLocation.lastUpdated || drone.updatedAt
      })),
      organization: {
        name: orgData.organizationInfo.name,
        center: orgData.settings?.mapPreferences?.center || [20.5937, 78.9629],
        zoom: orgData.settings?.mapPreferences?.defaultZoom || 10
      }
    };

    return sendSuccess(res, mapData, 'Map data retrieved successfully');

  } catch (error) {
    console.error('❌ Get map data error:', error);
    return sendError(res, 'Failed to retrieve map data', 500);
  }
};

module.exports = {
  initializeOrganizationData,
  getDashboardData,
  getDrones,
  getDroneById,
  addDrone,
  updateDrone,
  removeDrone,
  getStatistics,
  getAnalytics,
  addDroneNote,
  updateDroneStatus,
  getMapData
};
