import React, { useRef, useEffect } from "react";
import "esri-leaflet";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import OrgSidebar from "./OrgSidebar";
import { Bell } from "lucide-react";

// Dummy drone data
const drones = [
    {
        id: "DRN001",
        name: "Drone Arjuna",
        lat: 28.6139,
        lng: 77.209,
        status: "Active",
        speed: "15 km/h",
        battery: "87%",
        temp: "32°C",
    },
    {
        id: "DRN002",
        name: "Drone Tejas",
        lat: 19.076,
        lng: 72.8777,
        status: "Inactive",
        speed: "0 km/h",
        battery: "100%",
        temp: "27°C",
    },
    {
        id: "DRN003",
        name: "Drone Vikrant",
        lat: 13.0827,
        lng: 80.2707,
        status: "Crashed",
        speed: "0 km/h",
        battery: "10%",
        temp: "70°C",
    },
];

// Custom marker icon
const droneIcon = new L.Icon({
    iconUrl: "/icons/drone-marker.png",
    iconSize: [35, 35],
    iconAnchor: [17, 34],
    popupAnchor: [0, -30],
});

// Status color helper
const getStatusColor = (status) => {
    switch (status) {
        case "Active":
            return "text-green-600";
        case "Inactive":
            return "text-gray-500";
        case "Crashed":
            return "text-red-600";
        case "Maintenance":
            return "text-yellow-500";
        default:
            return "text-black";
    }
};

// Add zoom control to right side
const MoveZoomControl = () => {
    const map = useMap();

    useEffect(() => {
        L.control.zoom({ position: "topright" }).addTo(map);
    }, [map]);

    return null;
};

const DroneTrackingPage = () => {
    const mapContainer = useRef(null);
    const map = useRef(null);

    return (
        <div className="w-full h-screen flex overflow-hidden text-black">
            {/* Sidebar */}
            <OrgSidebar />

            {/* Main content area */}
            <div className="flex flex-col flex-1 ml-0 lg:ml-72 h-screen">
                {/* Header */}
                <div className="flex items-center justify-between px-6 py-3 bg-[#91d0f5] shadow text-black">
                    <input
                        type="text"
                        placeholder="Search drones..."
                        className="px-4 py-2 rounded-full border-none w-80 focus:outline-none"
                    />
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <Bell size={18} />
                    </div>
                </div>

                {/* Map and Drone List */}
                <div className="relative flex flex-1 overflow-hidden">
                    {/* Drone List (Sidebar Overlay) */}
                    <div className="absolute z-[1000] top-4 left-4 bg-white p-4 rounded shadow w-72 max-h-[80vh] overflow-y-auto">
                        <h3 className="text-lg font-semibold mb-2">Drone Status</h3>
                        <ul className="space-y-2">
                            {drones.map((drone) => (
                                <li key={drone.id} className="text-sm">
                                    <div className="flex justify-between">
                                        <span className="font-medium">{drone.name}</span>
                                        <span className={`font-semibold ${getStatusColor(drone.status)}`}>
                                            {drone.status}
                                        </span>
                                    </div>
                                    <div className="text-xs text-gray-500">ID: {drone.id}</div>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Map Container */}
                    <MapContainer
                        center={[20.5937, 78.9629]}
                        zoom={5}
                        scrollWheelZoom={true}
                        className="w-full h-full z-0"
                        zoomControl={false}
                    >
                        <MoveZoomControl />
                        <TileLayer
                            attribution='Tiles &copy; Esri, Maxar, Earthstar Geographics'
                            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                        />
                        {drones.map((drone) => (
                            <Marker
                                key={drone.id}
                                position={[drone.lat, drone.lng]}
                                icon={droneIcon}
                            >
                                <Popup className="text-sm">
                                    <strong>{drone.name}</strong><br />
                                    ID: {drone.id}<br />
                                    Status: <span className={getStatusColor(drone.status)}>{drone.status}</span><br />
                                    Speed: {drone.speed}<br />
                                    Battery: {drone.battery}<br />
                                    Temp: {drone.temp}
                                </Popup>
                            </Marker>
                        ))}
                    </MapContainer>
                </div>
            </div>
        </div>
    );
};

export default DroneTrackingPage;
