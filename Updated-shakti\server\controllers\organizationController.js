const Organization = require('../models/Organization');
const User = require('../models/User');
const { sendSuccess, sendError } = require('../utils/response');
const NotificationService = require('../utils/notificationService');
const mongoose = require('mongoose');

// Get all organizations with filtering and pagination
const getAllOrganizations = async (req, res) => {
  try {
    // Check if MongoDB is connected
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState !== 1) {
      // MongoDB not connected, return mock data for development
      const mockOrganizations = [
        {
          _id: '507f1f77bcf86cd799439011',
          name: '<PERSON><PERSON>',
          displayName: '<PERSON>am <PERSON> Agricultural Services',
          type: 'private',
          contact: { primaryEmail: '<EMAIL>', phone: '+919876543210' },
          status: 'active',
          isVerified: true,
          createdAt: new Date('2023-01-15')
        },
        {
          _id: '507f1f77bcf86cd799439012',
          name: 'Test Organization',
          displayName: 'Test Organization Ltd.',
          type: 'government',
          contact: { primaryEmail: '<EMAIL>', phone: '+919876543211' },
          status: 'active',
          isVerified: true,
          createdAt: new Date('2023-02-20')
        }
      ];

      return sendSuccess(res, {
        organizations: mockOrganizations,
        pagination: {
          totalCount: mockOrganizations.length,
          currentPage: 1,
          totalPages: 1,
          limit: 10,
          hasNextPage: false,
          hasPrevPage: false
        }
      }, 'Organizations retrieved successfully (development mode)');
    }

    const {
      page = 1,
      limit = 10,
      status,
      type,
      verified,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};

    // Include deleted organizations for admin view (they can see and restore them)
    // For regular users, exclude deleted organizations
    if (!req.user || req.user.role !== 'admin') {
      filter.isDeleted = { $ne: true };
    }

    if (status) filter.status = status;
    if (type) filter.type = type;
    if (verified !== undefined) filter.isVerified = verified === 'true';
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { displayName: { $regex: search, $options: 'i' } },
        { 'contact.primaryEmail': { $regex: search, $options: 'i' } },
        { 'registration.registrationNumber': { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with population
    const organizations = await Organization.find(filter)
      .populate('createdBy', 'username email')
      .populate('verifiedBy', 'username email')
      .populate('lastModifiedBy', 'username email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const totalCount = await Organization.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return sendSuccess(res, {
      organizations,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    }, 'Organizations retrieved successfully');

  } catch (error) {
    console.error('Error in getAllOrganizations:', error);
    return sendError(res, 'Failed to retrieve organizations', 500);
  }
};

// Get organization by ID
const getOrganizationById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if user is org role and trying to access their own organization
    if (req.user && req.user.role === 'org' && req.user.profile.organizationId !== id) {
      return sendError(res, 'Access denied. You can only view your own organization.', 403);
    }

    const organization = await Organization.findById(id)
      .populate('createdBy', 'username email')
      .populate('verifiedBy', 'username email')
      .populate('lastModifiedBy', 'username email')
      .populate('notes.addedBy', 'username email');

    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    return sendSuccess(res, { organization }, 'Organization retrieved successfully');

  } catch (error) {
    console.error('Error in getOrganizationById:', error);
    return sendError(res, 'Failed to retrieve organization', 500);
  }
};

// Create new organization
const createOrganization = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const organizationData = {
      ...req.body,
      createdBy: req.user._id,
      lastModifiedBy: req.user._id
    };

    // Check if organization name already exists (excluding soft-deleted organizations)
    const existingOrg = await Organization.findOne({
      name: organizationData.name,
      isDeleted: false
    }).session(session);

    if (existingOrg) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Organization with this name already exists', 400);
    }

    // Check if registration number already exists (excluding soft-deleted organizations)
    const existingRegNumber = await Organization.findOne({
      'registration.registrationNumber': organizationData.registration.registrationNumber,
      isDeleted: false
    }).session(session);

    if (existingRegNumber) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Organization with this registration number already exists', 400);
    }

    // Create organization
    const organization = new Organization(organizationData);
    await organization.save({ session });

    // Populate the created organization
    await organization.populate('createdBy', 'username email');

    await session.commitTransaction();

    // Create notification for organization creation
    try {
      await NotificationService.createOrganizationNotification('created', organization, req.user);
    } catch (notificationError) {
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, { organization }, 'Organization created successfully', 201);

  } catch (error) {
    await session.abortTransaction();

    console.error('❌ CREATE ORGANIZATION ERROR:', error);
    console.error('❌ ERROR NAME:', error.name);
    console.error('❌ ERROR MESSAGE:', error.message);
    console.error('❌ ERROR STACK:', error.stack);

    if (error.name === 'ValidationError') {
      console.error('❌ VALIDATION ERRORS:', error.errors);
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return sendError(res, `Validation failed: ${validationErrors.join(', ')}`, 400);
    }

    if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      return sendError(res, 'Database error occurred', 500);
    }

    return sendError(res, 'Failed to create organization', 500);
  } finally {
    session.endSession();
  }
};

// Update organization
const updateOrganization = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      lastModifiedBy: req.user._id
    };

    // Remove fields that shouldn't be updated directly
    delete updateData.createdBy;
    delete updateData.stats;
    delete updateData.notes;

    // Check if organization exists and store previous values for notification
    const organization = await Organization.findById(id).session(session);
    if (!organization) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Organization not found', 404);
    }

    // Store previous values for notification
    const previousValues = {
      name: organization.name,
      status: organization.status,
      contactEmail: organization.contactInfo?.email,
      totalDrones: organization.stats?.totalDrones
    };

    // Check for duplicate name if name is being updated
    if (updateData.name && updateData.name !== organization.name) {
      const existingOrg = await Organization.findOne({
        name: updateData.name,
        _id: { $ne: id }
      }).session(session);

      if (existingOrg) {
        await session.abortTransaction();
        session.endSession();
        return sendError(res, 'Organization with this name already exists', 400);
      }
    }

    // Check for duplicate registration number if being updated
    if (updateData.registration?.registrationNumber &&
        updateData.registration.registrationNumber !== organization.registration.registrationNumber) {
      const existingRegNumber = await Organization.findOne({
        'registration.registrationNumber': updateData.registration.registrationNumber,
        _id: { $ne: id },
        isDeleted: false
      }).session(session);

      if (existingRegNumber) {
        await session.abortTransaction();
        session.endSession();
        return sendError(res, 'Organization with this registration number already exists', 400);
      }
    }

    // Update organization
    const updatedOrganization = await Organization.findByIdAndUpdate(
      id,
      updateData,
      {
        new: true,
        runValidators: true,
        session
      }
    ).populate('createdBy', 'username email')
     .populate('verifiedBy', 'username email')
     .populate('lastModifiedBy', 'username email');

    await session.commitTransaction();

    // Create notification for organization update
    try {
      await NotificationService.createOrganizationNotification('updated', updatedOrganization, req.user, previousValues);
    } catch (notificationError) {
      console.error('❌ Failed to create notification for organization update:', notificationError);
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, { organization: updatedOrganization }, 'Organization updated successfully');

  } catch (error) {
    await session.abortTransaction();
    console.error('Error in updateOrganization:', error);

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return sendError(res, `Validation failed: ${validationErrors.join(', ')}`, 400);
    }

    return sendError(res, 'Failed to update organization', 500);
  } finally {
    session.endSession();
  }
};

// Update organization status
const updateOrganizationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const organization = await Organization.findByIdAndUpdate(
      id,
      { 
        status,
        lastModifiedBy: req.user._id
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'username email')
     .populate('lastModifiedBy', 'username email');

    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    return sendSuccess(res, { organization }, 'Organization status updated successfully');

  } catch (error) {
    console.error('Error in updateOrganizationStatus:', error);
    return sendError(res, 'Failed to update organization status', 500);
  }
};

// Verify organization
const verifyOrganization = async (req, res) => {
  try {
    const { id } = req.params;

    const organization = await Organization.findByIdAndUpdate(
      id,
      {
        isVerified: true,
        status: 'active',
        verificationDate: new Date(),
        verifiedBy: req.user._id,
        lastModifiedBy: req.user._id
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'username email')
     .populate('verifiedBy', 'username email')
     .populate('lastModifiedBy', 'username email');

    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    return sendSuccess(res, { organization }, 'Organization verified successfully');

  } catch (error) {
    console.error('Error in verifyOrganization:', error);
    return sendError(res, 'Failed to verify organization', 500);
  }
};

// Get organization statistics
const getOrganizationStats = async (req, res) => {
  try {
    // Check if MongoDB is connected
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState !== 1) {
      // MongoDB not connected, return mock data for development
      const mockStats = {
        totalOrganizations: 2,
        activeOrganizations: 2,
        pendingOrganizations: 0,
        verifiedOrganizations: 2,
        organizationsByType: {
          private: 1,
          government: 1,
          ngo: 0,
          research: 0,
          military: 0,
          other: 0
        },
        recentRegistrations: 2,
        // Add mock drone data for development
        totalDrones: 45,
        activeDrones: 32,
        totalUsers: 15,
        totalFlightHours: 1250
      };
      console.log('📊 Returning mock organization stats (MongoDB not connected)');
      return sendSuccess(res, mockStats, 'Organization statistics retrieved successfully (development mode)');
    }

    const stats = await Organization.getStatistics();
    return sendSuccess(res, stats, 'Organization statistics retrieved successfully');
  } catch (error) {
    console.error('Error in getOrganizationStats:', error);

    // Fallback to mock data if database query fails
    const mockStats = {
      totalOrganizations: 2,
      activeOrganizations: 2,
      pendingOrganizations: 0,
      verifiedOrganizations: 2,
      organizationsByType: {
        private: 1,
        government: 1,
        ngo: 0,
        research: 0,
        military: 0,
        other: 0
      },
      recentRegistrations: 2,
      // Add mock drone data for development
      totalDrones: 45,
      activeDrones: 32,
      totalUsers: 15,
      totalFlightHours: 1250
    };
    console.log('📊 Returning mock organization stats (database error fallback)');
    return sendSuccess(res, mockStats, 'Organization statistics retrieved successfully (fallback mode)');
  }
};

// Add note to organization
const addOrganizationNote = async (req, res) => {
  try {
    const { id } = req.params;
    const { content, type = 'general' } = req.body;

    const organization = await Organization.findById(id);
    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    await organization.addNote(content, req.user._id, type);

    // Get updated organization with populated notes
    const updatedOrganization = await Organization.findById(id)
      .populate('notes.addedBy', 'username email');

    return sendSuccess(res, { organization: updatedOrganization }, 'Note added successfully');

  } catch (error) {
    console.error('Error in addOrganizationNote:', error);
    return sendError(res, 'Failed to add note', 500);
  }
};

// Delete organization (soft delete with 10-day grace period)
const deleteOrganization = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { id } = req.params;

    // Check if organization exists and is not already deleted
    const organization = await Organization.findOne({
      _id: id,
      isDeleted: false
    }).session(session);

    if (!organization) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Organization not found or already deleted', 404);
    }

    // Check if organization has active users
    const activeUsers = await User.countDocuments({
      'profile.organizationId': id,
      isActive: true
    }).session(session);

    if (activeUsers > 0) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Cannot delete organization with active users. Please deactivate all users first.', 400);
    }

    // Soft delete the organization with 10-day grace period
    await organization.softDelete(req.user._id);

    await session.commitTransaction();

    // Create notification for organization deletion
    try {
      await NotificationService.createOrganizationNotification('deleted', organization, req.user);
    } catch (notificationError) {
      console.error('❌ Failed to create notification for organization deletion:', notificationError);
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, {
      organization: {
        id: organization._id,
        name: organization.name,
        deletedAt: organization.deletedAt,
        permanentDeleteAt: organization.permanentDeleteAt,
        daysUntilPermanentDelete: organization.daysUntilPermanentDelete
      }
    }, 'Organization marked for deletion. It will be permanently deleted in 10 days.');

  } catch (error) {
    await session.abortTransaction();
    console.error('Error in deleteOrganization:', error);
    return sendError(res, 'Failed to delete organization', 500);
  } finally {
    session.endSession();
  }
};

// Get users belonging to organization
const getOrganizationUsers = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    // Check if user is org role and trying to access their own organization
    if (req.user && req.user.role === 'org' && req.user.profile.organizationId !== id) {
      return sendError(res, 'Access denied. You can only view users from your own organization.', 403);
    }

    // Check if organization exists
    const organization = await Organization.findById(id);
    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    // Build filter
    const filter = { 'profile.organizationId': id };
    if (status) filter.isActive = status === 'active';

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get users
    const users = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return sendSuccess(res, {
      users,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    }, 'Organization users retrieved successfully');

  } catch (error) {
    console.error('Error in getOrganizationUsers:', error);
    return sendError(res, 'Failed to retrieve organization users', 500);
  }
};

// Get drones belonging to organization
const getOrganizationDrones = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user is org role and trying to access their own organization
    if (req.user && req.user.role === 'org' && req.user.profile.organizationId !== id) {
      return sendError(res, 'Access denied. You can only view drones from your own organization.', 403);
    }

    // Check if organization exists
    const organization = await Organization.findById(id);
    if (!organization) {
      return sendError(res, 'Organization not found', 404);
    }

    // For now, return empty array as Drone model will be created later
    // This will be updated when Drone model is implemented
    return sendSuccess(res, {
      drones: [],
      message: 'Drone model not yet implemented'
    }, 'Organization drones retrieved successfully');

  } catch (error) {
    console.error('Error in getOrganizationDrones:', error);
    return sendError(res, 'Failed to retrieve organization drones', 500);
  }
};

// Get deleted organizations (for admin to see what's pending deletion)
const getDeletedOrganizations = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    // Build search query for deleted organizations
    const searchQuery = {
      isDeleted: true,
      ...(search && {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { 'contact.primaryEmail': { $regex: search, $options: 'i' } },
          { 'registration.registrationNumber': { $regex: search, $options: 'i' } }
        ]
      })
    };

    const deletedOrganizations = await Organization.find(searchQuery)
      .populate('deletedBy', 'username email')
      .sort({ deletedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await Organization.countDocuments(searchQuery);

    // Add computed fields for each organization
    const organizationsWithTimer = deletedOrganizations.map(org => ({
      ...org,
      daysUntilPermanentDelete: Math.ceil((new Date(org.permanentDeleteAt) - new Date()) / (1000 * 60 * 60 * 24)),
      canRestore: new Date() < new Date(org.permanentDeleteAt)
    }));

    return sendSuccess(res, {
      organizations: organizationsWithTimer,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    }, 'Deleted organizations retrieved successfully');

  } catch (error) {
    console.error('Error in getDeletedOrganizations:', error);
    return sendError(res, 'Failed to retrieve deleted organizations', 500);
  }
};

// Restore deleted organization
const restoreOrganization = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { id } = req.params;

    // Find the deleted organization
    const organization = await Organization.findOne({
      _id: id,
      isDeleted: true
    }).session(session);

    if (!organization) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Deleted organization not found', 404);
    }

    // Check if it's still within the grace period
    if (new Date() >= organization.permanentDeleteAt) {
      await session.abortTransaction();
      session.endSession();
      return sendError(res, 'Organization cannot be restored as the grace period has expired', 400);
    }

    // Restore the organization
    await organization.restore(req.user._id);

    await session.commitTransaction();

    // Create notification for organization restoration
    try {
      await NotificationService.createOrganizationNotification('restored', organization, req.user);
    } catch (notificationError) {
      console.error('❌ Failed to create notification for organization restoration:', notificationError);
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, {
      organization: {
        id: organization._id,
        name: organization.name,
        status: organization.status,
        restoredAt: new Date()
      }
    }, 'Organization restored successfully');

  } catch (error) {
    await session.abortTransaction();
    console.error('Error in restoreOrganization:', error);
    return sendError(res, 'Failed to restore organization', 500);
  } finally {
    session.endSession();
  }
};

// Permanently delete expired organizations (for background job)
const permanentlyDeleteExpiredOrganizations = async () => {
  try {
    const now = new Date();

    // Find organizations that should be permanently deleted
    const expiredOrganizations = await Organization.find({
      isDeleted: true,
      permanentDeleteAt: { $lte: now }
    });

    console.log(`Found ${expiredOrganizations.length} organizations to permanently delete`);

    for (const org of expiredOrganizations) {
      try {
        // Permanently delete the organization
        await Organization.findByIdAndDelete(org._id);
        console.log(`✅ Permanently deleted organization: ${org.name} (${org._id})`);
      } catch (error) {
        console.error(`❌ Failed to permanently delete organization ${org._id}:`, error);
      }
    }

    return expiredOrganizations.length;
  } catch (error) {
    console.error('Error in permanentlyDeleteExpiredOrganizations:', error);
    return 0;
  }
};

// Get organization analytics data
const getOrganizationAnalytics = async (req, res) => {
  try {
    console.log('🔍 Fetching organization analytics data...');

    // Get monthly registration data for the last 12 months
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const monthlyData = await Organization.aggregate([
      {
        $match: {
          isDeleted: false,
          createdAt: { $gte: twelveMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          orgs: { $sum: 1 },
          approved: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          pending: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          rejected: {
            $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
          }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Get status distribution
    const statusData = await Organization.aggregate([
      {
        $match: { isDeleted: false }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get overall stats
    const overallStats = await Organization.aggregate([
      {
        $match: { isDeleted: false }
      },
      {
        $group: {
          _id: null,
          totalOrganizations: { $sum: 1 },
          activeOrganizations: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          pendingOrganizations: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          verifiedOrganizations: {
            $sum: { $cond: ['$isVerified', 1, 0] }
          },
          totalAllocatedDrones: { $sum: { $ifNull: ['$allocatedDrones', 0] } }
        }
      }
    ]);

    // Format monthly data with month names
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const formattedMonthlyData = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;

      const monthData = monthlyData.find(item =>
        item._id.year === year && item._id.month === month
      );

      formattedMonthlyData.push({
        month: monthNames[month - 1],
        orgs: monthData?.orgs || 0,
        approved: monthData?.approved || 0,
        pending: monthData?.pending || 0,
        rejected: monthData?.rejected || 0
      });
    }

    // Format status data
    const statusMap = {
      'active': { name: 'Approved', color: '#10b981' },
      'pending': { name: 'Pending', color: '#f59e0b' },
      'suspended': { name: 'Rejected', color: '#ef4444' },
      'inactive': { name: 'Inactive', color: '#6b7280' }
    };

    const formattedStatusData = statusData.map(item => ({
      name: statusMap[item._id]?.name || item._id,
      value: item.count,
      color: statusMap[item._id]?.color || '#6b7280'
    }));

    const stats = overallStats[0] || {
      totalOrganizations: 0,
      activeOrganizations: 0,
      pendingOrganizations: 0,
      verifiedOrganizations: 0,
      totalAllocatedDrones: 0
    };

    const analyticsData = {
      monthlyData: formattedMonthlyData,
      statusData: formattedStatusData,
      stats: {
        totalRegistered: stats.totalOrganizations,
        approved: stats.activeOrganizations,
        pending: stats.pendingOrganizations,
        avgPerMonth: (stats.totalOrganizations / 12).toFixed(1)
      }
    };

    return sendSuccess(res, analyticsData, 'Organization analytics retrieved successfully');

  } catch (error) {
    return sendError(res, 'Failed to retrieve organization analytics', 500);
  }
};

module.exports = {
  getAllOrganizations,
  getOrganizationById,
  createOrganization,
  updateOrganization,
  updateOrganizationStatus,
  verifyOrganization,
  getOrganizationStats,
  getOrganizationAnalytics,
  addOrganizationNote,
  deleteOrganization,
  getOrganizationUsers,
  getOrganizationDrones,
  getDeletedOrganizations,
  restoreOrganization,
  permanentlyDeleteExpiredOrganizations
};
