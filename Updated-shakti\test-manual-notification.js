const mongoose = require('mongoose');
const Notification = require('./server/models/Notification');
require('dotenv').config();

async function createTestNotification() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create a test notification
    const testNotification = new Notification({
      title: 'Test Notification',
      message: 'This is a test notification to verify the system is working.',
      type: 'system_test',
      category: 'system',
      priority: 'medium',
      triggeredBy: {
        userId: null,
        username: 'System',
        role: 'system'
      },
      metadata: {
        test: true,
        timestamp: new Date()
      }
    });

    await testNotification.save();
    console.log('✅ Test notification created successfully');
    console.log('📧 Notification details:');
    console.log('   ID:', testNotification._id);
    console.log('   Title:', testNotification.title);
    console.log('   Message:', testNotification.message);
    console.log('   Created:', testNotification.createdAt);

    // Check total notifications count
    const totalNotifications = await Notification.countDocuments();
    console.log('📊 Total notifications in database:', totalNotifications);

    // List recent notifications
    const recentNotifications = await Notification.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title type createdAt');
    
    console.log('📋 Recent notifications:');
    recentNotifications.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (${notif.type}) - ${notif.createdAt}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

createTestNotification();
