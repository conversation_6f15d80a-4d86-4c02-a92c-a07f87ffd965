const axios = require('axios');

async function testDroneAPI() {
  try {
    console.log('🔑 Step 1: Getting JWT Token...');
    
    // Login to get token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'Admin123!'
    });

    if (!loginResponse.data.success) {
      console.error('❌ Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!');
    console.log('🎯 JWT Token:', token.substring(0, 50) + '...');

    console.log('\n🚁 Step 2: Creating Drone...');
    
    // Create drone with exact same data structure as frontend
    const droneData = {
      name: "AgriDrone Pro X1",
      model: "AgriDrone Pro X1",
      serialNumber: "DRONE001",
      registrationNumber: `REG-DRONE001-${Date.now()}`,
      manufacturer: "DJI",
      specifications: {
        type: "quadcopter",
        weight: 5.5,
        maxPayload: 2.5,
        maxFlightTime: 30,
        maxRange: 5,
        maxAltitude: 120,
        maxSpeed: 50,
        batteryCapacity: 5000,
        hasGPS: true,
        hasGimbal: false,
        hasObstacleAvoidance: false
      },
      purchase: {
        purchaseDate: new Date().toISOString(),
        purchasePrice: 50000,
        vendor: "DJI"
      },
      status: "active",
      condition: "excellent"
    };

    console.log('📤 Sending drone data:', JSON.stringify(droneData, null, 2));

    const droneResponse = await axios.post('http://localhost:5000/api/drones', droneData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (droneResponse.data.success) {
      console.log('✅ Drone created successfully!');
      console.log('📋 Response:', JSON.stringify(droneResponse.data, null, 2));
      console.log('\n🎉 SUCCESS: The drone API is working correctly!');
      console.log('🔗 Drone ID:', droneResponse.data.data.drone._id);
    } else {
      console.error('❌ Drone creation failed:', droneResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.data?.errors) {
      console.error('🔍 Validation Errors:', error.response.data.errors);
    }
  }
}

testDroneAPI();
