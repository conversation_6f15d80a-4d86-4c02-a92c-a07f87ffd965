import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Plus,
  Search,
  Filter,
  Settings,
  CheckCircle,
  AlertTriangle,
  User,
  MapPin,
  Save,
  X,
  Edit,
  Trash2,
  RefreshCw
} from 'lucide-react';
import {
  FaTools,
  FaPlane,
  FaCalendarAlt,
  FaClock,
  FaUser,
  FaMapMarkerAlt,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimesCircle
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const MaintenanceScheduler = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedDrone, setSelectedDrone] = useState('');
  const [maintenanceType, setMaintenanceType] = useState('');
  const [priority, setPriority] = useState('medium');
  const [technician, setTechnician] = useState('');
  const [notes, setNotes] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data for drones
  const drones = [
    { id: 'PRYMAJ95175', name: 'Falcon-X1', status: 'active', lastMaintenance: '2024-01-15', nextDue: '2024-02-15' },
    { id: 'PRYMAJ95173', name: 'Eagle-Pro', status: 'maintenance', lastMaintenance: '2024-01-20', nextDue: '2024-02-20' },
    { id: 'PRYMAJ95178', name: 'Hawk-Elite', status: 'active', lastMaintenance: '2024-01-10', nextDue: '2024-02-10' },
    { id: 'PRYMAJ95176', name: 'Phoenix-Max', status: 'inactive', lastMaintenance: '2024-01-25', nextDue: '2024-02-25' }
  ];

  // Mock data for maintenance schedules
  const [maintenanceSchedules, setMaintenanceSchedules] = useState([
    {
      id: 1,
      droneId: 'PRYMAJ95175',
      droneName: 'Falcon-X1',
      type: 'Routine Inspection',
      date: '2024-02-15',
      time: '09:00',
      technician: 'John Smith',
      priority: 'medium',
      status: 'scheduled',
      notes: 'Regular monthly inspection'
    },
    {
      id: 2,
      droneId: 'PRYMAJ95173',
      droneName: 'Eagle-Pro',
      type: 'Battery Replacement',
      date: '2024-02-16',
      time: '14:00',
      technician: 'Sarah Johnson',
      priority: 'high',
      status: 'in-progress',
      notes: 'Battery showing degraded performance'
    },
    {
      id: 3,
      droneId: 'PRYMAJ95178',
      droneName: 'Hawk-Elite',
      type: 'Propeller Check',
      date: '2024-02-17',
      time: '11:00',
      technician: 'Mike Wilson',
      priority: 'low',
      status: 'completed',
      notes: 'Propellers replaced successfully'
    }
  ]);

  const maintenanceTypes = [
    'Routine Inspection',
    'Battery Replacement',
    'Propeller Check',
    'Sensor Calibration',
    'Software Update',
    'Motor Maintenance',
    'Camera Cleaning',
    'GPS Calibration'
  ];

  const technicians = [
    'John Smith',
    'Sarah Johnson',
    'Mike Wilson',
    'Emily Davis',
    'Robert Brown'
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'scheduled':
        return <FaClock className="w-4 h-4 text-blue-600" />;
      case 'in-progress':
        return <FaTools className="w-4 h-4 text-orange-600" />;
      case 'completed':
        return <FaCheckCircle className="w-4 h-4 text-green-600" />;
      case 'cancelled':
        return <FaTimesCircle className="w-4 h-4 text-red-600" />;
      default:
        return <FaClock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredSchedules = useMemo(() => {
    return maintenanceSchedules.filter(schedule => {
      const matchesSearch = !searchTerm || 
        schedule.droneName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        schedule.droneId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        schedule.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        schedule.technician.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || schedule.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [maintenanceSchedules, searchTerm, statusFilter]);

  const handleAddMaintenance = () => {
    if (!selectedDrone || !maintenanceType || !selectedDate || !technician) {
      alert('Please fill in all required fields');
      return;
    }

    const newSchedule = {
      id: Date.now(),
      droneId: selectedDrone,
      droneName: drones.find(d => d.id === selectedDrone)?.name || 'Unknown',
      type: maintenanceType,
      date: selectedDate,
      time: '09:00',
      technician,
      priority,
      status: 'scheduled',
      notes
    };

    setMaintenanceSchedules([...maintenanceSchedules, newSchedule]);
    setShowAddModal(false);
    
    // Reset form
    setSelectedDrone('');
    setMaintenanceType('');
    setTechnician('');
    setNotes('');
    setPriority('medium');
  };

  const handleDeleteSchedule = (id) => {
    if (window.confirm('Are you sure you want to delete this maintenance schedule?')) {
      setMaintenanceSchedules(maintenanceSchedules.filter(schedule => schedule.id !== id));
    }
  };

  const handleStatusChange = (id, newStatus) => {
    setMaintenanceSchedules(maintenanceSchedules.map(schedule => 
      schedule.id === id ? { ...schedule, status: newStatus } : schedule
    ));
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/inventory')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Inventory</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaTools className="text-orange-600" />
                  Maintenance Scheduler
                </h2>
                <p className="text-gray-600 mt-1">Schedule and track drone maintenance activities</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                <Plus size={18} />
                <span className="hidden sm:inline">Schedule Maintenance</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6 space-y-6">
          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search by drone, type, or technician..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
              
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  <option value="all">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>

          {/* Maintenance Schedules Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FaCalendarAlt className="text-orange-600" />
                Maintenance Schedules ({filteredSchedules.length})
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Drone
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Maintenance Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Technician
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSchedules.map((schedule) => (
                    <tr key={schedule.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <FaPlane className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{schedule.droneName}</div>
                            <div className="text-xs text-gray-500">ID: {schedule.droneId}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-medium">{schedule.type}</div>
                        {schedule.notes && (
                          <div className="text-xs text-gray-500 truncate max-w-xs">{schedule.notes}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center gap-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          {schedule.date}
                        </div>
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          {schedule.time}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-400" />
                          {schedule.technician}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(schedule.priority)}`}>
                          {schedule.priority.charAt(0).toUpperCase() + schedule.priority.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(schedule.status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(schedule.status)}`}>
                            {schedule.status.charAt(0).toUpperCase() + schedule.status.slice(1).replace('-', ' ')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2">
                          {schedule.status === 'scheduled' && (
                            <button
                              onClick={() => handleStatusChange(schedule.id, 'in-progress')}
                              className="text-orange-600 hover:text-orange-900 p-1 rounded transition-colors"
                              title="Start Maintenance"
                            >
                              <FaTools size={16} />
                            </button>
                          )}
                          {schedule.status === 'in-progress' && (
                            <button
                              onClick={() => handleStatusChange(schedule.id, 'completed')}
                              className="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                              title="Mark Complete"
                            >
                              <CheckCircle size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteSchedule(schedule.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredSchedules.length === 0 && (
                <div className="text-center py-12">
                  <FaTools className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance schedules</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm || statusFilter !== 'all'
                      ? 'No schedules match your current filters.'
                      : 'Get started by scheduling your first maintenance.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Maintenance Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaTools className="text-orange-600" />
                  Schedule Maintenance
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Drone *
                  </label>
                  <select
                    value={selectedDrone}
                    onChange={(e) => setSelectedDrone(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  >
                    <option value="">Choose a drone...</option>
                    {drones.map((drone) => (
                      <option key={drone.id} value={drone.id}>
                        {drone.name} ({drone.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maintenance Type *
                  </label>
                  <select
                    value={maintenanceType}
                    onChange={(e) => setMaintenanceType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  >
                    <option value="">Select type...</option>
                    {maintenanceTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Scheduled Date *
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assigned Technician *
                  </label>
                  <select
                    value={technician}
                    onChange={(e) => setTechnician(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  >
                    <option value="">Select technician...</option>
                    {technicians.map((tech) => (
                      <option key={tech} value={tech}>
                        {tech}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority Level
                  </label>
                  <select
                    value={priority}
                    onChange={(e) => setPriority(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes (Optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  placeholder="Add any additional notes or instructions..."
                />
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex items-center justify-end gap-4">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddMaintenance}
                  className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                >
                  <Save size={16} />
                  Schedule Maintenance
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MaintenanceScheduler;
