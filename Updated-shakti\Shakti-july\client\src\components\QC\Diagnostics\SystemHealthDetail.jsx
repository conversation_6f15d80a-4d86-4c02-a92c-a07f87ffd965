import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Edit,
  Download,
  Printer,
  Battery,
  Zap,
  Settings,
  Wifi,
  HardDrive,
  Cpu,
  Thermometer,
  Gauge,
  TrendingUp,
  TrendingDown,
  Target,
  Shield
} from 'lucide-react';

const SystemHealthDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [healthCheck, setHealthCheck] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample detailed health check data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const sampleData = {
        'SH-2024-001': {
          id: 'SH-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          technician: '<PERSON>',
          date: '2024-01-15',
          time: '10:30',
          location: 'Maintenance Bay A',
          status: 'Healthy',
          overallScore: 95,
          duration: '15 minutes',
          nextCheck: '2024-01-22',
          flightHours: 245.5,
          lastMaintenance: '2024-01-10',
          systems: {
            battery: {
              status: 'Healthy',
              score: 98,
              temperature: '22°C',
              voltage: '25.2V',
              cycles: 145,
              capacity: '95%',
              health: 'Excellent',
              estimatedLife: '18 months',
              details: {
                cellVoltages: ['4.2V', '4.2V', '4.1V', '4.2V', '4.2V', '4.1V'],
                chargingTime: '45 minutes',
                dischargingRate: 'Normal',
                temperatureRange: '18°C - 25°C'
              }
            },
            motors: {
              status: 'Healthy',
              score: 96,
              temperature: '28°C',
              vibration: 'Low',
              efficiency: '98%',
              rpm: '5200 RPM',
              torque: 'Normal',
              details: {
                motor1: { rpm: 5200, temp: '27°C', vibration: 'Low' },
                motor2: { rpm: 5180, temp: '28°C', vibration: 'Low' },
                motor3: { rpm: 5220, temp: '29°C', vibration: 'Low' },
                motor4: { rpm: 5190, temp: '28°C', vibration: 'Low' }
              }
            },
            sensors: {
              status: 'Warning',
              score: 85,
              calibration: 'Drift detected',
              accuracy: 'Good',
              temperature: '24°C',
              details: {
                gps: { status: 'Healthy', accuracy: '±2m', satellites: 12 },
                imu: { status: 'Warning', drift: '0.5°/hr', calibration: 'Needed' },
                barometer: { status: 'Healthy', accuracy: '±1m', pressure: '1013.25 hPa' },
                compass: { status: 'Healthy', deviation: '±1°', calibration: 'Good' },
                camera: { status: 'Healthy', resolution: '4K', stabilization: 'Active' }
              }
            },
            communication: {
              status: 'Healthy',
              score: 94,
              signal: 'Strong',
              latency: '12ms',
              range: 'Full',
              frequency: '2.4GHz',
              details: {
                telemetry: { status: 'Active', rate: '10Hz', quality: 'Excellent' },
                video: { status: 'Active', bitrate: '20Mbps', quality: 'HD' },
                control: { status: 'Active', latency: '12ms', reliability: '99.9%' }
              }
            },
            storage: {
              status: 'Healthy',
              score: 92,
              usage: '68%',
              speed: 'Normal',
              errors: 0,
              capacity: '256GB',
              available: '82GB',
              details: {
                readSpeed: '95 MB/s',
                writeSpeed: '85 MB/s',
                health: '98%',
                badSectors: 0,
                temperature: '35°C'
              }
            },
            processor: {
              status: 'Healthy',
              score: 97,
              load: '45%',
              temperature: '35°C',
              memory: '78%',
              details: {
                cpuUsage: '45%',
                memoryUsage: '78%',
                cacheHit: '95%',
                processes: 24,
                uptime: '72 hours'
              }
            }
          },
          environmentalData: {
            temperature: '22°C',
            humidity: '45%',
            pressure: '1013.25 hPa',
            windSpeed: '5 km/h',
            visibility: '10 km'
          },
          issues: [
            {
              severity: 'Warning',
              component: 'IMU Sensor',
              description: 'Minor sensor drift detected in IMU',
              impact: 'May affect flight stability in precision maneuvers',
              recommendation: 'Recalibrate IMU sensors before next flight'
            }
          ],
          recommendations: [
            'Recalibrate IMU sensors',
            'Monitor sensor performance',
            'Schedule routine maintenance check',
            'Update firmware to latest version'
          ],
          maintenanceHistory: [
            { date: '2024-01-10', type: 'Routine Maintenance', technician: 'John Smith', status: 'Completed' },
            { date: '2024-01-05', type: 'Battery Calibration', technician: 'Sarah Johnson', status: 'Completed' },
            { date: '2023-12-28', type: 'Sensor Calibration', technician: 'Mike Wilson', status: 'Completed' }
          ],
          performanceMetrics: {
            flightTime: '2.5 hours',
            maxAltitude: '120m',
            maxSpeed: '25 km/h',
            dataCollected: '15.2 GB',
            missionSuccess: '100%',
            energyEfficiency: '95%'
          }
        },
        'SH-2024-002': {
          id: 'SH-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          technician: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:15',
          location: 'Field Station B',
          status: 'Critical',
          overallScore: 65,
          duration: '25 minutes',
          nextCheck: '2024-01-15',
          flightHours: 189.3,
          lastMaintenance: '2024-01-08',
          systems: {
            battery: {
              status: 'Warning',
              score: 75,
              temperature: '35°C',
              voltage: '23.8V',
              cycles: 298,
              capacity: '78%',
              health: 'Degraded',
              estimatedLife: '3 months',
              details: {
                cellVoltages: ['3.9V', '4.0V', '3.8V', '4.1V', '3.9V', '3.7V'],
                chargingTime: '65 minutes',
                dischargingRate: 'High',
                temperatureRange: '25°C - 38°C'
              }
            },
            motors: {
              status: 'Critical',
              score: 45,
              temperature: '68°C',
              vibration: 'High',
              efficiency: '82%',
              rpm: '4800 RPM',
              torque: 'Reduced',
              details: {
                motor1: { rpm: 4850, temp: '65°C', vibration: 'Normal' },
                motor2: { rpm: 4200, temp: '72°C', vibration: 'Critical' },
                motor3: { rpm: 4900, temp: '68°C', vibration: 'High' },
                motor4: { rpm: 4750, temp: '66°C', vibration: 'Normal' }
              }
            },
            sensors: {
              status: 'Healthy',
              score: 88,
              calibration: 'Good',
              accuracy: 'High',
              temperature: '26°C',
              details: {
                gps: { status: 'Healthy', accuracy: '±1.5m', satellites: 14 },
                imu: { status: 'Healthy', drift: '0.2°/hr', calibration: 'Good' },
                barometer: { status: 'Healthy', accuracy: '±0.5m', pressure: '1012.8 hPa' },
                compass: { status: 'Healthy', deviation: '±0.5°', calibration: 'Excellent' },
                camera: { status: 'Healthy', resolution: '4K', stabilization: 'Active' }
              }
            },
            communication: {
              status: 'Healthy',
              score: 91,
              signal: 'Strong',
              latency: '15ms',
              range: 'Full',
              frequency: '2.4GHz',
              details: {
                telemetry: { status: 'Active', rate: '10Hz', quality: 'Good' },
                video: { status: 'Active', bitrate: '18Mbps', quality: 'HD' },
                control: { status: 'Active', latency: '15ms', reliability: '99.5%' }
              }
            },
            storage: {
              status: 'Warning',
              score: 78,
              usage: '89%',
              speed: 'Slow',
              errors: 3,
              capacity: '128GB',
              available: '14GB',
              details: {
                readSpeed: '65 MB/s',
                writeSpeed: '45 MB/s',
                health: '85%',
                badSectors: 3,
                temperature: '42°C'
              }
            },
            processor: {
              status: 'Healthy',
              score: 89,
              load: '62%',
              temperature: '42°C',
              memory: '85%',
              details: {
                cpuUsage: '62%',
                memoryUsage: '85%',
                cacheHit: '88%',
                processes: 28,
                uptime: '96 hours'
              }
            }
          },
          environmentalData: {
            temperature: '28°C',
            humidity: '65%',
            pressure: '1011.5 hPa',
            windSpeed: '12 km/h',
            visibility: '8 km'
          },
          issues: [
            {
              severity: 'Critical',
              component: 'Motor #2',
              description: 'Motor #2 overheating and high vibration levels',
              impact: 'Flight safety compromised, immediate grounding required',
              recommendation: 'Replace motor #2 immediately'
            },
            {
              severity: 'Warning',
              component: 'Storage',
              description: 'Storage near capacity with performance degradation',
              impact: 'May affect data logging and system performance',
              recommendation: 'Clean storage and defragment'
            },
            {
              severity: 'Warning',
              component: 'Battery',
              description: 'Battery showing signs of degradation',
              impact: 'Reduced flight time and performance',
              recommendation: 'Consider battery replacement'
            }
          ],
          recommendations: [
            'Replace motor #2 immediately',
            'Clean storage and defragment',
            'Consider battery replacement',
            'Perform comprehensive system check',
            'Review maintenance schedule'
          ],
          maintenanceHistory: [
            { date: '2024-01-08', type: 'Routine Maintenance', technician: 'Sarah Johnson', status: 'Completed' },
            { date: '2024-01-02', type: 'Motor Inspection', technician: 'Mike Wilson', status: 'Issues Found' },
            { date: '2023-12-25', type: 'Battery Check', technician: 'John Smith', status: 'Warning' }
          ],
          performanceMetrics: {
            flightTime: '1.8 hours',
            maxAltitude: '95m',
            maxSpeed: '18 km/h',
            dataCollected: '8.7 GB',
            missionSuccess: '89%',
            energyEfficiency: '78%'
          }
        }
      };

      setHealthCheck(sampleData[id] || null);
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!healthCheck) {
    return (
      <QCLayout title="Health Check Not Found" subtitle="The requested health check could not be found">
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Health Check Not Found</h3>
          <p className="text-gray-600 mb-4">The health check with ID "{id}" could not be found.</p>
          <button
            onClick={() => navigate('/qc-diagnostics/system-health')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to System Health
          </button>
        </div>
      </QCLayout>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Healthy': return 'bg-green-100 text-green-700 border-green-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Critical': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Healthy': return <CheckCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      case 'Critical': return <XCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSystemIcon = (system) => {
    switch (system) {
      case 'battery': return <Battery className="w-5 h-5" />;
      case 'motors': return <Zap className="w-5 h-5" />;
      case 'sensors': return <Settings className="w-5 h-5" />;
      case 'communication': return <Wifi className="w-5 h-5" />;
      case 'storage': return <HardDrive className="w-5 h-5" />;
      case 'processor': return <Cpu className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  return (
    <QCLayout
      title={`System Health - ${healthCheck.id}`}
      subtitle={`${healthCheck.droneId} - ${healthCheck.droneName}`}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate('/qc-diagnostics/system-health')}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to System Health
          </button>
          
          <div className="flex items-center gap-3">
            <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Edit
            </button>
            <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
            <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
              <Printer className="w-4 h-4" />
              Print
            </button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Status Card */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Health Check Overview</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {healthCheck.technician}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {healthCheck.date} at {healthCheck.time}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {healthCheck.location}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {healthCheck.duration}
                  </span>
                </div>
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(healthCheck.status)}`}>
                {getStatusIcon(healthCheck.status)}
                <span className="ml-2">{healthCheck.status}</span>
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(healthCheck.overallScore)} mb-1`}>
                  {healthCheck.overallScore}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div
                    className={`h-2 rounded-full ${healthCheck.overallScore >= 90 ? 'bg-green-500' : healthCheck.overallScore >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                    style={{width: `${healthCheck.overallScore}%`}}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">Overall Health</p>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">{healthCheck.flightHours}h</div>
                <p className="text-xs text-gray-500">Flight Hours</p>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">{healthCheck.lastMaintenance}</div>
                <p className="text-xs text-gray-500">Last Maintenance</p>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">{healthCheck.nextCheck}</div>
                <p className="text-xs text-gray-500">Next Check</p>
              </div>
            </div>
          </div>

          {/* Environmental Data */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Environmental Conditions</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 flex items-center gap-2">
                  <Thermometer className="w-4 h-4" />
                  Temperature
                </span>
                <span className="text-sm font-medium text-gray-900">{healthCheck.environmentalData.temperature}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Humidity</span>
                <span className="text-sm font-medium text-gray-900">{healthCheck.environmentalData.humidity}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pressure</span>
                <span className="text-sm font-medium text-gray-900">{healthCheck.environmentalData.pressure}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Wind Speed</span>
                <span className="text-sm font-medium text-gray-900">{healthCheck.environmentalData.windSpeed}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Visibility</span>
                <span className="text-sm font-medium text-gray-900">{healthCheck.environmentalData.visibility}</span>
              </div>
            </div>
          </div>
        </div>

        {/* System Status Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(healthCheck.systems).map(([systemName, systemData]) => (
            <div key={systemName} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${systemData.status === 'Healthy' ? 'bg-green-100 text-green-600' : systemData.status === 'Warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>
                    {getSystemIcon(systemName)}
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 capitalize">{systemName}</h4>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(systemData.status)}`}>
                      {getStatusIcon(systemData.status)}
                      <span className="ml-1">{systemData.status}</span>
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-xl font-bold ${getScoreColor(systemData.score)}`}>
                    {systemData.score}%
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                {systemData.temperature && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Temperature</span>
                    <span className="font-medium text-gray-900">{systemData.temperature}</span>
                  </div>
                )}
                {systemData.voltage && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Voltage</span>
                    <span className="font-medium text-gray-900">{systemData.voltage}</span>
                  </div>
                )}
                {systemData.efficiency && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Efficiency</span>
                    <span className="font-medium text-gray-900">{systemData.efficiency}</span>
                  </div>
                )}
                {systemData.usage && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Usage</span>
                    <span className="font-medium text-gray-900">{systemData.usage}</span>
                  </div>
                )}
                {systemData.signal && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Signal</span>
                    <span className="font-medium text-gray-900">{systemData.signal}</span>
                  </div>
                )}
                {systemData.latency && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Latency</span>
                    <span className="font-medium text-gray-900">{systemData.latency}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </QCLayout>
  );
};

export default SystemHealthDetail;
