{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.8", "axios": "^1.10.0", "chart.js": "^4.4.9", "esri-leaflet": "^3.0.17", "leaflet": "^1.9.4", "lucide-react": "^0.513.0", "mapbox-gl": "^3.13.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "tailwindcss": "^4.1.8", "three": "^0.178.0", "three-stdlib": "^2.36.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}