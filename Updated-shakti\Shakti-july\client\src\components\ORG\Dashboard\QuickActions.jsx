import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  MapPin, 
  Activity, 
  Settings, 
  Download, 
  AlertTriangle,
  Users,
  BarChart3
} from 'lucide-react';

const QuickActions = () => {
  const navigate = useNavigate();

  const actions = [
    {
      id: 1,
      title: 'Add New Drone',
      description: 'Register a new drone to the fleet',
      icon: <Plus size={24} />,
      color: 'bg-blue-500',
      hoverColor: 'hover:bg-blue-600',
      onClick: () => navigate('/orgadddrone')
    },
    {
      id: 2,
      title: 'View Full Map',
      description: 'Access detailed tracking map',
      icon: <MapPin size={24} />,
      color: 'bg-green-500',
      hoverColor: 'hover:bg-green-600',
      onClick: () => navigate('/dronetrackingpage')
    },
    {
      id: 3,
      title: 'Live Monitoring',
      description: 'Real-time drone activities',
      icon: <Activity size={24} />,
      color: 'bg-purple-500',
      hoverColor: 'hover:bg-purple-600',
      onClick: () => navigate('/orgdronepage')
    },
    {
      id: 4,
      title: 'System Settings',
      description: 'Configure dashboard preferences',
      icon: <Settings size={24} />,
      color: 'bg-gray-500',
      hoverColor: 'hover:bg-gray-600',
      onClick: () => console.log('Settings clicked')
    },
    {
      id: 5,
      title: 'Export Reports',
      description: 'Download analytics and reports',
      icon: <Download size={24} />,
      color: 'bg-indigo-500',
      hoverColor: 'hover:bg-indigo-600',
      onClick: () => console.log('Export clicked')
    },
    {
      id: 6,
      title: 'Alert Center',
      description: 'View system alerts and warnings',
      icon: <AlertTriangle size={24} />,
      color: 'bg-orange-500',
      hoverColor: 'hover:bg-orange-600',
      onClick: () => navigate('/orgnotification')
    },
    {
      id: 7,
      title: 'Team Management',
      description: 'Manage pilots and operators',
      icon: <Users size={24} />,
      color: 'bg-teal-500',
      hoverColor: 'hover:bg-teal-600',
      onClick: () => console.log('Team management clicked')
    },
    {
      id: 8,
      title: 'Analytics',
      description: 'Detailed performance analytics',
      icon: <BarChart3 size={24} />,
      color: 'bg-pink-500',
      hoverColor: 'hover:bg-pink-600',
      onClick: () => console.log('Analytics clicked')
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 sm:p-4 lg:p-6">
      {/* Header - Mobile: Stack vertically, Desktop: Side by side */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 gap-2 sm:gap-0">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800">Quick Actions</h2>
        <span className="text-xs sm:text-sm text-gray-500">Frequently used functions</span>
      </div>

      {/* Actions Grid - Responsive: Mobile 2 cols, Tablet 4 cols, Desktop 8 cols */}
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2 sm:gap-3 lg:gap-4">
        {actions.map((action) => (
          <button
            key={action.id}
            onClick={action.onClick}
            className="group flex flex-col items-center p-2 sm:p-3 lg:p-4 rounded-xl border border-gray-200 hover:border-transparent hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 min-h-[80px] sm:min-h-[100px] lg:min-h-[120px]"
          >
            {/* Icon */}
            <div className={`${action.color} ${action.hoverColor} text-white p-2 sm:p-2.5 lg:p-3 rounded-lg sm:rounded-xl mb-2 sm:mb-3 transition-colors duration-300 group-hover:scale-110 transform`}>
              <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6">
                {action.icon}
              </div>
            </div>

            {/* Title */}
            <h3 className="text-xs sm:text-sm font-semibold text-gray-800 text-center mb-1 group-hover:text-gray-900 leading-tight">
              {action.title}
            </h3>

            {/* Description - Hide on mobile, show on larger screens */}
            <p className="hidden sm:block text-xs text-gray-500 text-center leading-tight">
              {action.description}
            </p>
          </button>
        ))}
      </div>

      {/* Mobile: Show description as tooltip or in a separate section */}
      <div className="sm:hidden mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          Tap any action to access frequently used functions
        </p>
      </div>
    </div>
  );
};

export default QuickActions;
