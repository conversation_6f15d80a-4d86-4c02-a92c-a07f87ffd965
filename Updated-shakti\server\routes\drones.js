const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

// Import middleware
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

// Import controller
const droneController = require('../controllers/droneController');

// Validation middleware for drone operations
const createDroneValidation = [
  body('name')
    .notEmpty()
    .withMessage('Drone name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Drone name must be between 2 and 100 characters'),

  body('serialNumber')
    .notEmpty()
    .withMessage('Serial number is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Serial number must be between 3 and 50 characters'),

  body('registrationNumber')
    .notEmpty()
    .withMessage('Registration number is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Registration number must be between 3 and 50 characters'),

  body('model')
    .notEmpty()
    .withMessage('Model is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Model must be between 2 and 100 characters'),

  body('manufacturer')
    .notEmpty()
    .withMessage('Manufacturer is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Manufacturer must be between 2 and 100 characters'),

  body('specifications.type')
    .notEmpty()
    .withMessage('Drone type is required')
    .isIn(['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'])
    .withMessage('Invalid drone type'),

  body('specifications.weight')
    .isFloat({ min: 0.1, max: 25 })
    .withMessage('Weight must be between 0.1 and 25 kg'),

  body('specifications.maxPayload')
    .isFloat({ min: 0, max: 10 })
    .withMessage('Maximum payload must be between 0 and 10 kg'),

  body('specifications.maxFlightTime')
    .isInt({ min: 1, max: 300 })
    .withMessage('Maximum flight time must be between 1 and 300 minutes'),

  body('specifications.maxRange')
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Maximum range must be between 0.1 and 100 km'),

  body('specifications.maxAltitude')
    .isInt({ min: 1, max: 500 })
    .withMessage('Maximum altitude must be between 1 and 500 meters'),

  body('specifications.maxSpeed')
    .isInt({ min: 1, max: 200 })
    .withMessage('Maximum speed must be between 1 and 200 km/h'),

  body('specifications.batteryCapacity')
    .isInt({ min: 100, max: 50000 })
    .withMessage('Battery capacity must be between 100 and 50000 mAh'),

  body('status')
    .optional()
    .isIn(['active', 'inactive', 'maintenance', 'retired', 'lost'])
    .withMessage('Invalid status'),

  body('condition')
    .optional()
    .isIn(['excellent', 'good', 'fair', 'poor', 'damaged'])
    .withMessage('Invalid condition'),

  body('organizationId')
    .optional()
    .isMongoId()
    .withMessage('Invalid organization ID format'),

  body('purchase.purchaseDate')
    .isISO8601()
    .withMessage('Invalid purchase date format'),

  body('purchase.purchasePrice')
    .isFloat({ min: 0 })
    .withMessage('Purchase price must be a positive number'),

  body('purchase.vendor')
    .notEmpty()
    .withMessage('Vendor is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Vendor must be between 2 and 100 characters')
];

const updateDroneValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid drone ID format'),

  body('serialNumber')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('Serial number must be between 3 and 50 characters'),

  body('model')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Model must be between 2 and 100 characters'),

  body('specifications.type')
    .optional()
    .isIn(['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'])
    .withMessage('Invalid drone type')
];

const getDroneValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid drone ID format')
];

const statusUpdateValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid drone ID format'),

  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['active', 'inactive', 'maintenance', 'retired'])
    .withMessage('Invalid status. Must be one of: active, inactive, maintenance, retired')
];

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('sortBy')
    .optional()
    .isIn(['serialNumber', 'model', 'manufacturer', 'status', 'createdAt', 'updatedAt'])
    .withMessage('Invalid sort field'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),

  query('status')
    .optional()
    .isIn(['active', 'inactive', 'maintenance', 'retired'])
    .withMessage('Invalid status filter'),

  query('organizationId')
    .optional()
    .isMongoId()
    .withMessage('Invalid organization ID format')
];

// Routes

// GET /api/drones - Get all drones with filtering and pagination
router.get('/', 
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  queryValidation,
  validation.handleValidationErrors,
  droneController.getAllDrones
);

// GET /api/drones/stats - Get drone statistics
router.get('/stats',
  auth.authenticate,
  auth.authorize(['admin']),
  droneController.getDroneStats
);

// GET /api/drones/analytics - Get drone analytics data (public access for development)
router.get('/analytics',
  // Temporarily remove auth for development - uncomment below for production
  // auth.authenticate,
  // auth.authorize(['admin']),
  droneController.getDroneAnalytics
);

// GET /api/drones/:id - Get drone by ID
router.get('/:id',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  getDroneValidation,
  validation.handleValidationErrors,
  droneController.getDroneById
);

// POST /api/drones - Create new drone
router.post('/',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  createDroneValidation,
  validation.handleValidationErrors,
  droneController.createDrone
);

// PUT /api/drones/:id - Update drone
router.put('/:id',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  updateDroneValidation,
  validation.handleValidationErrors,
  droneController.updateDrone
);

// PATCH /api/drones/:id/status - Update drone status
router.patch('/:id/status',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  statusUpdateValidation,
  validation.handleValidationErrors,
  droneController.updateDroneStatus
);

// DELETE /api/drones/:id - Delete drone
router.delete('/:id',
  auth.authenticate,
  auth.authorize(['admin']),
  getDroneValidation,
  validation.handleValidationErrors,
  droneController.deleteDrone
);

module.exports = router;
