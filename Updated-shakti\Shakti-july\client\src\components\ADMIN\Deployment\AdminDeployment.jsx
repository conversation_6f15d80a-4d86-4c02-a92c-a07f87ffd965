import React, { useState, useMemo, useEffect } from 'react';

import {
  Bell,
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw,
  TrendingUp,
  Activity,
  AlertTriangle,
  CheckCircle,
  Settings,
  MoreVertical,
  Calendar,
  MapPin,
  Users,
  Clock
} from 'lucide-react';

// React Icons imports
import {
  FaRocket,
  FaPlane,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
  FaClock,
  FaMapMarkerAlt,
  FaBuilding,
  FaCalendarAlt,
  FaEye,
  FaEdit,
  FaTrash,
  FaUnlock,
  FaLock,
  FaChartLine,
  FaUsers,
  FaCog,
  FaCloudUploadAlt,
  FaFileExport
} from 'react-icons/fa';

import { useNavigate } from 'react-router-dom';
import DroneDeployment from './DroneDeployment';
import DroneFilterHeader from './DroneFilterHeader';
import AdminSidebar from '../common/AdminSidebar';

const AdminDeployment = () => {
  const navigate = useNavigate();

  // Quick Actions handlers
  const handleViewAnalytics = () => {
    console.log('AdminDeployment: Navigating to deployment analytics');
    navigate('/deployment-analytics');
  };

  const handleScheduleDeployment = () => {
    console.log('AdminDeployment: Navigating to schedule deployment');
    navigate('/schedule-deployment');
  };

  const handleManageZones = () => {
    console.log('AdminDeployment: Navigating to manage zones');
    navigate('/manage-zones');
  };

  const handleDeploymentSettings = () => {
    console.log('AdminDeployment: Navigating to deployment settings');
    navigate('/deployment-settings');
  };
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [locationFilter, setLocationFilter] = useState('All');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Mock deployment analytics data
  const deploymentStats = useMemo(() => ({
    totalRequests: 156,
    pendingApprovals: 23,
    activeDeployments: 89,
    completedToday: 12,
    avgResponseTime: '2.3 hrs',
    successRate: 94.5,
    lastUpdated: new Date().toLocaleTimeString()
  }), []);

  const handleRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />

      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header Section */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <FaRocket className="text-blue-600" />
                Drone Deployment
              </h2>
              <p className="text-gray-600 mt-1">Manage deployment requests and monitor active missions</p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw size={18} className={refreshing ? 'animate-spin' : ''} />
                <span className="hidden sm:inline">Refresh</span>
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Plus size={18} />
                <span className="hidden sm:inline">New Request</span>
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Download size={18} />
                <span className="hidden sm:inline">Export</span>
              </button>
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                <Bell size={18} />
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Dashboard */}
        <div className="px-4 lg:px-6 py-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Requests</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{deploymentStats.totalRequests}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FaRocket className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-600 font-medium">+12%</span>
                <span className="text-gray-500 ml-1">from last week</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-3xl font-bold text-orange-600 mt-2">{deploymentStats.pendingApprovals}</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <FaClock className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Awaiting approval</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-3xl font-bold text-green-600 mt-2">{deploymentStats.activeDeployments}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FaCheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Currently deployed</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed Today</p>
                  <p className="text-3xl font-bold text-purple-600 mt-2">{deploymentStats.completedToday}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Missions completed</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Response</p>
                  <p className="text-3xl font-bold text-blue-600 mt-2">{deploymentStats.avgResponseTime}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Average approval time</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-3xl font-bold text-green-600 mt-2">{deploymentStats.successRate}%</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Activity className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Mission success rate</span>
              </div>
            </div>
          </div>

          {/* Enhanced Filter Header */}
          <DroneFilterHeader
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            locationFilter={locationFilter}
            setLocationFilter={setLocationFilter}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
            showFilters={showFilters}
            setShowFilters={setShowFilters}
          />

          {/* Enhanced Deployment Table */}
          <DroneDeployment
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            locationFilter={locationFilter}
            sortBy={sortBy}
            sortOrder={sortOrder}
          />

          {/* Quick Actions Panel */}
          <div className="mt-6 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaCog className="text-blue-600" />
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={handleScheduleDeployment}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 group"
              >
                <FaCloudUploadAlt className="text-blue-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-blue-700">Schedule Deploy</div>
                  <div className="text-sm text-gray-500">Schedule new deployment</div>
                </div>
              </button>
              <button
                onClick={handleViewAnalytics}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-300 transition-all duration-200 group"
              >
                <FaChartLine className="text-green-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-green-700">Analytics</div>
                  <div className="text-sm text-gray-500">View deployment reports</div>
                </div>
              </button>
              <button
                onClick={handleManageZones}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-orange-50 hover:border-orange-300 transition-all duration-200 group"
              >
                <FaUsers className="text-orange-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-orange-700">Manage Zones</div>
                  <div className="text-sm text-gray-500">Configure deployment zones</div>
                </div>
              </button>
              <button
                onClick={handleDeploymentSettings}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 group"
              >
                <Settings className="text-purple-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-purple-700">Settings</div>
                  <div className="text-sm text-gray-500">Configure deployment</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDeployment;
