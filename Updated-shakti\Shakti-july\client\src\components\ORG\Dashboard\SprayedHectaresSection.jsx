import React, { useState, useEffect } from 'react';
import {
  Droplets,
  TrendingUp,
  TrendingDown,
  Target,
  Calendar,
  MapPin,
  BarChart3,
  Activity,
  Award,
  Clock,
  Zap,
  CheckCircle
} from 'lucide-react';
import SprayedHectaresChart from './SprayedHectaresChart';

const SprayedHectaresSection = () => {
  const [timeRange, setTimeRange] = useState('month');
  const [selectedMetric, setSelectedMetric] = useState('hectares');
  const [stats, setStats] = useState({
    totalHectares: 2847,
    monthlyTarget: 3200,
    dailyAverage: 94.9,
    efficiency: 89.2,
    completedMissions: 156,
    activeMissions: 8,
    costPerHectare: 245,
    fuelSaved: 23.5
  });

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalHectares: prev.totalHectares + Math.floor(Math.random() * 5),
        dailyAverage: Math.max(80, Math.min(120, prev.dailyAverage + (Math.random() - 0.5) * 2)),
        efficiency: Math.max(85, Math.min(95, prev.efficiency + (Math.random() - 0.5) * 1)),
        activeMissions: Math.max(5, Math.min(15, prev.activeMissions + Math.floor(Math.random() * 3 - 1)))
      }));
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const progressPercentage = (stats.totalHectares / stats.monthlyTarget) * 100;

  const quickStats = [
    {
      label: 'Total Sprayed',
      value: `${stats.totalHectares.toLocaleString()}`,
      unit: 'hectares',
      change: '+12.5%',
      trend: 'up',
      icon: <Droplets className="w-5 h-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      label: 'Daily Average',
      value: stats.dailyAverage.toFixed(1),
      unit: 'ha/day',
      change: '+8.2%',
      trend: 'up',
      icon: <BarChart3 className="w-5 h-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      label: 'Efficiency',
      value: stats.efficiency.toFixed(1),
      unit: '%',
      change: '+2.1%',
      trend: 'up',
      icon: <Zap className="w-5 h-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      label: 'Active Missions',
      value: stats.activeMissions,
      unit: 'ongoing',
      change: '+3',
      trend: 'up',
      icon: <Activity className="w-5 h-5" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ];

  const achievements = [
    {
      title: 'Monthly Target',
      progress: progressPercentage,
      current: stats.totalHectares,
      target: stats.monthlyTarget,
      icon: <Target className="w-4 h-4" />,
      color: progressPercentage >= 100 ? 'text-green-600' : 'text-blue-600',
      bgColor: progressPercentage >= 100 ? 'bg-green-500' : 'bg-blue-500'
    },
    {
      title: 'Missions Completed',
      progress: 87.5,
      current: stats.completedMissions,
      target: 180,
      icon: <CheckCircle className="w-4 h-4" />,
      color: 'text-green-600',
      bgColor: 'bg-green-500'
    },
    {
      title: 'Cost Efficiency',
      progress: 92.3,
      current: `₹${stats.costPerHectare}`,
      target: '₹200',
      icon: <Award className="w-4 h-4" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-500'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
            <Droplets className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Sprayed Hectares Analytics</h2>
            <p className="text-gray-600">Comprehensive coverage and performance metrics</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-200 rounded-lg text-sm font-medium text-gray-700 bg-white hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          
          <div className="flex items-center gap-2 px-3 py-2 bg-green-50 rounded-lg border border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full pulse-soft"></div>
            <span className="text-sm font-medium text-green-700">Live Data</span>
          </div>
        </div>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} ${stat.borderColor} border rounded-2xl p-6 stat-card-hover group cursor-pointer`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 bg-white rounded-lg shadow-sm ${stat.color}`}>
                {stat.icon}
              </div>
              <div className={`flex items-center gap-1 text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.trend === 'up' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {stat.change}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-gray-800">{stat.value}</span>
                <span className="text-sm text-gray-500">{stat.unit}</span>
              </div>
              <p className="text-sm text-gray-600">{stat.label}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Chart Section - Takes 2 columns */}
        <div className="xl:col-span-2">
          <SprayedHectaresChart />
        </div>

        {/* Achievements & Progress - Takes 1 column */}
        <div className="space-y-6">
          {/* Progress Cards */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-br from-green-500 to-green-600 rounded-lg">
                <Target className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">Progress Tracking</h3>
            </div>

            <div className="space-y-4">
              {achievements.map((achievement, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`p-1 rounded ${achievement.color}`}>
                        {achievement.icon}
                      </div>
                      <span className="text-sm font-medium text-gray-700">{achievement.title}</span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {achievement.current} / {achievement.target}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div
                      className={`h-2 rounded-full progress-bar-animated ${achievement.bgColor} relative`}
                      style={{ width: `${Math.min(100, achievement.progress)}%` }}
                    >
                      <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{achievement.progress.toFixed(1)}% Complete</span>
                    <span>{achievement.progress >= 100 ? 'Target Achieved!' : 'In Progress'}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Insights */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">Key Insights</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700">Avg. Mission Time</span>
                </div>
                <span className="text-sm font-bold text-blue-600">2.4 hrs</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Droplets className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-gray-700">Fuel Efficiency</span>
                </div>
                <span className="text-sm font-bold text-green-600">{stats.fuelSaved}% saved</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <MapPin className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium text-gray-700">Coverage Area</span>
                </div>
                <span className="text-sm font-bold text-purple-600">12 States</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SprayedHectaresSection;
