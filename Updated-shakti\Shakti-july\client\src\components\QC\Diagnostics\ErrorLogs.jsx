import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  AlertCircle,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Bug,
  Zap,
  Wifi,
  Database,
  Code,
  Download
} from 'lucide-react';

const ErrorLogs = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [errorLogs, setErrorLogs] = useState([]);

  // Initialize with sample error log data
  React.useEffect(() => {
    if (errorLogs.length === 0) {
      setErrorLogs([
        {
          id: 'ERR-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          timestamp: '2024-01-15 14:32:15',
          severity: 'Critical',
          category: 'System',
          errorCode: 'SYS_001',
          title: 'Motor Controller Failure',
          description: 'Motor controller #2 has stopped responding to commands',
          location: 'Flight Mission Area B',
          flightId: 'FLT-2024-0156',
          technician: 'Auto-Detected',
          status: 'Open',
          stackTrace: 'MotorController.cpp:245\nDroneSystem.cpp:1023\nFlightControl.cpp:567',
          affectedSystems: ['Motors', 'Flight Control', 'Safety Systems'],
          resolution: '',
          resolvedBy: '',
          resolvedAt: '',
          occurrences: 1,
          firstOccurrence: '2024-01-15 14:32:15',
          lastOccurrence: '2024-01-15 14:32:15'
        },
        {
          id: 'ERR-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          timestamp: '2024-01-15 11:45:22',
          severity: 'Warning',
          category: 'Communication',
          errorCode: 'COM_003',
          title: 'Signal Strength Degradation',
          description: 'Communication signal strength dropped below threshold during flight',
          location: 'Remote Survey Site',
          flightId: 'FLT-2024-0154',
          technician: 'Sarah Johnson',
          status: 'In Progress',
          stackTrace: 'CommModule.cpp:189\nTelemetry.cpp:445\nDataLink.cpp:223',
          affectedSystems: ['Communication', 'Telemetry', 'Data Transmission'],
          resolution: 'Antenna alignment adjustment in progress',
          resolvedBy: '',
          resolvedAt: '',
          occurrences: 3,
          firstOccurrence: '2024-01-14 16:20:10',
          lastOccurrence: '2024-01-15 11:45:22'
        },
        {
          id: 'ERR-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          timestamp: '2024-01-14 09:15:33',
          severity: 'Info',
          category: 'Sensor',
          errorCode: 'SNS_012',
          title: 'GPS Accuracy Warning',
          description: 'GPS accuracy temporarily reduced due to satellite visibility',
          location: 'Urban Mapping Zone',
          flightId: 'FLT-2024-0152',
          technician: 'Mike Wilson',
          status: 'Resolved',
          stackTrace: 'GPSModule.cpp:334\nNavigation.cpp:678\nPositioning.cpp:123',
          affectedSystems: ['GPS', 'Navigation', 'Positioning'],
          resolution: 'GPS accuracy restored after moving to open area',
          resolvedBy: 'Mike Wilson',
          resolvedAt: '2024-01-14 09:28:45',
          occurrences: 1,
          firstOccurrence: '2024-01-14 09:15:33',
          lastOccurrence: '2024-01-14 09:15:33'
        },
        {
          id: 'ERR-2024-004',
          droneId: 'DRN-002',
          droneName: 'Inspector Delta',
          timestamp: '2024-01-13 16:42:18',
          severity: 'Error',
          category: 'Storage',
          errorCode: 'STG_007',
          title: 'Storage Write Failure',
          description: 'Failed to write flight data to storage device',
          location: 'Industrial Inspection Site',
          flightId: 'FLT-2024-0148',
          technician: 'John Smith',
          status: 'Resolved',
          stackTrace: 'StorageManager.cpp:156\nDataLogger.cpp:289\nFileSystem.cpp:445',
          affectedSystems: ['Storage', 'Data Logging', 'File System'],
          resolution: 'Storage device replaced and data recovery completed',
          resolvedBy: 'John Smith',
          resolvedAt: '2024-01-13 18:15:30',
          occurrences: 2,
          firstOccurrence: '2024-01-13 16:42:18',
          lastOccurrence: '2024-01-13 16:45:22'
        },
        {
          id: 'ERR-2024-005',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          timestamp: '2024-01-12 13:28:44',
          severity: 'Warning',
          category: 'Battery',
          errorCode: 'BAT_004',
          title: 'Battery Temperature High',
          description: 'Battery temperature exceeded normal operating range',
          location: 'Desert Survey Area',
          flightId: 'FLT-2024-0145',
          technician: 'Auto-Detected',
          status: 'Resolved',
          stackTrace: 'BatteryMonitor.cpp:78\nPowerManagement.cpp:234\nThermalControl.cpp:167',
          affectedSystems: ['Battery', 'Power Management', 'Thermal Control'],
          resolution: 'Flight paused for cooling, resumed after temperature normalized',
          resolvedBy: 'System Auto-Recovery',
          resolvedAt: '2024-01-12 13:45:12',
          occurrences: 1,
          firstOccurrence: '2024-01-12 13:28:44',
          lastOccurrence: '2024-01-12 13:28:44'
        }
      ]);
    }
  }, [errorLogs.length]);

  // Delete error log function
  const handleDeleteErrorLog = (id) => {
    setErrorLogs(errorLogs.filter(log => log.id !== id));
  };

  // Update error log status
  const handleUpdateStatus = (id, newStatus) => {
    setErrorLogs(errorLogs.map(log => 
      log.id === id ? { ...log, status: newStatus } : log
    ));
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Critical': return 'bg-red-100 text-red-700 border-red-200';
      case 'Error': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Info': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'Critical': return <XCircle className="w-4 h-4" />;
      case 'Error': return <AlertCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      case 'Info': return <CheckCircle className="w-4 h-4" />;
      default: return <Bug className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Resolved': return 'bg-green-100 text-green-700 border-green-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Open': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'System': return <Zap className="w-3 h-3" />;
      case 'Communication': return <Wifi className="w-3 h-3" />;
      case 'Storage': return <Database className="w-3 h-3" />;
      case 'Sensor': return <AlertCircle className="w-3 h-3" />;
      case 'Battery': return <Zap className="w-3 h-3" />;
      default: return <Code className="w-3 h-3" />;
    }
  };

  const filteredErrorLogs = errorLogs.filter(log => {
    const matchesSearch = log.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.errorCode.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSeverity = filterSeverity === 'all' || log.severity.toLowerCase() === filterSeverity.toLowerCase();
    const matchesCategory = filterCategory === 'all' || log.category.toLowerCase() === filterCategory.toLowerCase();
    const matchesDrone = filterDrone === 'all' || log.droneId === filterDrone;
    return matchesSearch && matchesSeverity && matchesCategory && matchesDrone;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search error logs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterSeverity}
        onChange={(e) => setFilterSeverity(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Severity</option>
        <option value="critical">Critical</option>
        <option value="error">Error</option>
        <option value="warning">Warning</option>
        <option value="info">Info</option>
      </select>

      <select
        value={filterCategory}
        onChange={(e) => setFilterCategory(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Categories</option>
        <option value="system">System</option>
        <option value="communication">Communication</option>
        <option value="storage">Storage</option>
        <option value="sensor">Sensor</option>
        <option value="battery">Battery</option>
      </select>

      <button
        className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2"
      >
        <Download className="w-4 h-4" />
        Export Logs
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Error Logs & Diagnostics"
      subtitle="Monitor and analyze system errors and diagnostic information"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Errors</p>
                <p className="text-2xl font-bold text-gray-900">{filteredErrorLogs.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <AlertCircle className="w-3 h-3" />
                  Logged
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <AlertCircle className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredErrorLogs.filter(l => l.severity === 'Critical').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <XCircle className="w-3 h-3" />
                  Immediate attention
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <XCircle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Open Issues</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredErrorLogs.filter(l => l.status === 'Open' || l.status === 'In Progress').length}
                </p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Needs resolution
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredErrorLogs.filter(l => l.status === 'Resolved').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Fixed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Error Logs Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Error Log Records</h3>
                <p className="text-sm text-gray-600 mt-1">System errors and diagnostic information</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Error Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Severity & Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Affected Systems
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status & Resolution
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredErrorLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                    {/* Error Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Bug className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{log.id}</h4>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                              {log.errorCode}
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{log.title}</p>
                            <p className="text-xs text-gray-600">{log.description}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {log.timestamp}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {log.location}
                              </span>
                              <span>Flight: {log.flightId}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Severity & Category */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getSeverityColor(log.severity)}`}>
                          {getSeverityIcon(log.severity)}
                          <span className="ml-1">{log.severity}</span>
                        </span>
                        <div className="flex items-center gap-1">
                          {getCategoryIcon(log.category)}
                          <span className="text-xs text-gray-600">{log.category}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Drone: {log.droneId}</p>
                          <p>{log.droneName}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Occurrences: {log.occurrences}</p>
                        </div>
                      </div>
                    </td>

                    {/* Affected Systems */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        {log.affectedSystems.slice(0, 3).map((system, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <div className="w-2 h-2 rounded-full bg-red-400"></div>
                            <span className="text-xs text-gray-700">{system}</span>
                          </div>
                        ))}
                        {log.affectedSystems.length > 3 && (
                          <p className="text-xs text-gray-500">+{log.affectedSystems.length - 3} more systems</p>
                        )}
                      </div>
                    </td>

                    {/* Status & Resolution */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(log.status)}`}>
                          {log.status}
                        </span>
                        {log.resolution && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Resolution:</p>
                            <p className="text-xs text-gray-700">{log.resolution}</p>
                          </div>
                        )}
                        {log.resolvedBy && (
                          <div className="text-xs text-gray-500">
                            <p>Resolved by: {log.resolvedBy}</p>
                            <p>At: {log.resolvedAt}</p>
                          </div>
                        )}
                        {log.technician && log.technician !== 'Auto-Detected' && (
                          <div className="text-xs text-gray-500">
                            <p>Assigned: {log.technician}</p>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-diagnostics/error-logs/${log.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={log.status}
                          onChange={(e) => handleUpdateStatus(log.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Open">Open</option>
                          <option value="In Progress">In Progress</option>
                          <option value="Resolved">Resolved</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteErrorLog(log.id)}
                          title="Delete Log"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default ErrorLogs;
