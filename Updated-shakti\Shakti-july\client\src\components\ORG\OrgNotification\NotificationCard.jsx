import React, { useState } from 'react';
import {
    Clock,
    MapPin,
    User,
    Battery,
    Thermometer,
    Gauge,
    Mountain,
    MoreVertical,
    Eye,
    Archive,
    Trash2,
    CheckCircle,
    Circle,
    AlertTriangle,
    Info,
    CheckCircle2,
    Zap,
    Shield,
    Activity,
    Users,
    Plane,
    Wrench
} from 'lucide-react';

const NotificationCard = ({ 
    notification, 
    isSelected, 
    onSelect, 
    onMarkAsRead, 
    viewMode = 'list' 
}) => {
    const [showDetails, setShowDetails] = useState(false);
    const [showActions, setShowActions] = useState(false);

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'critical': return 'border-red-500 bg-red-50';
            case 'high': return 'border-orange-500 bg-orange-50';
            case 'medium': return 'border-yellow-500 bg-yellow-50';
            case 'low': return 'border-blue-500 bg-blue-50';
            default: return 'border-gray-300 bg-white';
        }
    };

    const getIconColor = (color) => {
        switch (color) {
            case 'red': return 'text-red-600 bg-red-100';
            case 'orange': return 'text-orange-600 bg-orange-100';
            case 'yellow': return 'text-yellow-600 bg-yellow-100';
            case 'green': return 'text-green-600 bg-green-100';
            case 'blue': return 'text-blue-600 bg-blue-100';
            case 'purple': return 'text-purple-600 bg-purple-100';
            case 'indigo': return 'text-indigo-600 bg-indigo-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const formatTimeAgo = (timestamp) => {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        return `${days}d ago`;
    };

    const IconComponent = notification.icon;

    if (viewMode === 'card') {
        return (
            <div
                className={`notification-card relative bg-white rounded-xl shadow-sm border-l-4 hover:shadow-lg transition-all duration-300  ${
                    getPriorityColor(notification.priority)
                } ${isSelected ? 'ring-2 ring-blue-500' : ''} ${!notification.isRead ? 'bg-white border-blue-200' : 'bg-white'}`}
                style={{ backgroundColor: 'white', background: 'white', color: '#000000' }}
            >
                
                {/* Card Header */}
                <div className="p-4 pb-3">
                    <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                            {/* Selection Checkbox */}
                            <button
                                onClick={() => onSelect(notification.id)}
                                className="mt-1 p-1 hover:bg-gray-100 rounded transition-colors"
                            >
                                {isSelected ? (
                                    <CheckCircle className="w-4 h-4 text-blue-600" />
                                ) : (
                                    <Circle className="w-4 h-4 text-gray-400" />
                                )}
                            </button>

                            {/* Icon */}
                            <div className={`p-2 rounded-lg ${getIconColor(notification.color)}`}>
                                <IconComponent className="w-5 h-5" />
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <h3 className="font-semibold text-gray-900 truncate">
                                        {notification.type}
                                    </h3>
                                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                                        notification.priority === 'critical' ? 'bg-red-100 text-red-800' :
                                        notification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                        notification.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-blue-100 text-blue-800'
                                    }`}>
                                        {notification.priority}
                                    </span>
                                    {!notification.isRead && (
                                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                    )}
                                </div>
                                <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                                    {notification.description}
                                </p>
                                <div className="flex items-center gap-4 text-xs text-gray-500">
                                    <span className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {formatTimeAgo(notification.timestamp)}
                                    </span>
                                    <span className="flex items-center gap-1">
                                        <Plane className="w-3 h-3" />
                                        {notification.droneId}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {/* Actions Menu */}
                        <div className="relative">
                            <button
                                onClick={() => setShowActions(!showActions)}
                                className="p-1 hover:bg-gray-100 rounded transition-colors"
                            >
                                <MoreVertical className="w-4 h-4 text-gray-400" />
                            </button>
                            
                            {showActions && (
                                <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                                    <button
                                        onClick={() => {
                                            onMarkAsRead(notification.id, !notification.isRead);
                                            setShowActions(false);
                                        }}
                                        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                                    >
                                        <Eye className="w-3 h-3" />
                                        {notification.isRead ? 'Mark Unread' : 'Mark Read'}
                                    </button>
                                    <button className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                                        <Archive className="w-3 h-3" />
                                        Archive
                                    </button>
                                    <button className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2">
                                        <Trash2 className="w-3 h-3" />
                                        Delete
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Expandable Details */}
                {showDetails && (
                    <div className="px-4 pb-4 border-t border-gray-100">
                        <div className="pt-3 space-y-2">
                            <div className="grid grid-cols-2 gap-4 text-xs">
                                <div className="flex items-center gap-2">
                                    <User className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600">Pilot:</span>
                                    <span className="font-medium">{notification.pilot}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <MapPin className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600">Location:</span>
                                    <span className="font-medium">{notification.location}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Battery className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600">Battery:</span>
                                    <span className="font-medium">{notification.details.batteryLevel}%</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Mountain className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600">Altitude:</span>
                                    <span className="font-medium">{notification.details.altitude}m</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Toggle Details Button */}
                <button
                    onClick={() => setShowDetails(!showDetails)}
                    className="w-full py-2 text-xs text-blue-600 hover:bg-blue-50 transition-colors border-t border-gray-100"
                >
                    {showDetails ? 'Hide Details' : 'Show Details'}
                </button>
            </div>
        );
    }

    // List view
    return (
        <div
            className={`notification-list-item bg-white rounded-lg border hover:shadow-md transition-all duration-200 text-black ${
                isSelected ? 'ring-2 ring-blue-500' : ''
            } ${!notification.isRead ? 'bg-white border-blue-200' : 'bg-white border-gray-200'}`}
            style={{ backgroundColor: 'white', background: 'white', color: '#000000' }}
        >
            <div className="p-4">
                <div className="flex items-center gap-4">
                    {/* Selection Checkbox */}
                    <button
                        onClick={() => onSelect(notification.id)}
                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                    >
                        {isSelected ? (
                            <CheckCircle className="w-4 h-4 text-blue-600" />
                        ) : (
                            <Circle className="w-4 h-4 text-gray-400" />
                        )}
                    </button>

                    {/* Icon */}
                    <div className={`p-2 rounded-lg ${getIconColor(notification.color)}`}>
                        <IconComponent className="w-4 h-4" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-gray-900">
                                {notification.type}
                            </h3>
                            <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                                notification.priority === 'critical' ? 'bg-red-100 text-red-800' :
                                notification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                notification.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-blue-100 text-blue-800'
                            }`}>
                                {notification.priority}
                            </span>
                            {!notification.isRead && (
                                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                            {notification.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {formatTimeAgo(notification.timestamp)}
                            </span>
                            <span className="flex items-center gap-1">
                                <Plane className="w-3 h-3" />
                                {notification.droneId}
                            </span>
                            <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {notification.pilot}
                            </span>
                            <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {notification.location}
                            </span>
                        </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => onMarkAsRead(notification.id, !notification.isRead)}
                            className="p-2 hover:bg-gray-100 rounded transition-colors"
                            title={notification.isRead ? 'Mark as unread' : 'Mark as read'}
                        >
                            <Eye className={`w-4 h-4 ${notification.isRead ? 'text-gray-400' : 'text-blue-600'}`} />
                        </button>
                        
                        <div className="relative">
                            <button
                                onClick={() => setShowActions(!showActions)}
                                className="p-2 hover:bg-gray-100 rounded transition-colors"
                            >
                                <MoreVertical className="w-4 h-4 text-gray-400" />
                            </button>
                            
                            {showActions && (
                                <div className="absolute right-0 top-10 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                                    <button className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                                        <Archive className="w-3 h-3" />
                                        Archive
                                    </button>
                                    <button className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2">
                                        <Trash2 className="w-3 h-3" />
                                        Delete
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default NotificationCard;
