require('dotenv').config();
const express = require('express');
const morgan = require('morgan');

// Import database connection
const connectDB = require('./config/database');
const { connectOrgDB } = require('./config/organizationDatabase');

// Import middleware
const {
  cors,
  helmetConfig,
  corsError<PERSON><PERSON><PERSON>,
  requestLogger
} = require('./middleware/security');

// Import routes
const apiRoutes = require('./routes');

// Import background jobs
const organizationCleanupJob = require('./jobs/organizationCleanup');

// Initialize Express app
const app = express();

// Connect to database (with fallback for development)
const connectDatabase = async () => {
  try {
    await connectDB();
    console.log('✅ MongoDB Connected Successfully');

    // Connect to organization database
    await connectOrgDB();
    console.log('✅ Organization Database Connected Successfully');

    // Start background jobs after successful database connection
    organizationCleanupJob.start();
  } catch (error) {
    console.log('⚠️  MongoDB Connection Failed - Running in development mode with in-memory storage');
    console.log('📝 Note: Data will not persist between server restarts');
    console.log('📝 Background jobs will not run without database connection');
    // Don't exit the process, continue with in-memory storage
  }
};

connectDatabase();

// Trust proxy (for rate limiting behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(helmetConfig);
app.use(cors);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}
app.use(requestLogger);

// API routes
app.use('/api', apiRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Welcome to SHAKTI Drone Management API',
    version: '1.0.0',
    documentation: '/api/health',
    timestamp: new Date().toISOString()
  });
});

// CORS error handler
app.use(corsErrorHandler);

// Global error handler
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(error => ({
      field: error.path,
      message: error.message
    }));
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors,
      timestamp: new Date().toISOString()
    });
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} already exists`,
      timestamp: new Date().toISOString()
    });
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      timestamp: new Date().toISOString()
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired',
      timestamp: new Date().toISOString()
    });
  }

  // Default error
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  res.status(statusCode).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    timestamp: new Date().toISOString()
  });
});

// 404 handler for non-API routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    timestamp: new Date().toISOString()
  });
});

// Start server
const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`
🚀 SHAKTI Drone Management API Server Started
📍 Environment: ${process.env.NODE_ENV || 'development'}
🌐 Server running on port ${PORT}
📊 Health check: http://localhost:${PORT}/api/health
📚 API Base URL: http://localhost:${PORT}/api
⏰ Started at: ${new Date().toISOString()}
  `);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err.message);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received. Shutting down gracefully...');

  // Stop background jobs
  organizationCleanupJob.stop();

  server.close(() => {
    console.log('✅ Process terminated');
  });
});

module.exports = app;
