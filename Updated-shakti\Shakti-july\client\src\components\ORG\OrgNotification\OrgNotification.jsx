import React, { useEffect, useState } from "react";
import {
    Bell,
    Search,
    RefreshCw,
    Filter,
    Settings,
    MoreVertical,
    CheckCircle,
    Archive,
    Trash2,
    Eye,
    EyeOff,
    AlertTriangle,
    Info,
    CheckCircle2,
    Clock,
    Zap,
    Shield,
    Activity,
    TrendingUp,
    Calendar,
    Users,
    MapPin,
    Battery,
    Plane,
    Wrench
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import OrgSidebar from '../common/OrgSidebar';
import NotificationCard from './NotificationCard';
import NotificationFilters from './NotificationFilters';
import NotificationStats from './NotificationStats';
import NotificationActions from './NotificationActions';
import './notifications.css';

const OrgNotification = () => {
    const navigate = useNavigate();

    // Enhanced state management
    const [notifications, setNotifications] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedNotifications, setSelectedNotifications] = useState([]);
    const [showFilters, setShowFilters] = useState(false);
    const [filterType, setFilterType] = useState('all');
    const [filterPriority, setFilterPriority] = useState('all');
    const [filterStatus, setFilterStatus] = useState('all');
    const [sortOrder, setSortOrder] = useState('newest');
    const [viewMode, setViewMode] = useState('list'); // 'list' or 'card'

    // Enhanced notification types with more details
    const notificationTypes = [
        {
            type: "Critical Alert",
            category: "emergency",
            icon: AlertTriangle,
            color: "red",
            priority: "critical",
            description: "Immediate attention required"
        },
        {
            type: "Drone Crash",
            category: "emergency",
            icon: AlertTriangle,
            color: "red",
            priority: "critical",
            description: "Drone has crashed and needs immediate attention"
        },
        {
            type: "Battery Low",
            category: "warning",
            icon: Battery,
            color: "yellow",
            priority: "high",
            description: "Drone battery level is critically low"
        },
        {
            type: "Mission Completed",
            category: "success",
            icon: CheckCircle2,
            color: "green",
            priority: "medium",
            description: "Mission has been completed successfully"
        },
        {
            type: "Drone Connected",
            category: "info",
            icon: Plane,
            color: "blue",
            priority: "low",
            description: "New drone has been connected to the system"
        },
        {
            type: "Maintenance Required",
            category: "warning",
            icon: Wrench,
            color: "orange",
            priority: "high",
            description: "Scheduled maintenance is due"
        },
        {
            type: "System Update",
            category: "info",
            icon: Info,
            color: "blue",
            priority: "low",
            description: "System update available"
        },
        {
            type: "Weather Alert",
            category: "warning",
            icon: Activity,
            color: "yellow",
            priority: "medium",
            description: "Weather conditions may affect operations"
        },
        {
            type: "Pilot Assignment",
            category: "info",
            icon: Users,
            color: "purple",
            priority: "medium",
            description: "New pilot has been assigned to drone"
        },
        {
            type: "Location Update",
            category: "info",
            icon: MapPin,
            color: "indigo",
            priority: "low",
            description: "Drone location has been updated"
        }
    ];

    // Enhanced notification generation
    const generateNotification = () => {
        const rand = Math.floor(Math.random() * notificationTypes.length);
        const item = notificationTypes[rand];
        const now = new Date();

        // Generate realistic drone and pilot names
        const droneNames = ['ARJUNA-001', 'ARJUNA-002', 'ARJUNA-ADV-003', 'ARJUNA-STD-004', 'ARJUNA-PRO-005'];
        const pilotNames = ['Deepchand Jaiswal', 'Yuvraj Khade', 'Vishal Shelke', 'Om Unge', 'Prathamesh Jadhav'];
        const locations = ['Maharashtra', 'Karnataka', 'Gujarat', 'Punjab', 'Haryana'];

        return {
            id: Date.now() + Math.random(),
            type: item.type,
            category: item.category,
            icon: item.icon,
            color: item.color,
            priority: item.priority,
            description: item.description,
            time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            date: now.toDateString(),
            timestamp: now.getTime(),
            isRead: false,
            isArchived: false,
            droneId: droneNames[Math.floor(Math.random() * droneNames.length)],
            pilot: pilotNames[Math.floor(Math.random() * pilotNames.length)],
            location: locations[Math.floor(Math.random() * locations.length)],
            details: {
                batteryLevel: Math.floor(Math.random() * 100),
                altitude: Math.floor(Math.random() * 200),
                speed: Math.floor(Math.random() * 60),
                temperature: Math.floor(Math.random() * 40) + 10
            }
        };
    };

    // Initialize component and generate initial notifications
    useEffect(() => {
        setIsLoading(true);

        // Generate initial notifications
        const initialNotifications = Array.from({ length: 15 }, (_, i) => {
            const notification = generateNotification();
            // Vary the timestamps to create realistic distribution
            notification.timestamp = Date.now() - (i * 3600000) - Math.random() * 3600000;
            notification.date = new Date(notification.timestamp).toDateString();
            notification.time = new Date(notification.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            // Make some notifications read
            notification.isRead = Math.random() > 0.6;
            return notification;
        });

        setNotifications(initialNotifications);
        setIsLoading(false);

        // Set up real-time notification generation
        const interval = setInterval(() => {
            const newNotif = generateNotification();
            setNotifications(prev => [newNotif, ...prev.slice(0, 49)]);
        }, 8000);

        return () => clearInterval(interval);
    }, []);

    // Enhanced filtering and search logic
    const filteredNotifications = notifications.filter(notification => {
        const matchesSearch = searchQuery === '' ||
            notification.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
            notification.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
            notification.pilot.toLowerCase().includes(searchQuery.toLowerCase()) ||
            notification.location.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesType = filterType === 'all' || notification.category === filterType;
        const matchesPriority = filterPriority === 'all' || notification.priority === filterPriority;
        const matchesStatus = filterStatus === 'all' ||
            (filterStatus === 'read' && notification.isRead) ||
            (filterStatus === 'unread' && !notification.isRead) ||
            (filterStatus === 'archived' && notification.isArchived);

        return matchesSearch && matchesType && matchesPriority && matchesStatus && !notification.isArchived;
    }).sort((a, b) => {
        if (sortOrder === 'newest') return b.timestamp - a.timestamp;
        if (sortOrder === 'oldest') return a.timestamp - b.timestamp;
        if (sortOrder === 'priority') {
            const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return 0;
    });

    // Handle refresh
    const handleRefresh = async () => {
        setRefreshing(true);
        // Simulate refresh delay
        setTimeout(() => setRefreshing(false), 2000);
    };

    // Handle notification selection
    const handleNotificationSelect = (notificationId) => {
        setSelectedNotifications(prev =>
            prev.includes(notificationId)
                ? prev.filter(id => id !== notificationId)
                : [...prev, notificationId]
        );
    };

    // Mark notifications as read/unread
    const markAsRead = (notificationIds, isRead = true) => {
        setNotifications(prev =>
            prev.map(notification =>
                notificationIds.includes(notification.id)
                    ? { ...notification, isRead }
                    : notification
            )
        );
    };

    // Archive notifications
    const archiveNotifications = (notificationIds) => {
        setNotifications(prev =>
            prev.map(notification =>
                notificationIds.includes(notification.id)
                    ? { ...notification, isArchived: true }
                    : notification
            )
        );
        setSelectedNotifications([]);
    };

    // Delete notifications
    const deleteNotifications = (notificationIds) => {
        setNotifications(prev =>
            prev.filter(notification => !notificationIds.includes(notification.id))
        );
        setSelectedNotifications([]);
    };

    // Calculate statistics
    const stats = {
        total: notifications.filter(n => !n.isArchived).length,
        unread: notifications.filter(n => !n.isRead && !n.isArchived).length,
        critical: notifications.filter(n => n.priority === 'critical' && !n.isArchived).length,
        high: notifications.filter(n => n.priority === 'high' && !n.isArchived).length,
        today: notifications.filter(n => n.date === new Date().toDateString() && !n.isArchived).length,
        categories: {
            emergency: notifications.filter(n => n.category === 'emergency' && !n.isArchived).length,
            warning: notifications.filter(n => n.category === 'warning' && !n.isArchived).length,
            success: notifications.filter(n => n.category === 'success' && !n.isArchived).length,
            info: notifications.filter(n => n.category === 'info' && !n.isArchived).length
        }
    };

    return (
        <div className="notification-page-container w-full min-h-screen bg-gray-50 overflow-x-hidden" style={{ filter: 'none', colorScheme: 'normal' }}>
            <OrgSidebar />

            {/* Main Content */}
            <main className="notification-page-main ml-0 lg:ml-72 min-h-screen flex flex-col transition-all duration-300 max-w-full overflow-x-hidden" style={{ color: '#000000', backgroundColor: '#ffffff', filter: 'none' }}>

                {/* Professional Header */}
                <div className="notification-page-header bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40 text-black" style={{ color: '#000000' }}>
                    <div className="px-4 lg:px-6 py-4">
                        <div className="flex flex-col lg:flex-row lg:items-center gap-4 lg:gap-0 lg:justify-between">
                            {/* Search Section */}
                            <div className="flex-1 max-w-full lg:max-w-md">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                                    <input
                                        type="text"
                                        placeholder="Search notifications, drones, pilots..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                    />
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center gap-2 lg:gap-3 lg:ml-6 flex-shrink-0">
                                <button
                                    onClick={handleRefresh}
                                    disabled={refreshing}
                                    className={`inline-flex items-center gap-2 px-3 lg:px-4 py-2 lg:py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                                        refreshing
                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 hover:shadow-md'
                                    }`}
                                >
                                    <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                                    <span className="hidden sm:inline">Refresh</span>
                                </button>

                                <button
                                    onClick={() => setShowFilters(!showFilters)}
                                    className={`inline-flex items-center gap-2 px-3 lg:px-4 py-2 lg:py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                                        showFilters
                                            ? 'bg-blue-600 text-white shadow-lg'
                                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 hover:shadow-md'
                                    }`}
                                >
                                    <Filter className="w-4 h-4" />
                                    <span className="hidden sm:inline">Filters</span>
                                </button>

                                <button className="inline-flex items-center gap-2 px-3 lg:px-4 py-2 lg:py-2.5 rounded-lg font-medium text-sm bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 hover:shadow-md transition-all duration-200">
                                    <Settings className="w-4 h-4" />
                                    <span className="hidden sm:inline">Settings</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content Area */}
                <div className="p-4 lg:p-6 space-y-4 lg:space-y-6 max-w-full overflow-x-hidden flex-1 text-black" style={{ color: '#000000' }}>

                    {/* Statistics Dashboard */}
                    <div className="w-full overflow-x-auto">
                        <NotificationStats stats={stats} />
                    </div>

                    {/* Bulk Actions */}
                    {selectedNotifications.length > 0 && (
                        <div className="w-full overflow-x-auto">
                            <NotificationActions
                                selectedCount={selectedNotifications.length}
                                onMarkAsRead={() => markAsRead(selectedNotifications, true)}
                                onMarkAsUnread={() => markAsRead(selectedNotifications, false)}
                                onArchive={() => archiveNotifications(selectedNotifications)}
                                onDelete={() => deleteNotifications(selectedNotifications)}
                                onClearSelection={() => setSelectedNotifications([])}
                            />
                        </div>
                    )}

                    {/* Advanced Filters */}
                    {showFilters && (
                        <div className="w-full overflow-x-auto">
                            <NotificationFilters
                                filterType={filterType}
                                setFilterType={setFilterType}
                                filterPriority={filterPriority}
                                setFilterPriority={setFilterPriority}
                                filterStatus={filterStatus}
                                setFilterStatus={setFilterStatus}
                                sortOrder={sortOrder}
                                setSortOrder={setSortOrder}
                                viewMode={viewMode}
                                setViewMode={setViewMode}
                            />
                        </div>
                    )}

                    {/* Notifications List */}
                    <div className="space-y-4">
                        {/* Header with count and view toggle */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                            <div className="flex items-center gap-4">
                                <h2 className="text-xl lg:text-2xl font-bold text-gray-900">
                                    Notifications
                                </h2>
                                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                    {filteredNotifications.length} total
                                </span>
                                {stats.unread > 0 && (
                                    <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                                        {stats.unread} unread
                                    </span>
                                )}
                            </div>

                            <div className="flex items-center gap-2">
                                <button
                                    onClick={() => setViewMode('list')}
                                    className={`p-2 rounded-lg transition-all duration-200 ${
                                        viewMode === 'list'
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                                    }`}
                                >
                                    <Eye className="w-4 h-4" />
                                </button>
                                <button
                                    onClick={() => setViewMode('card')}
                                    className={`p-2 rounded-lg transition-all duration-200 ${
                                        viewMode === 'card'
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                                    }`}
                                >
                                    <EyeOff className="w-4 h-4" />
                                </button>
                            </div>
                        </div>

                        {/* Notifications Grid/List */}
                        {isLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span className="ml-3 text-gray-600">Loading notifications...</span>
                            </div>
                        ) : filteredNotifications.length === 0 ? (
                            <div className="text-center py-12">
                                <Bell className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                                <p className="text-gray-500">
                                    {searchQuery || filterType !== 'all' || filterPriority !== 'all' || filterStatus !== 'all'
                                        ? 'Try adjusting your search or filters'
                                        : 'New notifications will appear here'
                                    }
                                </p>
                            </div>
                        ) : (
                            <div className={`${
                                viewMode === 'card'
                                    ? 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6'
                                    : 'space-y-2'
                            }`}>
                                {filteredNotifications.map((notification) => (
                                    <NotificationCard
                                        key={notification.id}
                                        notification={notification}
                                        isSelected={selectedNotifications.includes(notification.id)}
                                        onSelect={handleNotificationSelect}
                                        onMarkAsRead={(id, isRead) => markAsRead([id], isRead)}
                                        viewMode={viewMode}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </main>
        </div>
    );
};

export default OrgNotification;
