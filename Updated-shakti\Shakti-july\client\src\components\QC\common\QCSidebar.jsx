import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Wrench,
  ClipboardCheck,
  Activity,
  BarChart3,
  Settings,
  Bell,
  Search,
  ChevronLeft,
  ChevronRight,
  Shield,
  Users,
  LogOut
} from 'lucide-react';

const QCSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    {
      id: 'dashboard',
      label: 'QC Dashboard',
      icon: LayoutDashboard,
      path: '/qc-dashboard',
      description: 'Maintenance overview and alerts'
    },
    {
      id: 'maintenance',
      label: 'Maintenance',
      icon: Wrench,
      path: '/qc-maintenance',
      description: 'Schedule and track maintenance',
      submenu: [
        { label: 'Scheduler', path: '/qc-maintenance/scheduler' },
        { label: 'Work Orders', path: '/qc-maintenance/work-orders' },
        { label: 'History', path: '/qc-maintenance/history' },
        { label: 'Parts Inventory', path: '/qc-maintenance/inventory' }
      ]
    },
    {
      id: 'inspections',
      label: 'Inspections',
      icon: ClipboardCheck,
      path: '/qc-inspections',
      description: 'Quality control and safety checks',
      submenu: [
        { label: 'Pre-Flight', path: '/qc-inspections/pre-flight' },
        { label: 'Post-Flight', path: '/qc-inspections/post-flight' },
        { label: 'Quality Assessment', path: '/qc-inspections/quality-assessment' },
        { label: 'Compliance', path: '/qc-inspections/compliance' }
      ]
    },
    {
      id: 'diagnostics',
      label: 'Diagnostics',
      icon: Activity,
      path: '/qc-diagnostics',
      description: 'Real-time health monitoring',
      submenu: [
        { label: 'System Health', path: '/qc-diagnostics/system-health' },
        { label: 'Error Logs', path: '/qc-diagnostics/error-logs' },
        { label: 'Performance Analytics', path: '/qc-diagnostics/performance-analytics' }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: BarChart3,
      path: '/qc-reports',
      description: 'Analytics and reporting',
      submenu: [
        { label: 'Flight Reports', path: '/qc-reports/flight-reports' },
        { label: 'Maintenance Reports', path: '/qc-reports/maintenance-reports' },
        { label: 'Compliance Reports', path: '/qc-reports/compliance-reports' }
      ]
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      path: '/qc-settings',
      description: 'QC configuration and preferences',
      subItems: [
        { label: 'General Settings', path: '/qc-settings/general' },
        { label: 'User Management', path: '/qc-settings/users' },
        { label: 'Security Settings', path: '/qc-settings/security' },
        { label: 'System Configuration', path: '/qc-settings/system' }
      ]
    }
  ];

  const [expandedMenu, setExpandedMenu] = useState(null);

  const handleMenuClick = (item) => {
    if (item.submenu) {
      setExpandedMenu(expandedMenu === item.id ? null : item.id);
    } else {
      navigate(item.path);
    }
  };

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleLogout = () => {
    // Clear any stored authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    localStorage.removeItem('droneDraft');

    // Navigate to login page
    navigate('/');
  };

  return (
    <div className={`fixed left-0 top-0 h-screen bg-white shadow-xl border-r border-gray-200 transition-all duration-300 z-50 flex flex-col ${
      isCollapsed ? 'w-16' : 'w-72'
    }`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-25 to-indigo-25" style={{background: 'linear-gradient(to right, #f0f4ff, #f0f2ff)'}}>
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{background: 'linear-gradient(to bottom right, #a5b4fc, #c7d2fe)'}}>
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-gray-800">QC Portal</h2>
                <p className="text-sm text-gray-600">Quality Control</p>
              </div>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1.5 hover:bg-gray-200 rounded-lg transition-colors"
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4 text-gray-600" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 overflow-y-auto py-4 min-h-0">
        <div className="space-y-1 px-3">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isMenuActive = isActive(item.path);
            const isExpanded = expandedMenu === item.id;

            return (
              <div key={item.id}>
                {/* Main Menu Item */}
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`w-full flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200 group ${
                    isMenuActive
                      ? 'text-blue-700 shadow-sm'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  style={isMenuActive ? {background: 'linear-gradient(to right, #dbeafe, #e0e7ff)'} : {}}
                  title={isCollapsed ? item.label : ''}
                >
                  <Icon className={`w-5 h-5 ${isMenuActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}`} />
                  
                  {!isCollapsed && (
                    <>
                      <div className="flex-1 text-left">
                        <div className="font-medium">{item.label}</div>
                        <div className={`text-xs ${isMenuActive ? 'text-blue-500' : 'text-gray-500'}`}>
                          {item.description}
                        </div>
                      </div>
                      
                      {item.submenu && (
                        <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${
                          isExpanded ? 'rotate-90' : ''
                        } ${isMenuActive ? 'text-white' : 'text-gray-400'}`} />
                      )}
                    </>
                  )}
                </button>

                {/* Submenu */}
                {item.submenu && !isCollapsed && isExpanded && (
                  <div className="mt-1 ml-8 space-y-1">
                    {item.submenu.map((subItem) => (
                      <button
                        key={subItem.path}
                        onClick={() => navigate(subItem.path)}
                        className={`w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors ${
                          isActive(subItem.path)
                            ? 'text-blue-600 font-medium'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                        }`}
                        style={isActive(subItem.path) ? {backgroundColor: '#f0f4ff'} : {}}
                      >
                        <div className={`w-2 h-2 rounded-full ${
                          isActive(subItem.path) ? 'bg-blue-400' : 'bg-gray-300'
                        }`} />
                        {subItem.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </nav>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 mt-auto">
        {!isCollapsed && (
          <div className="space-y-3">
            {/* User Info */}
            <div className="flex items-center gap-3 p-2 rounded-lg" style={{backgroundColor: '#f8faff'}}>
              <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{background: 'linear-gradient(to bottom right, #a5b4fc, #c7d2fe)'}}>
                <Users className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-800">QC Team</div>
                <div className="text-xs text-gray-500">Maintenance Dept.</div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <button
                className="flex-1 p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                title="Notifications"
              >
                <Bell className="w-4 h-4 text-gray-600 mx-auto" />
              </button>
              <button
                className="flex-1 p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                title="Search"
              >
                <Search className="w-4 h-4 text-gray-600 mx-auto" />
              </button>
              <button
                onClick={handleLogout}
                className="flex-1 p-2 bg-red-100 hover:bg-red-200 rounded-lg transition-colors"
                title="Logout"
              >
                <LogOut className="w-4 h-4 text-red-600 mx-auto" />
              </button>
            </div>
          </div>
        )}

        {/* Collapsed State Actions */}
        {isCollapsed && (
          <div className="space-y-2">
            <button
              className="w-full p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              title="Notifications"
            >
              <Bell className="w-4 h-4 text-gray-600 mx-auto" />
            </button>
            <button
              className="w-full p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              title="Search"
            >
              <Search className="w-4 h-4 text-gray-600 mx-auto" />
            </button>
            <button
              onClick={handleLogout}
              className="w-full p-2 bg-red-100 hover:bg-red-200 rounded-lg transition-colors"
              title="Logout"
            >
              <LogOut className="w-4 h-4 text-red-600 mx-auto" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default QCSidebar;
