import { useState, useEffect } from 'react';
import EnhancedMapView from './EnhancedMapView';
import DroneDetailsPanel from './DroneDetailsPanel';
import './AdminMapStyles.css';

import {
  Bell,
  Search,
  RefreshCw,
  Loader2,
  Filter,
  MapPin,
  Building,
  User,
  BarChart3
} from 'lucide-react';

import AdminSidebar from '../common/AdminSidebar';
import mapDataService from './MapDataService';
import { MapLoadingSpinner, MapErrorDisplay, SearchLoadingIndicator } from './MapLoadingComponents';

const EnhancedMap = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [viewType, setViewType] = useState('organizations'); // 'organizations', 'individuals'
  const [stats, setStats] = useState({
    totalOrganizations: 0,
    activeOrganizations: 0,
    totalIndividuals: 0,
    approvedIndividuals: 0,
    totalDrones: 0,
    activeDrones: 0
  });
  const [filters, setFilters] = useState({
    state: '',
    status: 'all'
  });

  // Drone details panel state
  const [selectedEntity, setSelectedEntity] = useState(null);
  const [selectedEntityType, setSelectedEntityType] = useState(null);
  const [selectedDrones, setSelectedDrones] = useState([]);
  const [showDroneDetails, setShowDroneDetails] = useState(false);

  // Initialize component
  useEffect(() => {
    document.body.style.overflow = "auto";
    document.body.style.display = "block";
    document.body.style.justifyContent = "unset";
    document.body.style.alignItems = "unset";
    document.body.style.height = "auto";
    document.body.style.background = "#f5f5f5";

    loadInitialData();

    // Subscribe to updates
    const unsubscribeOrgs = mapDataService.subscribe('organizations', () => {
      updateStats();
    });

    const unsubscribeInds = mapDataService.subscribe('individuals', () => {
      updateStats();
    });

    return () => {
      unsubscribeOrgs();
      unsubscribeInds();
    };
  }, []);

  // Handle search with debounce
  useEffect(() => {
    if (searchQuery) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setIsSearching(false);
      }, 800);
      return () => clearTimeout(timer);
    }
  }, [searchQuery]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      // Load both types of data for statistics, but display based on viewType
      await Promise.all([
        mapDataService.fetchOrganizations(),
        mapDataService.fetchIndividuals()
      ]);
      updateStats();
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const updateStats = () => {
    const currentStats = mapDataService.getCombinedStats();
    setStats(currentStats);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Always refresh both for statistics
      await Promise.all([
        mapDataService.fetchOrganizations(filters),
        mapDataService.fetchIndividuals(filters)
      ]);
      updateStats();
    } catch (err) {
      setError(err.message);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleEntitySelect = async (entity, entityType) => {
    setSelectedEntity(entity);
    setSelectedEntityType(entityType);
    
    try {
      let drones = [];
      if (entityType === 'organization') {
        drones = await mapDataService.fetchOrganizationDrones(entity.id);
      } else if (entityType === 'individual') {
        drones = await mapDataService.fetchIndividualDrones(entity.id);
      }
      setSelectedDrones(drones);
      setShowDroneDetails(true);
    } catch (error) {
      console.error('Failed to load drone data:', error);
      setSelectedDrones([]);
      setShowDroneDetails(true);
    }
  };

  const handleCloseDroneDetails = () => {
    setShowDroneDetails(false);
    setSelectedEntity(null);
    setSelectedEntityType(null);
    setSelectedDrones([]);
  };

  const handleRefreshDroneDetails = async () => {
    if (selectedEntity && selectedEntityType) {
      try {
        let drones = [];
        if (selectedEntityType === 'organization') {
          drones = await mapDataService.fetchOrganizationDrones(selectedEntity.id);
        } else if (selectedEntityType === 'individual') {
          drones = await mapDataService.fetchIndividualDrones(selectedEntity.id);
        }
        setSelectedDrones(drones);
      } catch (error) {
        console.error('Failed to refresh drone data:', error);
      }
    }
  };

  if (error) {
    return (
      <div className="min-h-screen w-full bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9]">
        <AdminSidebar />
        <div className="lg:pl-[250px] w-full h-screen">
          <MapErrorDisplay
            title="Failed to load map data"
            message={error}
            onRetry={loadInitialData}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="admin-map-container min-h-screen w-full bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] flex">
      {/* Sidebar */}
      <AdminSidebar />

      {/* Main content */}
      <div className="admin-map-main flex-1 lg:pl-[250px] w-full min-h-screen flex flex-col">
        {/* Header */}
        <div className="admin-header bg-gradient-to-r from-[#91d0f5] to-[#7ab9e3] shadow-md flex-shrink-0">
          <div className="px-4 sm:px-6 py-3">
            {/* Mobile-first responsive header */}
            <div className="flex flex-col space-y-3 lg:space-y-0 lg:flex-row lg:items-center lg:justify-between">

              {/* Left section - Search and Controls */}
              <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:space-x-4 flex-1 lg:flex-none">
                {/* Search Bar */}
                <div className="relative flex-1 sm:flex-none sm:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600" size={18} />
                  <input
                    type="text"
                    placeholder="Search organizations, individuals, states..."
                    className="w-full pl-10 pr-4 py-2.5 rounded-full border-none focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all text-black placeholder-gray-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {isSearching && <SearchLoadingIndicator />}
                </div>

                {/* View Type Toggle */}
                <div className="flex items-center bg-white rounded-lg p-1 shadow-sm">
                  <button
                    onClick={() => setViewType('organizations')}
                    className={`flex items-center gap-2 px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${
                      viewType === 'organizations'
                        ? 'bg-blue-600 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Building className="w-4 h-4" />
                    <span className="hidden sm:inline">Organizations</span>
                    <span className="sm:hidden">Orgs</span>
                  </button>
                  <button
                    onClick={() => setViewType('individuals')}
                    className={`flex items-center gap-2 px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${
                      viewType === 'individuals'
                        ? 'bg-green-600 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <User className="w-4 h-4" />
                    <span className="hidden sm:inline">Individuals</span>
                    <span className="sm:hidden">Individuals</span>
                  </button>
                </div>
              </div>

              {/* Right section - Action buttons */}
              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="flex items-center justify-center bg-white text-blue-600 p-2.5 rounded-full shadow-sm hover:bg-blue-50 hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Refresh data"
                >
                  {isRefreshing ? <Loader2 size={18} className="animate-spin" /> : <RefreshCw size={18} />}
                </button>
                <button className="flex items-center justify-center bg-white text-blue-600 p-2.5 rounded-full shadow-sm hover:bg-blue-50 hover:shadow-md transition-all duration-200">
                  <Bell size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="admin-stats px-4 sm:px-6 py-3 flex-shrink-0">
          <div className="stats-grid">
            <StatCard
              title="Organizations"
              value={stats.totalOrganizations}
              icon={<Building className="w-4 h-4 text-blue-600" />}
              color="blue"
            />
            <StatCard
              title="Active Orgs"
              value={stats.activeOrganizations}
              icon={<Building className="w-4 h-4 text-green-600" />}
              color="green"
            />
            <StatCard
              title="Individuals"
              value={stats.totalIndividuals}
              icon={<User className="w-4 h-4 text-purple-600" />}
              color="purple"
            />
            <StatCard
              title="Approved Individuals"
              value={stats.approvedIndividuals}
              icon={<User className="w-4 h-4 text-green-600" />}
              color="green"
            />
            <StatCard
              title="Total Drones"
              value={stats.totalDrones}
              icon={<MapPin className="w-4 h-4 text-amber-600" />}
              color="amber"
            />
            <StatCard
              title="Active Drones"
              value={stats.activeDrones}
              icon={<BarChart3 className="w-4 h-4 text-emerald-600" />}
              color="emerald"
            />
          </div>
        </div>

        {/* Enhanced MapView */}
        <div className="admin-map-wrapper flex-1 px-4 sm:px-6 pb-4 overflow-hidden">
          <div className="admin-map-content w-full h-full bg-white rounded-xl shadow-lg overflow-hidden flex flex-col">
            {/* Map Container with full height */}
            <div className="flex-1 w-full min-h-0">
              {isLoading ? (
                <MapLoadingSpinner text="Loading organizations, individuals and map data..." />
              ) : (
                <EnhancedMapView
                  searchQuery={searchQuery}
                  filters={filters}
                  viewType={viewType}
                  onEntitySelect={handleEntitySelect}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Drone Details Panel */}
      <DroneDetailsPanel
        entity={selectedEntity}
        entityType={selectedEntityType}
        drones={selectedDrones}
        isVisible={showDroneDetails}
        onClose={handleCloseDroneDetails}
        onRefresh={handleRefreshDroneDetails}
      />
    </div>
  );
};

// Stats Card Component
const StatCard = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100 border-l-4 border-blue-500',
    green: 'bg-gradient-to-br from-green-50 to-green-100 border-l-4 border-green-500',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100 border-l-4 border-purple-500',
    amber: 'bg-gradient-to-br from-amber-50 to-amber-100 border-l-4 border-amber-500',
    emerald: 'bg-gradient-to-br from-emerald-50 to-emerald-100 border-l-4 border-emerald-500'
  };

  return (
    <div className={`${colorClasses[color]} p-2 sm:p-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 w-full`}>
      <div className="flex justify-between items-start">
        <div className="flex flex-col min-w-0 flex-1">
          <h4 className="text-xs font-medium text-gray-700 mb-1 leading-tight truncate">{title}</h4>
          <span className="text-lg sm:text-xl font-bold text-gray-900 leading-none">{value}</span>
        </div>
        <div className="p-1.5 rounded-lg bg-white shadow-sm flex-shrink-0 ml-2">
          {icon}
        </div>
      </div>
    </div>
  );
};

export default EnhancedMap;
