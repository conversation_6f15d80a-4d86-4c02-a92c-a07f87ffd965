import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    User,
    Eye,
    Edit2,
    Trash2,
    X,
    Check,
    RotateCw,
    Search,
    Filter,
    Download,
    Plus,
    TrendingUp,
    Users,
    Activity,
    Calendar,
    MapPin,
    Phone,
    Mail,
    Globe,
    ChevronDown,
    ChevronUp,
    MoreVertical,
    AlertCircle,
    CheckCircle,
    Clock,
    XCircle,
    Bot
} from 'lucide-react';
import AdminSidebar from '../common/AdminSidebar';
import individualService from '../../../services/individualService';

const IndividualPage = () => {
    const navigate = useNavigate();

    // Enhanced state management
    const [individuals, setIndividuals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [stats, setStats] = useState({
        totalIndividuals: 0,
        pendingIndividuals: 0,
        approvedIndividuals: 0,
        rejectedIndividuals: 0,
        suspendedIndividuals: 0,
        verifiedIndividuals: 0
    });

    // Filter and search state
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState('createdAt');
    const [sortOrder, setSortOrder] = useState('desc');

    // UI state
    const [selectedIndividuals, setSelectedIndividuals] = useState([]);
    const [showFilters, setShowFilters] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Fetch individuals data
    const fetchIndividuals = async () => {
        try {
            setError(null);
            const params = {
                search: searchTerm,
                status: statusFilter === 'all' ? '' : statusFilter,
                sortBy,
                sortOrder,
                limit: 50 // Get more records for better UX
            };

            const response = await individualService.getAllIndividuals(params);
            
            if (response.success) {
                setIndividuals(response.data.individuals || []);
            }
        } catch (err) {
            console.error('Error fetching individuals:', err);
            setError(err.message || 'Failed to fetch individuals');
        } finally {
            setLoading(false);
        }
    };

    // Fetch statistics
    const fetchStats = async () => {
        try {
            const response = await individualService.getIndividualStats();
            if (response.success) {
                setStats(response.data);
            }
        } catch (err) {
            console.error('Error fetching individual stats:', err);
        }
    };

    // Initial data load
    useEffect(() => {
        fetchIndividuals();
        fetchStats();
    }, [searchTerm, statusFilter, sortBy, sortOrder]);

    // Handle individual actions
    const handleApprove = async (individualId) => {
        try {
            await individualService.updateIndividualStatus(individualId, 'approved');
            fetchIndividuals();
            fetchStats();
        } catch (err) {
            alert('Failed to approve individual: ' + err.message);
        }
    };

    const handleReject = async (individualId) => {
        try {
            await individualService.updateIndividualStatus(individualId, 'rejected');
            fetchIndividuals();
            fetchStats();
        } catch (err) {
            alert('Failed to reject individual: ' + err.message);
        }
    };

    const handleDelete = async (individualId) => {
        if (window.confirm('Are you sure you want to delete this individual? This action cannot be undone.')) {
            try {
                await individualService.deleteIndividual(individualId);
                fetchIndividuals();
                fetchStats();
            } catch (err) {
                alert('Failed to delete individual: ' + err.message);
            }
        }
    };

    const handleRefresh = async () => {
        setIsRefreshing(true);
        await fetchIndividuals();
        await fetchStats();
        setIsRefreshing(false);
    };

    // Status badge component
    const StatusBadge = ({ status }) => {
        const statusConfig = {
            pending: { icon: Clock, color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
            approved: { icon: CheckCircle, color: 'bg-green-100 text-green-800', label: 'Approved' },
            rejected: { icon: XCircle, color: 'bg-red-100 text-red-800', label: 'Rejected' },
            suspended: { icon: AlertCircle, color: 'bg-orange-100 text-orange-800', label: 'Suspended' }
        };

        const config = statusConfig[status] || statusConfig.pending;
        const Icon = config.icon;

        return (
            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
                <Icon className="w-3 h-3" />
                {config.label}
            </span>
        );
    };

    // Filtered and sorted individuals
    const filteredIndividuals = useMemo(() => {
        return individuals.filter(individual => {
            const matchesSearch = !searchTerm || 
                individual.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                individual.contact?.primaryEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                individual.documents?.panNumber?.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesStatus = statusFilter === 'all' || individual.status === statusFilter;
            
            return matchesSearch && matchesStatus;
        });
    }, [individuals, searchTerm, statusFilter]);

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <AdminSidebar />
                <div className="lg:pl-[250px] w-full">
                    <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                            <RotateCw className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
                            <p className="text-gray-600">Loading individuals...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <AdminSidebar />
            
            {/* Main Content */}
            <div className="lg:pl-[250px] w-full">
                {/* Header Section */}
                <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900 text-left flex items-center gap-3">
                                <User className="text-blue-600" />
                                Individuals
                            </h2>
                            <p className="text-gray-600 mt-1">Manage and monitor all registered individuals</p>
                        </div>
                        <div className="flex items-center gap-3">
                            <button
                                onClick={() => navigate('/individualform')}
                                className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <Plus className="w-4 h-4" />
                                Add Individual
                            </button>
                            <button
                                onClick={handleRefresh}
                                disabled={isRefreshing}
                                className="flex items-center gap-2 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                            >
                                <RotateCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="px-4 lg:px-6 py-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Individuals</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.totalIndividuals}</p>
                                </div>
                                <Users className="w-8 h-8 text-blue-600" />
                            </div>
                        </div>
                        
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Pending</p>
                                    <p className="text-2xl font-bold text-yellow-600">{stats.pendingIndividuals}</p>
                                </div>
                                <Clock className="w-8 h-8 text-yellow-600" />
                            </div>
                        </div>
                        
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Approved</p>
                                    <p className="text-2xl font-bold text-green-600">{stats.approvedIndividuals}</p>
                                </div>
                                <CheckCircle className="w-8 h-8 text-green-600" />
                            </div>
                        </div>
                        
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Verified</p>
                                    <p className="text-2xl font-bold text-blue-600">{stats.verifiedIndividuals}</p>
                                </div>
                                <Activity className="w-8 h-8 text-blue-600" />
                            </div>
                        </div>

                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Allocated Drones</p>
                                    <p className="text-2xl font-bold text-purple-600">{stats.totalAllocatedDrones || 0}</p>
                                </div>
                                <Bot className="w-8 h-8 text-purple-600" />
                            </div>
                        </div>
                    </div>

                    {/* Search and Filters */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                        <div className="flex flex-col lg:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <input
                                        type="text"
                                        placeholder="Search individuals by name, email, or PAN..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                            </div>
                            
                            <div className="flex gap-3">
                                <select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="suspended">Suspended</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Individuals Table */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50 border-b border-gray-200">
                                    <tr>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Individual</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Contact</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Documents</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Drones</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Status</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Registered</th>
                                        <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {filteredIndividuals.map((individual) => (
                                        <tr key={individual._id} className="hover:bg-gray-50">
                                            <td className="text-left px-6 py-4">
                                                <div>
                                                    <div className="font-medium text-gray-900">{individual.fullName}</div>
                                                    <div className="text-sm text-gray-500 capitalize">{individual.gender} • Age: {individual.age || 'N/A'}</div>
                                                </div>
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <div className="text-sm">
                                                    <div className="flex items-center gap-1 text-gray-900">
                                                        <Mail className="w-3 h-3" />
                                                        {individual.contact?.primaryEmail}
                                                    </div>
                                                    <div className="flex items-center gap-1 text-gray-500 mt-1">
                                                        <Phone className="w-3 h-3" />
                                                        {individual.contact?.phone}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <div className="text-sm">
                                                    <div className="text-gray-900">PAN: {individual.documents?.panNumber}</div>
                                                    <div className="text-gray-500">Aadhar: {individual.documents?.aadharNumber}</div>
                                                </div>
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <div className="flex items-center">
                                                    <Bot className="h-4 w-4 text-gray-400 mr-1" />
                                                    <span className="text-sm text-gray-900">{individual.allocatedDrones || 0}</span>
                                                </div>
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <StatusBadge status={individual.status} />
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <div className="text-sm text-gray-900">
                                                    {new Date(individual.createdAt).toLocaleDateString()}
                                                </div>
                                            </td>
                                            <td className="text-left px-6 py-4">
                                                <div className="flex items-center gap-2">
                                                    {individual.status === 'pending' && (
                                                        <>
                                                            <button
                                                                onClick={() => handleApprove(individual._id)}
                                                                className="p-1 text-green-600 hover:bg-green-50 rounded"
                                                                title="Approve"
                                                            >
                                                                <Check className="w-4 h-4" />
                                                            </button>
                                                            <button
                                                                onClick={() => handleReject(individual._id)}
                                                                className="p-1 text-red-600 hover:bg-red-50 rounded"
                                                                title="Reject"
                                                            >
                                                                <X className="w-4 h-4" />
                                                            </button>
                                                        </>
                                                    )}
                                                    <button
                                                        onClick={() => handleDelete(individual._id)}
                                                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {filteredIndividuals.length === 0 && (
                            <div className="text-center py-12">
                                <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No individuals found</h3>
                                <p className="text-gray-500 mb-4">
                                    {searchTerm || statusFilter !== 'all' 
                                        ? 'Try adjusting your search or filters' 
                                        : 'Get started by adding your first individual'}
                                </p>
                                <button
                                    onClick={() => navigate('/individualform')}
                                    className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    <Plus className="w-4 h-4" />
                                    Add Individual
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IndividualPage;
