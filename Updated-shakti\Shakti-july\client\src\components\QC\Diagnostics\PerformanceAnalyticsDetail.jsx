import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  BarChart3,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Edit,
  Download,
  Printer,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Battery,
  Gauge,
  Target,
  Award,
  Thermometer,
  Wifi,
  Settings,
  HardDrive,
  Cpu
} from 'lucide-react';

const PerformanceAnalyticsDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample detailed performance analytics data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const sampleData = {
        'PA-2024-001': {
          id: 'PA-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          analyst: '<PERSON>',
          date: '2024-01-15',
          time: '16:30',
          period: 'Weekly',
          reportType: 'Comprehensive Performance',
          overallScore: 92,
          trend: 'Improving',
          nextAnalysis: '2024-01-22',
          analysisScope: {
            startDate: '2024-01-08',
            endDate: '2024-01-15',
            totalFlights: 45,
            totalHours: 67.5,
            missionsCompleted: 42,
            dataCollected: '245.7 GB'
          },
          metrics: {
            flightEfficiency: {
              score: 94,
              trend: 'up',
              change: '+3%',
              benchmark: 90,
              details: {
                avgFlightTime: '1.5 hours',
                fuelEfficiency: '95%',
                routeOptimization: '92%',
                timeUtilization: '88%'
              }
            },
            batteryPerformance: {
              score: 89,
              trend: 'stable',
              change: '0%',
              benchmark: 85,
              details: {
                avgBatteryLife: '2.3 hours',
                chargingEfficiency: '94%',
                degradationRate: '0.2%/month',
                temperatureStability: '96%'
              }
            },
            systemReliability: {
              score: 96,
              trend: 'up',
              change: '+2%',
              benchmark: 95,
              details: {
                uptime: '99.8%',
                mtbf: '450 hours',
                errorRate: '0.02%',
                recoveryTime: '< 30 seconds'
              }
            },
            missionSuccess: {
              score: 98,
              trend: 'up',
              change: '+1%',
              benchmark: 95,
              details: {
                completionRate: '98%',
                dataQuality: '96%',
                objectivesMet: '100%',
                safetyRecord: '100%'
              }
            },
            dataQuality: {
              score: 91,
              trend: 'down',
              change: '-1%',
              benchmark: 90,
              details: {
                imageSharpness: '94%',
                sensorAccuracy: '92%',
                dataIntegrity: '98%',
                processingSpeed: '89%'
              }
            },
            operationalCost: {
              score: 87,
              trend: 'up',
              change: '+4%',
              benchmark: 80,
              details: {
                costPerHour: '$45.20',
                maintenanceCost: '$12.50/hour',
                energyCost: '$3.80/hour',
                operatorCost: '$28.90/hour'
              }
            }
          },
          performanceBreakdown: {
            excellent: 65, // Percentage of time performing excellently
            good: 25,      // Percentage of time performing well
            average: 8,    // Percentage of time performing averagely
            poor: 2        // Percentage of time performing poorly
          },
          comparativeAnalysis: {
            fleetAverage: 85,
            industryBenchmark: 82,
            previousPeriod: 89,
            targetGoal: 95
          },
          keyInsights: [
            'Flight efficiency improved by 3% this week due to optimized flight paths',
            'System reliability exceeds benchmark by 1% with 99.8% uptime',
            'Mission success rate remains excellent at 98% with perfect safety record',
            'Data quality slightly decreased by 1% due to weather conditions',
            'Operational costs improved by 4% through better resource utilization'
          ],
          recommendations: [
            'Optimize flight patterns for wind resistance to improve data quality',
            'Update sensor calibration to maintain accuracy standards',
            'Continue current maintenance schedule to preserve reliability',
            'Consider upgrading image processing algorithms',
            'Implement predictive maintenance to reduce costs further'
          ],
          detailedMetrics: {
            flightOperations: {
              totalDistance: '2,450 km',
              avgAltitude: '95m',
              avgSpeed: '15 km/h',
              maxWindSpeed: '25 km/h',
              weatherConditions: 'Mostly clear (78%)'
            },
            systemHealth: {
              motorEfficiency: '96%',
              sensorCalibration: '94%',
              communicationQuality: '98%',
              storageUtilization: '68%',
              processorLoad: '45%'
            },
            missionTypes: [
              { type: 'Surveying', count: 25, successRate: '100%' },
              { type: 'Inspection', count: 12, successRate: '95%' },
              { type: 'Mapping', count: 8, successRate: '100%' }
            ]
          },
          historicalTrends: [
            { period: 'Week 1', score: 88 },
            { period: 'Week 2', score: 89 },
            { period: 'Week 3', score: 91 },
            { period: 'Week 4', score: 92 }
          ],
          alerts: [
            {
              type: 'Warning',
              message: 'Data quality trending downward - monitor weather impact',
              priority: 'Medium'
            },
            {
              type: 'Info',
              message: 'Flight efficiency exceeds target by 4%',
              priority: 'Low'
            }
          ]
        },
        'PA-2024-002': {
          id: 'PA-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          analyst: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:15',
          period: 'Monthly',
          reportType: 'Battery Performance Analysis',
          overallScore: 78,
          trend: 'Declining',
          nextAnalysis: '2024-02-14',
          analysisScope: {
            startDate: '2023-12-14',
            endDate: '2024-01-14',
            totalFlights: 38,
            totalHours: 52.3,
            missionsCompleted: 34,
            dataCollected: '156.8 GB'
          },
          metrics: {
            flightEfficiency: {
              score: 82,
              trend: 'down',
              change: '-5%',
              benchmark: 90,
              details: {
                avgFlightTime: '1.4 hours',
                fuelEfficiency: '78%',
                routeOptimization: '85%',
                timeUtilization: '82%'
              }
            },
            batteryPerformance: {
              score: 71,
              trend: 'down',
              change: '-8%',
              benchmark: 85,
              details: {
                avgBatteryLife: '1.8 hours',
                chargingEfficiency: '85%',
                degradationRate: '1.2%/month',
                temperatureStability: '78%'
              }
            },
            systemReliability: {
              score: 85,
              trend: 'stable',
              change: '0%',
              benchmark: 95,
              details: {
                uptime: '97.5%',
                mtbf: '280 hours',
                errorRate: '0.08%',
                recoveryTime: '45 seconds'
              }
            },
            missionSuccess: {
              score: 89,
              trend: 'down',
              change: '-3%',
              benchmark: 95,
              details: {
                completionRate: '89%',
                dataQuality: '91%',
                objectivesMet: '94%',
                safetyRecord: '100%'
              }
            },
            dataQuality: {
              score: 88,
              trend: 'stable',
              change: '0%',
              benchmark: 90,
              details: {
                imageSharpness: '89%',
                sensorAccuracy: '88%',
                dataIntegrity: '95%',
                processingSpeed: '85%'
              }
            },
            operationalCost: {
              score: 65,
              trend: 'down',
              change: '-12%',
              benchmark: 80,
              details: {
                costPerHour: '$68.50',
                maintenanceCost: '$28.90/hour',
                energyCost: '$8.20/hour',
                operatorCost: '$31.40/hour'
              }
            }
          },
          performanceBreakdown: {
            excellent: 35,
            good: 40,
            average: 20,
            poor: 5
          },
          comparativeAnalysis: {
            fleetAverage: 85,
            industryBenchmark: 82,
            previousPeriod: 86,
            targetGoal: 95
          },
          keyInsights: [
            'Battery performance declined 8% this month due to aging cells',
            'Operational costs increased due to frequent charging and maintenance',
            'Flight efficiency below benchmark by 8% affecting mission productivity',
            'System reliability remains stable but below fleet average',
            'Mission success rate decreased but safety record remains perfect'
          ],
          recommendations: [
            'Replace battery pack immediately to restore performance',
            'Conduct comprehensive system check for efficiency improvements',
            'Review maintenance schedule to address reliability issues',
            'Consider upgrading to newer battery technology',
            'Implement battery health monitoring system'
          ],
          detailedMetrics: {
            flightOperations: {
              totalDistance: '1,890 km',
              avgAltitude: '88m',
              avgSpeed: '13 km/h',
              maxWindSpeed: '30 km/h',
              weatherConditions: 'Variable (45%)'
            },
            systemHealth: {
              motorEfficiency: '89%',
              sensorCalibration: '91%',
              communicationQuality: '94%',
              storageUtilization: '89%',
              processorLoad: '62%'
            },
            missionTypes: [
              { type: 'Surveying', count: 18, successRate: '89%' },
              { type: 'Inspection', count: 15, successRate: '87%' },
              { type: 'Mapping', count: 5, successRate: '100%' }
            ]
          },
          historicalTrends: [
            { period: 'Month 1', score: 86 },
            { period: 'Month 2', score: 82 },
            { period: 'Month 3', score: 80 },
            { period: 'Month 4', score: 78 }
          ],
          alerts: [
            {
              type: 'Critical',
              message: 'Battery performance critically low - immediate replacement required',
              priority: 'High'
            },
            {
              type: 'Warning',
              message: 'Operational costs exceeding budget by 15%',
              priority: 'High'
            }
          ]
        }
      };

      setAnalytics(sampleData[id] || null);
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!analytics) {
    return (
      <QCLayout title="Performance Analytics Not Found" subtitle="The requested analytics report could not be found">
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Report Not Found</h3>
          <p className="text-gray-600 mb-4">The performance analytics report with ID "{id}" could not be found.</p>
          <button
            onClick={() => navigate('/qc-diagnostics/performance-analytics')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Performance Analytics
          </button>
        </div>
      </QCLayout>
    );
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendColor = (trend) => {
    switch (trend.toLowerCase()) {
      case 'improving':
      case 'up': return 'text-green-600';
      case 'declining':
      case 'down': return 'text-red-600';
      case 'stable': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend.toLowerCase()) {
      case 'improving':
      case 'up': return <TrendingUp className="w-3 h-3" />;
      case 'declining':
      case 'down': return <TrendingDown className="w-3 h-3" />;
      case 'stable': return <Activity className="w-3 h-3" />;
      default: return <Activity className="w-3 h-3" />;
    }
  };

  const getOverallTrendColor = (trend) => {
    switch (trend) {
      case 'Improving': return 'bg-green-100 text-green-700 border-green-200';
      case 'Declining': return 'bg-red-100 text-red-700 border-red-200';
      case 'Stable': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getMetricIcon = (metric) => {
    switch (metric) {
      case 'flightEfficiency': return <Target className="w-5 h-5" />;
      case 'batteryPerformance': return <Battery className="w-5 h-5" />;
      case 'systemReliability': return <Activity className="w-5 h-5" />;
      case 'missionSuccess': return <Award className="w-5 h-5" />;
      case 'dataQuality': return <Settings className="w-5 h-5" />;
      case 'operationalCost': return <Gauge className="w-5 h-5" />;
      default: return <BarChart3 className="w-5 h-5" />;
    }
  };

  return (
    <QCLayout
      title={`Performance Analytics - ${analytics.id}`}
      subtitle={`${analytics.droneId} - ${analytics.droneName}`}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate('/qc-diagnostics/performance-analytics')}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Performance Analytics
          </button>
          
          <div className="flex items-center gap-3">
            <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Edit
            </button>
            <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
            <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
              <Printer className="w-4 h-4" />
              Print
            </button>
          </div>
        </div>

        {/* Analytics Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Analytics Info */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance Analysis Overview</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                  <span className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {analytics.analyst}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {analytics.date} at {analytics.time}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {analytics.period}
                  </span>
                </div>
                <p className="text-gray-600">{analytics.reportType}</p>
              </div>
              <div className="flex flex-col items-end gap-2">
                <div className={`text-3xl font-bold ${getScoreColor(analytics.overallScore)}`}>
                  {analytics.overallScore}%
                </div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getOverallTrendColor(analytics.trend)}`}>
                  {getTrendIcon(analytics.trend)}
                  <span className="ml-2">{analytics.trend}</span>
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <p className="text-xs text-gray-500 mb-1">Analysis Period</p>
                <p className="text-sm font-medium text-gray-900">{analytics.analysisScope.startDate} - {analytics.analysisScope.endDate}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Total Flights</p>
                <p className="text-sm font-medium text-gray-900">{analytics.analysisScope.totalFlights}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Flight Hours</p>
                <p className="text-sm font-medium text-gray-900">{analytics.analysisScope.totalHours}h</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Data Collected</p>
                <p className="text-sm font-medium text-gray-900">{analytics.analysisScope.dataCollected}</p>
              </div>
            </div>
          </div>

          {/* Performance Breakdown */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Distribution</h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Excellent</span>
                  <span className="text-sm font-medium text-green-600">{analytics.performanceBreakdown.excellent}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{width: `${analytics.performanceBreakdown.excellent}%`}}></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Good</span>
                  <span className="text-sm font-medium text-blue-600">{analytics.performanceBreakdown.good}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{width: `${analytics.performanceBreakdown.good}%`}}></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Average</span>
                  <span className="text-sm font-medium text-yellow-600">{analytics.performanceBreakdown.average}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{width: `${analytics.performanceBreakdown.average}%`}}></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Poor</span>
                  <span className="text-sm font-medium text-red-600">{analytics.performanceBreakdown.poor}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-red-500 h-2 rounded-full" style={{width: `${analytics.performanceBreakdown.poor}%`}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(analytics.metrics).map(([metricName, metricData]) => (
            <div key={metricName} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${metricData.score >= 90 ? 'bg-green-100 text-green-600' : metricData.score >= 75 ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>
                    {getMetricIcon(metricName)}
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 capitalize">
                      {metricName.replace(/([A-Z])/g, ' $1').trim()}
                    </h4>
                    <div className="flex items-center gap-1">
                      <span className={`text-xs ${getTrendColor(metricData.trend)}`}>
                        {getTrendIcon(metricData.trend)}
                      </span>
                      <span className={`text-xs font-medium ${getTrendColor(metricData.trend)}`}>
                        {metricData.change}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-xl font-bold ${getScoreColor(metricData.score)}`}>
                    {metricData.score}%
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: {metricData.benchmark}%
                  </div>
                </div>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className={`h-2 rounded-full ${metricData.score >= 90 ? 'bg-green-500' : metricData.score >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                  style={{width: `${metricData.score}%`}}
                ></div>
              </div>

              <div className="space-y-2">
                {Object.entries(metricData.details).slice(0, 3).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                    <span className="font-medium text-gray-900">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </QCLayout>
  );
};

export default PerformanceAnalyticsDetail;
