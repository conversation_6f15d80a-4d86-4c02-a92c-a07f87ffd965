import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  Activity,
  Clock,
  Battery,
  MapPin,
  Zap,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import {
  FaChartLine,
  FaChartBar,
  FaChartPie,
  FaDownload,
  FaFilter,
  FaSync
} from 'react-icons/fa';

const DroneAnalytics = ({ droneId }) => {
  const [timeRange, setTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(false);

  // Mock analytics data
  const [analyticsData] = useState({
    flightStats: {
      totalFlights: 156,
      totalHours: 245.5,
      totalDistance: 1250.8,
      averageSpeed: 18.5,
      successRate: 98.7
    },
    batteryAnalytics: {
      averageUsage: 78,
      cycleCount: 342,
      healthScore: 94,
      estimatedLifespan: '18 months'
    },
    performanceTrends: [
      { date: '2025-04-18', flights: 8, hours: 12.5, efficiency: 95 },
      { date: '2025-04-19', flights: 6, hours: 9.2, efficiency: 92 },
      { date: '2025-04-20', flights: 10, hours: 15.8, efficiency: 97 },
      { date: '2025-04-21', flights: 7, hours: 11.3, efficiency: 94 },
      { date: '2025-04-22', flights: 9, hours: 14.1, efficiency: 96 },
      { date: '2025-04-23', flights: 5, hours: 8.7, efficiency: 89 },
      { date: '2025-04-24', flights: 11, hours: 16.9, efficiency: 98 }
    ],
    maintenanceAlerts: [
      {
        id: 1,
        type: 'warning',
        title: 'Propeller Inspection Due',
        description: 'Scheduled inspection required within 5 flight hours',
        priority: 'medium',
        dueDate: '2025-04-28'
      },
      {
        id: 2,
        type: 'info',
        title: 'Battery Calibration',
        description: 'Battery calibration recommended for optimal performance',
        priority: 'low',
        dueDate: '2025-05-01'
      }
    ]
  });

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  };

  const handleExport = () => {
    console.log('Exporting analytics data...');
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'info': return <CheckCircle className="w-4 h-4 text-blue-600" />;
      default: return <CheckCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
              <FaChartLine className="text-blue-600" />
              Drone Analytics Dashboard
            </h2>
            <p className="text-gray-600 mt-1">
              Performance insights and operational metrics for {droneId}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Activity className="w-5 h-5 text-blue-600" />
            </div>
            <span className="text-xs text-green-600 font-medium">+12%</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{analyticsData.flightStats.totalFlights}</h3>
          <p className="text-sm text-gray-600">Total Flights</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Clock className="w-5 h-5 text-green-600" />
            </div>
            <span className="text-xs text-green-600 font-medium">+8%</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{analyticsData.flightStats.totalHours}h</h3>
          <p className="text-sm text-gray-600">Flight Hours</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <MapPin className="w-5 h-5 text-purple-600" />
            </div>
            <span className="text-xs text-green-600 font-medium">+15%</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{analyticsData.flightStats.totalDistance} km</h3>
          <p className="text-sm text-gray-600">Distance Covered</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-orange-100 rounded-lg">
              <TrendingUp className="w-5 h-5 text-orange-600" />
            </div>
            <span className="text-xs text-green-600 font-medium">+3%</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{analyticsData.flightStats.successRate}%</h3>
          <p className="text-sm text-gray-600">Success Rate</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Battery className="w-5 h-5 text-yellow-600" />
            </div>
            <span className="text-xs text-red-600 font-medium">-2%</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{analyticsData.batteryAnalytics.healthScore}%</h3>
          <p className="text-sm text-gray-600">Battery Health</p>
        </div>
      </div>

      {/* Performance Trends Chart */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Performance Trends</h3>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">Flights</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Hours</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-gray-600">Efficiency</span>
            </div>
          </div>
        </div>
        <div className="h-64 flex items-end justify-between gap-2">
          {analyticsData.performanceTrends.map((day, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div className="w-full flex flex-col items-center gap-1 mb-2">
                <div 
                  className="w-full bg-blue-500 rounded-t"
                  style={{ height: `${(day.flights / 12) * 100}px` }}
                  title={`${day.flights} flights`}
                ></div>
                <div 
                  className="w-full bg-green-500"
                  style={{ height: `${(day.hours / 20) * 80}px` }}
                  title={`${day.hours} hours`}
                ></div>
                <div 
                  className="w-full bg-purple-500 rounded-b"
                  style={{ height: `${(day.efficiency / 100) * 60}px` }}
                  title={`${day.efficiency}% efficiency`}
                ></div>
              </div>
              <span className="text-xs text-gray-500 transform -rotate-45">
                {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Maintenance Alerts */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Maintenance Alerts</h3>
          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
            {analyticsData.maintenanceAlerts.length} Active
          </span>
        </div>
        <div className="space-y-3">
          {analyticsData.maintenanceAlerts.map((alert) => (
            <div key={alert.id} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
              {getAlertIcon(alert.type)}
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-medium text-gray-900">{alert.title}</h4>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(alert.priority)}`}>
                      {alert.priority}
                    </span>
                    <span className="text-xs text-gray-500">Due: {new Date(alert.dueDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{alert.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DroneAnalytics;
