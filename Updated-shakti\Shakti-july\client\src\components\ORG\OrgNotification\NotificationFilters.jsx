import React from 'react';
import {
    Filter,
    SortAsc,
    SortDesc,
    Calendar,
    Tag,
    AlertTriangle,
    Eye,
    EyeOff,
    Grid3X3,
    List,
    X,
    CheckCircle,
    Clock,
    Info,
    Shield
} from 'lucide-react';

const NotificationFilters = ({
    filterType,
    setFilterType,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    sortOrder,
    setSortOrder,
    viewMode,
    setViewMode
}) => {
    const FilterSection = ({ title, icon: Icon, children }) => (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-3">
                <Icon className="w-4 h-4 text-gray-600" />
                <h4 className="font-medium text-gray-900">{title}</h4>
            </div>
            {children}
        </div>
    );

    const FilterButton = ({ active, onClick, children, color = "blue" }) => (
        <button
            onClick={onClick}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                active
                    ? `bg-${color}-600 text-white shadow-md`
                    : `bg-gray-100 text-gray-700 hover:bg-gray-200`
            }`}
        >
            {children}
        </button>
    );

    const typeOptions = [
        { value: 'all', label: 'All Types', icon: Tag },
        { value: 'emergency', label: 'Emergency', icon: AlertTriangle, color: 'red' },
        { value: 'warning', label: 'Warning', icon: Shield, color: 'orange' },
        { value: 'success', label: 'Success', icon: CheckCircle, color: 'green' },
        { value: 'info', label: 'Information', icon: Info, color: 'blue' }
    ];

    const priorityOptions = [
        { value: 'all', label: 'All Priorities', color: 'gray' },
        { value: 'critical', label: 'Critical', color: 'red' },
        { value: 'high', label: 'High', color: 'orange' },
        { value: 'medium', label: 'Medium', color: 'yellow' },
        { value: 'low', label: 'Low', color: 'blue' }
    ];

    const statusOptions = [
        { value: 'all', label: 'All Status', icon: Eye },
        { value: 'unread', label: 'Unread', icon: EyeOff, color: 'orange' },
        { value: 'read', label: 'Read', icon: Eye, color: 'green' },
        { value: 'archived', label: 'Archived', icon: Clock, color: 'gray' }
    ];

    const sortOptions = [
        { value: 'newest', label: 'Newest First', icon: SortDesc },
        { value: 'oldest', label: 'Oldest First', icon: SortAsc },
        { value: 'priority', label: 'By Priority', icon: AlertTriangle }
    ];

    const clearAllFilters = () => {
        setFilterType('all');
        setFilterPriority('all');
        setFilterStatus('all');
        setSortOrder('newest');
    };

    const hasActiveFilters = filterType !== 'all' || filterPriority !== 'all' || filterStatus !== 'all' || sortOrder !== 'newest';

    return (
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-black">
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                    <Filter className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
                </div>
                <div className="flex items-center gap-3">
                    {hasActiveFilters && (
                        <button
                            onClick={clearAllFilters}
                            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                            <X className="w-4 h-4" />
                            Clear All
                        </button>
                    )}
                    <div className="flex items-center gap-1 bg-white rounded-lg p-1 border border-gray-200">
                        <button
                            onClick={() => setViewMode('list')}
                            className={`p-2 rounded-md transition-colors ${
                                viewMode === 'list' 
                                    ? 'bg-blue-600 text-white' 
                                    : 'text-gray-600 hover:bg-gray-100'
                            }`}
                        >
                            <List className="w-4 h-4" />
                        </button>
                        <button
                            onClick={() => setViewMode('card')}
                            className={`p-2 rounded-md transition-colors ${
                                viewMode === 'card' 
                                    ? 'bg-blue-600 text-white' 
                                    : 'text-gray-600 hover:bg-gray-100'
                            }`}
                        >
                            <Grid3X3 className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                {/* Type Filter */}
                <FilterSection title="Category" icon={Tag}>
                    <div className="space-y-2">
                        {typeOptions.map((option) => {
                            const IconComponent = option.icon;
                            return (
                                <button
                                    key={option.value}
                                    onClick={() => setFilterType(option.value)}
                                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                        filterType === option.value
                                            ? `bg-${option.color || 'blue'}-600 text-white shadow-md`
                                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                                    }`}
                                >
                                    <IconComponent className="w-4 h-4" />
                                    {option.label}
                                </button>
                            );
                        })}
                    </div>
                </FilterSection>

                {/* Priority Filter */}
                <FilterSection title="Priority" icon={AlertTriangle}>
                    <div className="space-y-2">
                        {priorityOptions.map((option) => (
                            <button
                                key={option.value}
                                onClick={() => setFilterPriority(option.value)}
                                className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                    filterPriority === option.value
                                        ? `bg-${option.color}-600 text-white shadow-md`
                                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                                }`}
                            >
                                <div className={`w-3 h-3 rounded-full bg-${option.color}-500`}></div>
                                {option.label}
                            </button>
                        ))}
                    </div>
                </FilterSection>

                {/* Status Filter */}
                <FilterSection title="Status" icon={Eye}>
                    <div className="space-y-2">
                        {statusOptions.map((option) => {
                            const IconComponent = option.icon;
                            return (
                                <button
                                    key={option.value}
                                    onClick={() => setFilterStatus(option.value)}
                                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                        filterStatus === option.value
                                            ? `bg-${option.color || 'blue'}-600 text-white shadow-md`
                                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                                    }`}
                                >
                                    <IconComponent className="w-4 h-4" />
                                    {option.label}
                                </button>
                            );
                        })}
                    </div>
                </FilterSection>

                {/* Sort Options */}
                <FilterSection title="Sort By" icon={SortDesc}>
                    <div className="space-y-2">
                        {sortOptions.map((option) => {
                            const IconComponent = option.icon;
                            return (
                                <button
                                    key={option.value}
                                    onClick={() => setSortOrder(option.value)}
                                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                        sortOrder === option.value
                                            ? 'bg-blue-600 text-white shadow-md'
                                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                                    }`}
                                >
                                    <IconComponent className="w-4 h-4" />
                                    {option.label}
                                </button>
                            );
                        })}
                    </div>
                </FilterSection>
            </div>

            {/* Active Filters Summary */}
            {hasActiveFilters && (
                <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Active Filters:</h4>
                    <div className="flex flex-wrap gap-2">
                        {filterType !== 'all' && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                Category: {typeOptions.find(o => o.value === filterType)?.label}
                                <button onClick={() => setFilterType('all')}>
                                    <X className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filterPriority !== 'all' && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                                Priority: {priorityOptions.find(o => o.value === filterPriority)?.label}
                                <button onClick={() => setFilterPriority('all')}>
                                    <X className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filterStatus !== 'all' && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                Status: {statusOptions.find(o => o.value === filterStatus)?.label}
                                <button onClick={() => setFilterStatus('all')}>
                                    <X className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {sortOrder !== 'newest' && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                Sort: {sortOptions.find(o => o.value === sortOrder)?.label}
                                <button onClick={() => setSortOrder('newest')}>
                                    <X className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default NotificationFilters;
