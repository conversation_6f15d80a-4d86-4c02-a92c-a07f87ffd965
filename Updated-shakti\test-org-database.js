require('dotenv').config();
const { connectOrgDB } = require('./server/config/organizationDatabase');
const getOrgDroneModel = require('./server/models/OrgDrone');

async function testOrgDatabase() {
  console.log('🧪 Testing Organization Database Setup...\n');

  try {
    // Connect to organization database
    console.log('1️⃣ Connecting to Organization Database...');
    await connectOrgDB();
    console.log('✅ Organization Database Connected\n');

    // Get the drone model
    console.log('2️⃣ Testing Drone Model...');
    const OrgDrone = getOrgDroneModel();
    console.log('✅ Drone Model Loaded\n');

    // Test creating a sample drone
    console.log('3️⃣ Testing Drone Creation...');
    const testDrone = new OrgDrone({
      droneId: OrgDrone.generateDroneId(),
      name: 'Test Drone Alpha',
      model: 'DJI Phantom 4 Pro',
      manufacturer: 'DJI',
      serialNumber: `TEST-${Date.now()}`,
      registrationNumber: `REG-TEST-${Date.now()}`,
      organizationId: '507f1f77bcf86cd799439011', // Sample ObjectId
      organizationName: 'Test Organization',
      droneType: 'quadcopter',
      specifications: {
        weight: 1.4,
        maxPayload: 0.5,
        maxFlightTime: 30,
        maxRange: 7,
        maxAltitude: 120,
        maxSpeed: 72,
        batteryCapacity: 5870,
        cameraResolution: '4K',
        hasGimbal: true,
        hasGPS: true,
        hasObstacleAvoidance: true
      },
      purchase: {
        purchaseDate: new Date(),
        purchasePrice: 1500,
        vendor: 'DJI Store'
      },
      currentLocation: {
        latitude: 19.0760,
        longitude: 72.8777,
        altitude: 0
      },
      createdBy: '507f1f77bcf86cd799439011', // Sample ObjectId
      createdByName: 'Test User'
    });

    // Validate the drone
    const validationError = testDrone.validateSync();
    if (validationError) {
      console.log('❌ Validation Error:', validationError.message);
      return;
    }

    console.log('✅ Drone validation passed');

    // Save the drone (comment out if you don't want to actually save)
    // const savedDrone = await testDrone.save();
    // console.log('✅ Drone saved with ID:', savedDrone._id);

    console.log('✅ Test drone created successfully (not saved)\n');

    // Test querying
    console.log('4️⃣ Testing Database Queries...');
    const droneCount = await OrgDrone.countDocuments();
    console.log(`✅ Current drone count in database: ${droneCount}\n`);

    // Test indexes
    console.log('5️⃣ Testing Database Indexes...');
    const indexes = await OrgDrone.collection.getIndexes();
    console.log('✅ Database indexes:');
    Object.keys(indexes).forEach(indexName => {
      console.log(`   - ${indexName}`);
    });

    console.log('\n🎉 Organization Database Test Complete!');
    console.log('✅ All tests passed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close connection
    process.exit(0);
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting Organization Database Test...');
  console.log('📊 This will test the separate organization database setup\n');
  
  testOrgDatabase().catch(console.error);
}

module.exports = { testOrgDatabase };
