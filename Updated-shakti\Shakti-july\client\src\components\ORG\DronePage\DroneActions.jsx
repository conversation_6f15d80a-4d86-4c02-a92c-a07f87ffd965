import React, { useState } from 'react';
import {
  Plus,
  Download,
  Upload,
  Settings,
  Play,
  Square,
  Wrench,
  MapPin,
  Users,
  Calendar,
  FileText,
  MoreHorizontal,
  Trash2,
  Copy,
  Edit
} from 'lucide-react';

const DroneActions = ({
  selectedDrones,
  onAddDrone,
  onExport,
  onBulkStatusUpdate,
  onBulkDelete,
  onBulkAssignPilot,
  onBulkAssignLocation,
  onBulkScheduleMaintenance,
  setSelectedDrones
}) => {
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const quickActions = [
    {
      label: 'Add New Drone',
      icon: Plus,
      onClick: onAddDrone,
      color: 'bg-blue-600 hover:bg-blue-700 text-white',
      primary: true
    },
    {
      label: 'Export Data',
      icon: Download,
      onClick: onExport,
      color: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
    },
    {
      label: 'Import Drones',
      icon: Upload,
      onClick: () => console.log('Import drones'),
      color: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
    },
    {
      label: 'Fleet Settings',
      icon: Settings,
      onClick: () => console.log('Fleet settings'),
      color: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
    }
  ];

  const handleBulkAction = async (action, ...args) => {
    if (isProcessing) return;
    setIsProcessing(true);
    try {
      await action(...args);
    } catch (error) {
      console.error('Bulk action failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const bulkActions = [
    {
      label: 'Activate Selected',
      icon: Play,
      onClick: () => handleBulkAction(onBulkStatusUpdate, selectedDrones, 'active'),
      color: 'bg-green-600 hover:bg-green-700 text-white',
      disabled: isProcessing
    },
    {
      label: 'Deactivate Selected',
      icon: Square,
      onClick: () => handleBulkAction(onBulkStatusUpdate, selectedDrones, 'inactive'),
      color: 'bg-yellow-600 hover:bg-yellow-700 text-white',
      disabled: isProcessing
    },
    {
      label: 'Schedule Maintenance',
      icon: Wrench,
      onClick: () => handleBulkAction(onBulkScheduleMaintenance, selectedDrones),
      color: 'bg-orange-600 hover:bg-orange-700 text-white',
      disabled: isProcessing
    },
    {
      label: 'Assign Location',
      icon: MapPin,
      onClick: () => handleBulkAction(onBulkAssignLocation, selectedDrones),
      color: 'bg-purple-600 hover:bg-purple-700 text-white',
      disabled: isProcessing
    },
    {
      label: 'Assign Pilot',
      icon: Users,
      onClick: () => handleBulkAction(onBulkAssignPilot, selectedDrones),
      color: 'bg-indigo-600 hover:bg-indigo-700 text-white',
      disabled: isProcessing
    }
  ];

  const reportActions = [
    {
      label: 'Generate Report',
      icon: FileText,
      onClick: () => console.log('Generate report'),
      color: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
    },
    {
      label: 'Schedule Report',
      icon: Calendar,
      onClick: () => console.log('Schedule report'),
      color: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
    }
  ];

  const ActionButton = ({ label, icon: Icon, onClick, color, primary = false, disabled = false }) => (
    <button
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      className={`inline-flex items-center gap-1 lg:gap-2 px-3 lg:px-4 py-2 lg:py-2.5 rounded-lg font-medium text-xs lg:text-sm transition-all duration-200 hover:shadow-md ${
        disabled ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500' : color
      } ${primary ? 'shadow-lg' : ''} whitespace-nowrap`}
    >
      <Icon className="w-3 h-3 lg:w-4 lg:h-4 flex-shrink-0" />
      <span className="hidden sm:inline">{disabled && isProcessing ? 'Processing...' : label}</span>
    </button>
  );

  return (
    <div className="space-y-4">
      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Quick Actions</h3>
          <button
            onClick={() => setShowBulkActions(!showBulkActions)}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <MoreHorizontal className="w-5 h-5" />
          </button>
        </div>

        <div className="drone-actions-container flex flex-wrap gap-2 lg:gap-3">
          {quickActions.map((action, index) => (
            <ActionButton key={index} {...action} />
          ))}
        </div>
      </div>

      {/* Bulk Actions - Show when drones are selected */}
      {selectedDrones.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6 animate-slideInDown">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <h3 className="text-lg font-semibold text-gray-800">
                Bulk Actions ({selectedDrones.length} selected)
              </h3>
            </div>
            <button
              onClick={() => setSelectedDrones([])}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Clear selection
            </button>
          </div>

          <div className="flex flex-wrap gap-2 lg:gap-3">
            {bulkActions.map((action, index) => (
              <ActionButton key={index} {...action} />
            ))}

            {/* Destructive Actions */}
            <div className="flex flex-wrap gap-2 ml-auto">
              <button
                onClick={() => handleBulkAction(() => console.log('Duplicate functionality coming soon'))}
                disabled={isProcessing}
                className={`inline-flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 hover:shadow-md ${
                  isProcessing ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500' : 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
                }`}
              >
                <Copy className="w-4 h-4" />
                Duplicate
              </button>
              <button
                onClick={() => handleBulkAction(() => console.log('Bulk edit functionality coming soon'))}
                disabled={isProcessing}
                className={`inline-flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 hover:shadow-md ${
                  isProcessing ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500' : 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300'
                }`}
              >
                <Edit className="w-4 h-4" />
                Edit
              </button>
              <button
                onClick={() => {
                  if (window.confirm(`Are you sure you want to delete ${selectedDrones.length} selected drones? This action cannot be undone.`)) {
                    handleBulkAction(onBulkDelete, selectedDrones);
                  }
                }}
                disabled={isProcessing}
                className={`inline-flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 hover:shadow-md ${
                  isProcessing ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500' : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                <Trash2 className="w-4 h-4" />
                {isProcessing ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Extended Actions - Show when toggle is active */}
      {showBulkActions && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-slideInDown">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Reports & Analytics</h3>
          
          <div className="flex flex-wrap gap-2 lg:gap-3">
            {reportActions.map((action, index) => (
              <ActionButton key={index} {...action} />
            ))}
          </div>
        </div>
      )}

      {/* Action Status Bar */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <span className="text-gray-600">
              Last updated: {new Date().toLocaleTimeString()}
            </span>
            <span className="text-gray-600">
              Auto-refresh: <span className="text-green-600 font-medium">Enabled</span>
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-gray-600">System Status: Online</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DroneActions;
