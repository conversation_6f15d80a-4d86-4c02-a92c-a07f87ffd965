import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  MapPin,
  Plus,
  Search,
  Filter,
  Settings,
  CheckCircle,
  AlertTriangle,
  X,
  Save,
  Edit,
  Trash2,
  RefreshCw,
  Shield,
  Eye,
  EyeOff
} from 'lucide-react';
import {
  FaMapMarkerAlt,
  FaPlus,
  FaSearch,
  FaFilter,
  FaCog,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimes,
  FaSave,
  FaEdit,
  FaTrash,
  FaEye,
  FaEyeSlash,
  FaShieldAlt,
  FaIndustry,
  FaHome,
  FaStore,
  FaTree
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const ManageZones = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingZone, setEditingZone] = useState(null);

  // Form state
  const [zoneName, setZoneName] = useState('');
  const [zoneType, setZoneType] = useState('');
  const [zoneStatus, setZoneStatus] = useState('active');
  const [coordinates, setCoordinates] = useState('');
  const [radius, setRadius] = useState(1000);
  const [maxAltitude, setMaxAltitude] = useState(120);
  const [restrictions, setRestrictions] = useState('');
  const [description, setDescription] = useState('');

  // Mock data for deployment zones
  const [deploymentZones, setDeploymentZones] = useState([
    {
      id: 1,
      name: 'North Industrial Zone',
      type: 'Industrial',
      status: 'active',
      coordinates: '40.7128, -74.0060',
      radius: 2000,
      maxAltitude: 150,
      restrictions: 'No flights during shift changes (7-8 AM, 3-4 PM)',
      description: 'Manufacturing and warehouse district',
      deployments: 18,
      lastUsed: '2024-02-14'
    },
    {
      id: 2,
      name: 'South Agricultural Area',
      type: 'Agricultural',
      status: 'active',
      coordinates: '40.6892, -74.0445',
      radius: 5000,
      maxAltitude: 200,
      restrictions: 'Avoid crop spraying areas',
      description: 'Farmland and agricultural facilities',
      deployments: 15,
      lastUsed: '2024-02-13'
    },
    {
      id: 3,
      name: 'East Residential District',
      type: 'Residential',
      status: 'restricted',
      coordinates: '40.7589, -73.9851',
      radius: 1500,
      maxAltitude: 100,
      restrictions: 'Flights only between 9 AM - 6 PM, noise restrictions apply',
      description: 'High-density residential area',
      deployments: 5,
      lastUsed: '2024-02-10'
    },
    {
      id: 4,
      name: 'West Commercial Hub',
      type: 'Commercial',
      status: 'active',
      coordinates: '40.7505, -73.9934',
      radius: 1200,
      maxAltitude: 120,
      restrictions: 'Avoid peak business hours (12-2 PM)',
      description: 'Shopping centers and office buildings',
      deployments: 12,
      lastUsed: '2024-02-15'
    },
    {
      id: 5,
      name: 'Central Park Area',
      type: 'Recreational',
      status: 'active',
      coordinates: '40.7829, -73.9654',
      radius: 800,
      maxAltitude: 80,
      restrictions: 'No flights during events, wildlife protection zones',
      description: 'Public park and recreational facilities',
      deployments: 8,
      lastUsed: '2024-02-12'
    },
    {
      id: 6,
      name: 'Airport Buffer Zone',
      type: 'Restricted',
      status: 'no-fly',
      coordinates: '40.6413, -73.7781',
      radius: 8000,
      maxAltitude: 0,
      restrictions: 'Permanent no-fly zone - FAA restricted airspace',
      description: 'Airport safety buffer zone',
      deployments: 0,
      lastUsed: 'Never'
    }
  ]);

  const zoneTypes = [
    'Industrial',
    'Agricultural',
    'Residential',
    'Commercial',
    'Recreational',
    'Restricted',
    'Emergency',
    'Government'
  ];

  const getZoneIcon = (type) => {
    switch (type) {
      case 'Industrial':
        return <FaIndustry className="w-5 h-5 text-gray-600" />;
      case 'Residential':
        return <FaHome className="w-5 h-5 text-blue-600" />;
      case 'Commercial':
        return <FaStore className="w-5 h-5 text-green-600" />;
      case 'Recreational':
        return <FaTree className="w-5 h-5 text-green-500" />;
      case 'Agricultural':
        return <FaTree className="w-5 h-5 text-yellow-600" />;
      case 'Restricted':
        return <FaShieldAlt className="w-5 h-5 text-red-600" />;
      default:
        return <FaMapMarkerAlt className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'restricted':
        return 'bg-yellow-100 text-yellow-800';
      case 'no-fly':
        return 'bg-red-100 text-red-800';
      case 'maintenance':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <FaCheckCircle className="w-4 h-4 text-green-600" />;
      case 'restricted':
        return <FaExclamationTriangle className="w-4 h-4 text-yellow-600" />;
      case 'no-fly':
        return <FaShieldAlt className="w-4 h-4 text-red-600" />;
      case 'maintenance':
        return <FaCog className="w-4 h-4 text-gray-600" />;
      default:
        return <FaCheckCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const filteredZones = deploymentZones.filter(zone => {
    const matchesSearch = !searchTerm || 
      zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      zone.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      zone.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || zone.status === statusFilter;
    const matchesType = typeFilter === 'all' || zone.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const handleAddZone = () => {
    if (!zoneName || !zoneType || !coordinates) {
      alert('Please fill in all required fields');
      return;
    }

    const newZone = {
      id: Date.now(),
      name: zoneName,
      type: zoneType,
      status: zoneStatus,
      coordinates,
      radius,
      maxAltitude,
      restrictions,
      description,
      deployments: 0,
      lastUsed: 'Never'
    };

    setDeploymentZones([...deploymentZones, newZone]);
    setShowAddModal(false);
    resetForm();
  };

  const handleEditZone = (zone) => {
    setEditingZone(zone);
    setZoneName(zone.name);
    setZoneType(zone.type);
    setZoneStatus(zone.status);
    setCoordinates(zone.coordinates);
    setRadius(zone.radius);
    setMaxAltitude(zone.maxAltitude);
    setRestrictions(zone.restrictions);
    setDescription(zone.description);
    setShowAddModal(true);
  };

  const handleUpdateZone = () => {
    if (!zoneName || !zoneType || !coordinates) {
      alert('Please fill in all required fields');
      return;
    }

    const updatedZones = deploymentZones.map(zone => 
      zone.id === editingZone.id 
        ? {
            ...zone,
            name: zoneName,
            type: zoneType,
            status: zoneStatus,
            coordinates,
            radius,
            maxAltitude,
            restrictions,
            description
          }
        : zone
    );

    setDeploymentZones(updatedZones);
    setShowAddModal(false);
    setEditingZone(null);
    resetForm();
  };

  const handleDeleteZone = (id) => {
    const zone = deploymentZones.find(z => z.id === id);
    if (zone && zone.deployments > 0) {
      if (!window.confirm(`This zone has ${zone.deployments} deployments. Are you sure you want to delete it?`)) {
        return;
      }
    } else if (!window.confirm('Are you sure you want to delete this zone?')) {
      return;
    }
    
    setDeploymentZones(deploymentZones.filter(zone => zone.id !== id));
  };

  const handleToggleStatus = (id) => {
    const zone = deploymentZones.find(z => z.id === id);
    if (!zone) return;

    let newStatus;
    if (zone.status === 'active') {
      newStatus = 'restricted';
    } else if (zone.status === 'restricted') {
      newStatus = 'no-fly';
    } else {
      newStatus = 'active';
    }

    setDeploymentZones(deploymentZones.map(z => 
      z.id === id ? { ...z, status: newStatus } : z
    ));
  };

  const resetForm = () => {
    setZoneName('');
    setZoneType('');
    setZoneStatus('active');
    setCoordinates('');
    setRadius(1000);
    setMaxAltitude(120);
    setRestrictions('');
    setDescription('');
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admindeployment')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Deployment</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaMapMarkerAlt className="text-orange-600" />
                  Manage Zones
                </h2>
                <p className="text-gray-600 mt-1">Configure and manage deployment zones and restrictions</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                <Plus size={18} />
                <span className="hidden sm:inline">Add Zone</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Zones</p>
                  <p className="text-2xl font-bold text-gray-900">{deploymentZones.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FaMapMarkerAlt className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Zones</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {deploymentZones.filter(z => z.status === 'active').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FaCheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Restricted</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {deploymentZones.filter(z => z.status === 'restricted' || z.status === 'no-fly').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <FaShieldAlt className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Deployments</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {deploymentZones.reduce((sum, zone) => sum + zone.deployments, 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <FaCog className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search by name, type, or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="restricted">Restricted</option>
                  <option value="no-fly">No-Fly</option>
                  <option value="maintenance">Maintenance</option>
                </select>

                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  <option value="all">All Types</option>
                  {zoneTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Zones Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FaMapMarkerAlt className="text-orange-600" />
                Deployment Zones ({filteredZones.length})
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Zone
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location & Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Restrictions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredZones.map((zone) => (
                    <tr key={zone.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                              {getZoneIcon(zone.type)}
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{zone.name}</div>
                            <div className="text-xs text-gray-500">{zone.type}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{zone.coordinates}</div>
                        <div className="text-xs text-gray-500">
                          Radius: {zone.radius}m | Max Alt: {zone.maxAltitude}m
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {zone.restrictions || 'No restrictions'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{zone.deployments} deployments</div>
                        <div className="text-xs text-gray-500">Last: {zone.lastUsed}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(zone.status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(zone.status)}`}>
                            {zone.status.charAt(0).toUpperCase() + zone.status.slice(1).replace('-', ' ')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2">
                          <button
                            onClick={() => handleToggleStatus(zone.id)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                            title="Toggle Status"
                          >
                            {zone.status === 'active' ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
                          </button>
                          <button
                            onClick={() => handleEditZone(zone)}
                            className="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                            title="Edit Zone"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteZone(zone.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                            title="Delete Zone"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredZones.length === 0 && (
                <div className="text-center py-12">
                  <FaMapMarkerAlt className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No zones found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                      ? 'No zones match your current filters.'
                      : 'Get started by creating your first deployment zone.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Zone Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaMapMarkerAlt className="text-orange-600" />
                  {editingZone ? 'Edit Zone' : 'Add New Zone'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    setEditingZone(null);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Zone Name *
                  </label>
                  <input
                    type="text"
                    value={zoneName}
                    onChange={(e) => setZoneName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="Enter zone name..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Zone Type *
                  </label>
                  <select
                    value={zoneType}
                    onChange={(e) => setZoneType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  >
                    <option value="">Select type...</option>
                    {zoneTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Coordinates *
                  </label>
                  <input
                    type="text"
                    value={coordinates}
                    onChange={(e) => setCoordinates(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="40.7128, -74.0060"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={zoneStatus}
                    onChange={(e) => setZoneStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                    <option value="active">Active</option>
                    <option value="restricted">Restricted</option>
                    <option value="no-fly">No-Fly</option>
                    <option value="maintenance">Maintenance</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Radius (meters)
                  </label>
                  <input
                    type="number"
                    min="100"
                    max="50000"
                    value={radius}
                    onChange={(e) => setRadius(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Altitude (meters)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="400"
                    value={maxAltitude}
                    onChange={(e) => setMaxAltitude(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Restrictions
                </label>
                <textarea
                  value={restrictions}
                  onChange={(e) => setRestrictions(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  placeholder="Enter any flight restrictions or special conditions..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  placeholder="Brief description of the zone..."
                />
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex items-center justify-end gap-4">
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    setEditingZone(null);
                    resetForm();
                  }}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={editingZone ? handleUpdateZone : handleAddZone}
                  className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                >
                  <Save size={16} />
                  {editingZone ? 'Update Zone' : 'Add Zone'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManageZones;
