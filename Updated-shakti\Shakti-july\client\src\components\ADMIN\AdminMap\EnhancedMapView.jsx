import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";

import {
  MapPin,
  Building,
  User,
  Phone,
  Mail,
  Calendar,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye
} from "lucide-react";
import mapDataService from './MapDataService';
import {
  OrganizationListSkeleton,
  MapSkeleton,
  MapOverlayLoading,
  ConnectionStatus
} from './MapLoadingComponents';

// Create dynamic organization icon
const createOrganizationIcon = (status, activeDrones, totalDrones) => {
  const colors = {
    active: '#10b981',
    pending: '#f59e0b',
    inactive: '#6b7280'
  };

  const color = colors[status] || colors.inactive;
  const percentage = totalDrones > 0 ? (activeDrones / totalDrones) * 100 : 0;

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${color};
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      ">
        <div style="
          width: 16px;
          height: 16px;
          background-color: white;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div style="
            width: 8px;
            height: 8px;
            background-color: ${color};
            border-radius: 1px;
          "></div>
        </div>
        ${percentage > 0 ? `
          <div style="
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            background-color: ${percentage > 70 ? '#10b981' : percentage > 30 ? '#f59e0b' : '#ef4444'};
            border: 2px solid white;
            border-radius: 50%;
            font-size: 8px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
          ">${Math.round(percentage)}</div>
        ` : ''}
      </div>
    `,
    className: 'custom-org-icon',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16],
  });
};

// Create dynamic individual icon
const createIndividualIcon = (status, activeDrones, totalDrones, gender) => {
  const colors = {
    approved: '#10b981',
    pending: '#f59e0b',
    rejected: '#ef4444',
    suspended: '#6b7280'
  };

  const color = colors[status] || colors.pending;
  const percentage = totalDrones > 0 ? (activeDrones / totalDrones) * 100 : 0;
  const genderIcon = gender === 'female' ? '♀' : gender === 'male' ? '♂' : '●';

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${color};
        width: 28px;
        height: 28px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        font-size: 12px;
        color: white;
        font-weight: bold;
      ">
        ${genderIcon}
        ${percentage > 0 ? `
          <div style="
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 10px;
            height: 10px;
            background-color: ${percentage > 70 ? '#10b981' : percentage > 30 ? '#f59e0b' : '#ef4444'};
            border: 1px solid white;
            border-radius: 50%;
            font-size: 7px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
          ">${Math.round(percentage)}</div>
        ` : ''}
      </div>
    `,
    className: 'custom-individual-icon',
    iconSize: [28, 28],
    iconAnchor: [14, 14],
    popupAnchor: [0, -14],
  });
};

const EnhancedMapView = ({ searchQuery = '', filters = {}, viewType = 'organizations', onEntitySelect }) => {
  const [organizations, setOrganizations] = useState([]);
  const [individuals, setIndividuals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState(null);
  const [selectedEntityType, setSelectedEntityType] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Load data
  useEffect(() => {
    loadData();

    // Subscribe to updates
    const unsubscribeOrgs = mapDataService.subscribe('organizations', (updatedOrgs) => {
      setOrganizations(updatedOrgs);
      setLastUpdate(new Date().toLocaleTimeString());
    });

    const unsubscribeInds = mapDataService.subscribe('individuals', (updatedInds) => {
      setIndividuals(updatedInds);
      setLastUpdate(new Date().toLocaleTimeString());
    });

    return () => {
      unsubscribeOrgs();
      unsubscribeInds();
    };
  }, []);

  // Handle search and filters
  useEffect(() => {
    if (searchQuery || filters.status !== 'all') {
      filterData();
    } else {
      loadData();
    }
  }, [searchQuery, filters, viewType]);

  const loadData = async () => {
    try {
      setIsLoading(true);

      if (viewType === 'organizations') {
        const orgs = await mapDataService.fetchOrganizations(filters);
        setOrganizations(orgs);
        setIndividuals([]); // Clear individuals when viewing organizations
      } else if (viewType === 'individuals') {
        const inds = await mapDataService.fetchIndividuals(filters);
        setIndividuals(inds);
        setOrganizations([]); // Clear organizations when viewing individuals
      }

      setLastUpdate(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterData = async () => {
    setIsUpdating(true);
    try {
      if (viewType === 'organizations') {
        let filteredOrgs = mapDataService.searchOrganizations(searchQuery);
        if (filters.status !== 'all') {
          filteredOrgs = filteredOrgs.filter(org => org.status === filters.status);
        }
        setOrganizations(filteredOrgs);
        setIndividuals([]); // Clear individuals when viewing organizations
      } else if (viewType === 'individuals') {
        let filteredInds = mapDataService.searchIndividuals(searchQuery);
        if (filters.status !== 'all') {
          filteredInds = filteredInds.filter(ind => ind.status === filters.status);
        }
        setIndividuals(filteredInds);
        setOrganizations([]); // Clear organizations when viewing individuals
      }

      setLastUpdate(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Failed to filter data:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEntityClick = async (entity, type) => {
    setSelectedEntity(entity);
    setSelectedEntityType(type);

    // Call the parent callback if provided
    if (onEntitySelect) {
      onEntitySelect(entity, type);
    }
  };

  const getStatusIcon = (status, type) => {
    if (type === 'organization') {
      switch (status) {
        case 'active': return <CheckCircle className="w-4 h-4 text-green-600" />;
        case 'pending': return <Clock className="w-4 h-4 text-amber-600" />;
        case 'inactive': return <AlertCircle className="w-4 h-4 text-gray-600" />;
        default: return <Building className="w-4 h-4 text-gray-600" />;
      }
    } else {
      switch (status) {
        case 'approved': return <CheckCircle className="w-4 h-4 text-green-600" />;
        case 'pending': return <Clock className="w-4 h-4 text-amber-600" />;
        case 'rejected': return <AlertCircle className="w-4 h-4 text-red-600" />;
        case 'suspended': return <AlertCircle className="w-4 h-4 text-gray-600" />;
        default: return <User className="w-4 h-4 text-gray-600" />;
      }
    }
  };

  const getStatusColor = (status, type) => {
    if (type === 'organization') {
      switch (status) {
        case 'active': return 'text-green-600 bg-green-50';
        case 'pending': return 'text-amber-600 bg-amber-50';
        case 'inactive': return 'text-gray-600 bg-gray-50';
        default: return 'text-gray-600 bg-gray-50';
      }
    } else {
      switch (status) {
        case 'approved': return 'text-green-600 bg-green-50';
        case 'pending': return 'text-amber-600 bg-amber-50';
        case 'rejected': return 'text-red-600 bg-red-50';
        case 'suspended': return 'text-gray-600 bg-gray-50';
        default: return 'text-gray-600 bg-gray-50';
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex w-full h-full overflow-hidden">
        <OrganizationListSkeleton />
        <MapSkeleton />
      </div>
    );
  }

  const hasData = organizations.length > 0 || individuals.length > 0;

  if (!hasData) {
    return (
      <div className="flex flex-col xl:flex-row w-full h-full overflow-hidden">
        <div className="w-full xl:w-80 h-1/3 xl:h-full bg-white border-r border-gray-200 p-6 flex items-center justify-center">
          <div className="text-center">
            {viewType === 'organizations' ? (
              <Building className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            ) : (
              <User className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            )}
            <h3 className="text-lg font-semibold text-gray-700 mb-2">No data found</h3>
            <p className="text-gray-500 text-sm">
              {searchQuery
                ? "Try adjusting your search query"
                : `No ${viewType} available`
              }
            </p>
          </div>
        </div>
        <div className="flex-1 bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">Select an entity to view on map</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row w-full h-full overflow-hidden relative">
      {/* Loading overlay */}
      {isUpdating && <MapOverlayLoading message="Updating data..." />}

      {/* Left Sidebar - Entity List */}
      <div className="entity-sidebar w-full lg:w-80 xl:w-96 h-1/3 lg:h-full bg-white border-r border-gray-200 overflow-hidden shadow-sm flex flex-col">
        {/* Header */}
        <div className="px-3 py-2 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 flex-shrink-0">
          <h2 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
            {viewType === 'organizations' ? (
              <>
                <Building className="w-4 h-4 text-blue-600" />
                <span>Organizations</span>
                <span className="ml-auto bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {organizations.length}
                </span>
              </>
            ) : (
              <>
                <User className="w-4 h-4 text-green-600" />
                <span>Individuals</span>
                <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  {individuals.length}
                </span>
              </>
            )}
          </h2>
        </div>

        {/* Entity List */}
        <div className="entity-list-container flex-1 p-2 lg:p-3 overflow-y-auto min-h-0">
          {/* Organizations */}
          {viewType === 'organizations' && (
            <div className="space-y-2">
              {organizations.map((org) => (
                <div
                  key={org.id}
                  className={`entity-card bg-white border rounded-lg p-2 lg:p-3 text-black transition-all duration-200 cursor-pointer hover:shadow-md hover:border-blue-300 ${
                    selectedEntity?.id === org.id && selectedEntityType === 'organization'
                      ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-300'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleEntityClick(org, 'organization')}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-sm font-semibold text-gray-900 truncate pr-2">{org.name}</h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${getStatusColor(org.status, 'organization')}`}>
                      {org.status}
                    </div>
                  </div>

                  <p className="text-xs text-gray-600 mb-2 truncate">{org.orgId}</p>

                  <div className="flex items-center text-xs text-gray-600 mb-2">
                    <MapPin className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{org.state}, {org.district}</span>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center text-gray-700">
                      <Activity className="w-3 h-3 mr-1" />
                      <span>{org.activeDrones}/{org.totalDrones} Active</span>
                    </div>
                    <div className="flex items-center text-gray-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      {new Date(org.registrationDate).getFullYear()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Individuals */}
          {viewType === 'individuals' && (
            <div className="space-y-2">
              {individuals.map((individual) => (
                <div
                  key={individual.id}
                  className={`entity-card bg-white border rounded-lg p-2 lg:p-3 text-black transition-all duration-200 cursor-pointer hover:shadow-md hover:border-green-300 ${
                    selectedEntity?.id === individual.id && selectedEntityType === 'individual'
                      ? 'ring-2 ring-green-500 bg-green-50 border-green-300'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleEntityClick(individual, 'individual')}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-sm font-semibold text-gray-900 truncate pr-2">{individual.fullName}</h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${getStatusColor(individual.status, 'individual')}`}>
                      {individual.status}
                    </div>
                  </div>

                  <p className="text-xs text-gray-600 mb-2 truncate">{individual.individualId}</p>

                  <div className="flex items-center text-xs text-gray-600 mb-2">
                    <MapPin className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{individual.state}, {individual.city}</span>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center text-gray-700">
                      <Activity className="w-3 h-3 mr-1" />
                      <span>{individual.activeDrones}/{individual.totalDrones} Active</span>
                    </div>
                    <div className="flex items-center text-gray-500">
                      <User className="w-3 h-3 mr-1" />
                      <span className="capitalize">{individual.gender}, {individual.age}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Right Map Container */}
      <div className="map-container flex-1 relative h-2/3 lg:h-full bg-gray-100 min-h-0">
        <MapContainer
          center={[20.5937, 78.9629]}
          zoom={6}
          style={{ height: "100%", width: "100%" }}
          className="z-0"
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />

          {/* Organization Markers */}
          {viewType === 'organizations' &&
            organizations.map((org) => (
              <Marker
                key={`org-${org.id}`}
                position={org.position}
                icon={createOrganizationIcon(org.status, org.activeDrones, org.totalDrones)}
                eventHandlers={{
                  click: () => handleEntityClick(org, 'organization'),
                }}
              >
                <Popup className="custom-popup">
                  <div className="p-2 min-w-[250px]">
                    <div className="flex items-center gap-2 mb-3">
                      {getStatusIcon(org.status, 'organization')}
                      <h3 className="font-bold text-gray-800">{org.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(org.status, 'organization')}`}>
                        {org.status}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Organization ID:</span>
                        <span className="font-medium">{org.orgId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-medium">{org.state}, {org.district}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Drones:</span>
                        <span className="font-medium">{org.totalDrones}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Active Drones:</span>
                        <span className="font-medium text-green-600">{org.activeDrones}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Contact Person:</span>
                        <span className="font-medium">{org.contactPerson}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-600">{org.phone}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-600">{org.email}</span>
                      </div>
                    </div>

                    <button
                      onClick={() => handleEntityClick(org, 'organization')}
                      className="w-full mt-3 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      View Drone Details
                    </button>
                  </div>
                </Popup>
              </Marker>
            ))}

          {/* Individual Markers */}
          {viewType === 'individuals' &&
            individuals.map((individual) => (
              <Marker
                key={`ind-${individual.id}`}
                position={individual.position}
                icon={createIndividualIcon(individual.status, individual.activeDrones, individual.totalDrones, individual.gender)}
                eventHandlers={{
                  click: () => handleEntityClick(individual, 'individual'),
                }}
              >
                <Popup className="custom-popup">
                  <div className="p-2 min-w-[250px]">
                    <div className="flex items-center gap-2 mb-3">
                      {getStatusIcon(individual.status, 'individual')}
                      <h3 className="font-bold text-gray-800">{individual.fullName}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(individual.status, 'individual')}`}>
                        {individual.status}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Individual ID:</span>
                        <span className="font-medium">{individual.individualId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-medium">{individual.state}, {individual.city}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Age & Gender:</span>
                        <span className="font-medium capitalize">{individual.age}, {individual.gender}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Drones:</span>
                        <span className="font-medium">{individual.totalDrones}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Active Drones:</span>
                        <span className="font-medium text-green-600">{individual.activeDrones}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-600">{individual.phone}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-600">{individual.email}</span>
                      </div>
                    </div>

                    <button
                      onClick={() => handleEntityClick(individual, 'individual')}
                      className="w-full mt-3 px-3 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      View Drone Details
                    </button>
                  </div>
                </Popup>
              </Marker>
            ))}
        </MapContainer>

        {/* Connection Status */}
        <ConnectionStatus isConnected={true} lastUpdate={lastUpdate} />
      </div>
    </div>
  );
};

export default EnhancedMapView;
