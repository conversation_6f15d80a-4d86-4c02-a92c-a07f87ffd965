import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  Shield,
  Lock,
  Key,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Database,
  Wifi,
  Server,
  FileText,
  Save,
  RefreshCw,
  Settings,
  Fingerprint,
  Smartphone,
  Mail,
  Bell
} from 'lucide-react';

const SecuritySettings = () => {
  const [settings, setSettings] = useState({
    // Password Policy
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    passwordExpiration: 90,
    preventPasswordReuse: 5,
    
    // Account Security
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    sessionTimeout: 60,
    requireTwoFactor: false,
    allowRememberMe: true,
    forcePasswordChange: false,
    
    // Data Security
    enableEncryption: true,
    encryptionLevel: 'AES-256',
    enableBackups: true,
    backupFrequency: 'daily',
    backupRetention: 30,
    enableAuditLog: true,
    auditLogRetention: 365,
    
    // Network Security
    enableSSL: true,
    requireVPN: false,
    allowedIPRanges: '***********/24, 10.0.0.0/8',
    enableFirewall: true,
    blockSuspiciousIPs: true,
    
    // Access Control
    enableRoleBasedAccess: true,
    requireApprovalForNewUsers: true,
    autoDeactivateInactiveUsers: true,
    inactivityThreshold: 90,
    enableGuestAccess: false,
    
    // Compliance
    enableSOXCompliance: true,
    enableHIPAACompliance: false,
    enableGDPRCompliance: true,
    enablePCICompliance: false,
    dataRetentionPeriod: 2555,
    enableRightToErasure: true
  });

  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [showPasswordPolicy, setShowPasswordPolicy] = useState(false);

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setUnsavedChanges(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUnsavedChanges(false);
      setLastSaved(new Date().toLocaleString());
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setSettings({
      minPasswordLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      passwordExpiration: 90,
      preventPasswordReuse: 5,
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      sessionTimeout: 60,
      requireTwoFactor: false,
      allowRememberMe: true,
      forcePasswordChange: false,
      enableEncryption: true,
      encryptionLevel: 'AES-256',
      enableBackups: true,
      backupFrequency: 'daily',
      backupRetention: 30,
      enableAuditLog: true,
      auditLogRetention: 365,
      enableSSL: true,
      requireVPN: false,
      allowedIPRanges: '***********/24, 10.0.0.0/8',
      enableFirewall: true,
      blockSuspiciousIPs: true,
      enableRoleBasedAccess: true,
      requireApprovalForNewUsers: true,
      autoDeactivateInactiveUsers: true,
      inactivityThreshold: 90,
      enableGuestAccess: false,
      enableSOXCompliance: true,
      enableHIPAACompliance: false,
      enableGDPRCompliance: true,
      enablePCICompliance: false,
      dataRetentionPeriod: 2555,
      enableRightToErasure: true
    });
    setUnsavedChanges(false);
  };

  const getSecurityScore = () => {
    let score = 0;
    const checks = [
      settings.minPasswordLength >= 8,
      settings.requireUppercase && settings.requireLowercase && settings.requireNumbers && settings.requireSpecialChars,
      settings.requireTwoFactor,
      settings.enableEncryption,
      settings.enableSSL,
      settings.enableAuditLog,
      settings.enableRoleBasedAccess,
      settings.maxLoginAttempts <= 5,
      settings.sessionTimeout <= 60,
      settings.enableFirewall
    ];
    
    score = (checks.filter(Boolean).length / checks.length) * 100;
    return Math.round(score);
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const securityScore = getSecurityScore();

  const headerActions = (
    <div className="flex items-center gap-3">
      {unsavedChanges && (
        <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-lg border border-amber-200">
          <AlertTriangle className="w-4 h-4" />
          <span className="text-sm font-medium">Unsaved Changes</span>
        </div>
      )}
      
      {lastSaved && (
        <div className="text-sm text-gray-500">
          Last saved: {lastSaved}
        </div>
      )}

      <div className="flex items-center gap-2 px-3 py-1 bg-white border border-gray-200 rounded-lg">
        <Shield className="w-4 h-4 text-blue-500" />
        <span className="text-sm font-medium text-gray-700">Security Score:</span>
        <span className={`text-sm font-bold ${getScoreColor(securityScore)}`}>
          {securityScore}%
        </span>
      </div>

      <button
        onClick={handleReset}
        className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <RefreshCw className="w-4 h-4" />
        Reset
      </button>

      <button
        onClick={handleSave}
        disabled={saving || !unsavedChanges}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
      >
        <Save className="w-4 h-4" />
        {saving ? 'Saving...' : 'Save Changes'}
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Security Settings"
      subtitle="Configure security policies, access controls, and compliance settings"
      actions={headerActions}
    >
      <div className="space-y-8">
        {/* Security Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Shield className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Security Overview</h3>
                <p className="text-sm text-gray-600 mt-1">Current security status and recommendations</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="relative w-24 h-24 mx-auto mb-4">
                  <div className="w-24 h-24 rounded-full bg-gray-200">
                    <div 
                      className={`w-24 h-24 rounded-full ${getScoreBgColor(securityScore)} flex items-center justify-center`}
                      style={{
                        background: `conic-gradient(${securityScore >= 90 ? '#10b981' : securityScore >= 70 ? '#f59e0b' : '#ef4444'} ${securityScore * 3.6}deg, #e5e7eb 0deg)`
                      }}
                    >
                      <div className="w-20 h-20 rounded-full bg-white flex items-center justify-center">
                        <span className={`text-xl font-bold ${getScoreColor(securityScore)}`}>
                          {securityScore}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-900">Security Score</h4>
                <p className="text-sm text-gray-600">Overall security rating</p>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-gray-900">Security Status</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Password Policy</span>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Encryption</span>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Two-Factor Auth</span>
                    {settings.requireTwoFactor ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-yellow-500" />
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Audit Logging</span>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-gray-900">Recommendations</h4>
                <div className="space-y-2">
                  {!settings.requireTwoFactor && (
                    <div className="flex items-start gap-2 p-2 bg-yellow-50 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                      <span className="text-xs text-yellow-700">Enable two-factor authentication</span>
                    </div>
                  )}
                  {settings.sessionTimeout > 60 && (
                    <div className="flex items-start gap-2 p-2 bg-yellow-50 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                      <span className="text-xs text-yellow-700">Reduce session timeout</span>
                    </div>
                  )}
                  {settings.maxLoginAttempts > 5 && (
                    <div className="flex items-start gap-2 p-2 bg-yellow-50 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                      <span className="text-xs text-yellow-700">Lower max login attempts</span>
                    </div>
                  )}
                  {securityScore >= 90 && (
                    <div className="flex items-start gap-2 p-2 bg-green-50 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span className="text-xs text-green-700">Excellent security configuration</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Password Policy */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef3c7'}}>
                <Key className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Password Policy</h3>
                <p className="text-sm text-gray-600 mt-1">Configure password requirements and security rules</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                <input
                  type="number"
                  min="6"
                  max="32"
                  value={settings.minPasswordLength}
                  onChange={(e) => handleInputChange('minPasswordLength', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Password Expiration (days)</label>
                <input
                  type="number"
                  min="30"
                  max="365"
                  value={settings.passwordExpiration}
                  onChange={(e) => handleInputChange('passwordExpiration', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Prevent Password Reuse (last N passwords)</label>
                <input
                  type="number"
                  min="0"
                  max="24"
                  value={settings.preventPasswordReuse}
                  onChange={(e) => handleInputChange('preventPasswordReuse', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Password Requirements</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireUppercase}
                    onChange={(e) => handleInputChange('requireUppercase', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Require Uppercase Letters</div>
                    <div className="text-xs text-gray-500">At least one uppercase letter (A-Z)</div>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireLowercase}
                    onChange={(e) => handleInputChange('requireLowercase', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Require Lowercase Letters</div>
                    <div className="text-xs text-gray-500">At least one lowercase letter (a-z)</div>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireNumbers}
                    onChange={(e) => handleInputChange('requireNumbers', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Require Numbers</div>
                    <div className="text-xs text-gray-500">At least one number (0-9)</div>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireSpecialChars}
                    onChange={(e) => handleInputChange('requireSpecialChars', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Require Special Characters</div>
                    <div className="text-xs text-gray-500">At least one special character (!@#$%^&*)</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Account Security */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <Lock className="w-5 h-5 text-green-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Account Security</h3>
                <p className="text-sm text-gray-600 mt-1">Login security and session management settings</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                <input
                  type="number"
                  min="3"
                  max="10"
                  value={settings.maxLoginAttempts}
                  onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Lockout Duration (minutes)</label>
                <input
                  type="number"
                  min="5"
                  max="1440"
                  value={settings.lockoutDuration}
                  onChange={(e) => handleInputChange('lockoutDuration', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                <input
                  type="number"
                  min="15"
                  max="480"
                  value={settings.sessionTimeout}
                  onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Authentication Options</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.requireTwoFactor}
                    onChange={(e) => handleInputChange('requireTwoFactor', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Require Two-Factor Authentication</div>
                    <div className="text-xs text-gray-500">Mandatory 2FA for all users</div>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.allowRememberMe}
                    onChange={(e) => handleInputChange('allowRememberMe', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Allow "Remember Me"</div>
                    <div className="text-xs text-gray-500">Users can stay logged in longer</div>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.forcePasswordChange}
                    onChange={(e) => handleInputChange('forcePasswordChange', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Force Password Change on First Login</div>
                    <div className="text-xs text-gray-500">New users must change default password</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default SecuritySettings;
