import React, { useState, useEffect } from "react";
import { useSearchPara<PERSON>, useNavigate } from "react-router-dom";
import OrgSidebar from "../common/OrgSidebar";
import {
  Bell,
  ArrowLeft,
  Download,
  Search,
  MapPin,
  Battery,
  Thermometer,
  Gauge,
  Clock,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Zap,
  Fuel,
  Navigation,
  Maximize2
} from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "./OrgMapStyles.css";

// Generate realistic drone data based on URL params
const generateDroneData = (droneId) => {
  const droneProfiles = {
    'PRYMAA95170': {
      name: 'Drone Alpha',
      organization: 'SkyFarm Co.',
      model: 'AgriSpray Pro X1',
      area: 'Delhi NCR',
      baseLocation: { lat: 28.6139, lng: 77.209 }
    },
    'PRYMAA95171': {
      name: 'Drone Beta',
      organization: 'GreenField Drones',
      model: 'CropGuard Elite',
      area: 'Mumbai',
      baseLocation: { lat: 19.076, lng: 72.8777 }
    },
    'PRYMAA95172': {
      name: 'Drone Gamma',
      organization: 'AgroTech Solutions',
      model: 'FarmMaster 3000',
      area: 'Chennai',
      baseLocation: { lat: 13.0827, lng: 80.2707 }
    }
  };

  const profile = droneProfiles[droneId] || droneProfiles['PRYMAA95170'];

  return {
    id: droneId || 'PRYMAA95170',
    ...profile,
    status: 'ACTIVE',
    battery: 83,
    temperature: 27,
    gpsStatus: 'Connected',
    altitude: 45,
    speed: 18,
    fuelLevel: 78,
    lastMaintenance: '15 days ago',
    missionSuccessRate: 98.7,
    flightDuration: '1h 23m',
    areaCovered: 4.8,
    sprayVolume: 12,
    batteryUsed: 65,
    temperaturePeak: 42
  };
};

// Generate realistic flight path
const generateFlightPath = (baseLocation) => {
  const path = [];
  let currentLat = baseLocation.lat;
  let currentLng = baseLocation.lng;

  for (let i = 0; i < 20; i++) {
    path.push([currentLat, currentLng]);
    currentLat += (Math.random() - 0.5) * 0.01;
    currentLng += (Math.random() - 0.5) * 0.01;
  }

  return path;
};

// Generate activity logs
const generateLogs = (count = 50) => {
  const logs = [];
  const now = new Date();

  for (let i = 0; i < count; i++) {
    const time = new Date(now.getTime() - i * 5 * 60 * 1000); // 5 minutes apart
    logs.push({
      id: i,
      time: time.toISOString(),
      battery: Math.max(20, 100 - i * 2),
      temp: Math.floor(Math.random() * 15) + 25,
      lat: (28.6139 + (Math.random() - 0.5) * 0.01).toFixed(6),
      lng: (77.209 + (Math.random() - 0.5) * 0.01).toFixed(6),
      altitude: Math.floor(Math.random() * 30) + 30,
      speed: Math.floor(Math.random() * 20) + 10,
      gps: Math.random() > 0.05, // 95% GPS success rate
      event: i % 10 === 0 ? ['Mission Start', 'Waypoint Reached', 'Spray Complete', 'Battery Warning'][Math.floor(Math.random() * 4)] : null
    });
  }

  return logs;
};

const ViewLogs = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const droneId = searchParams.get('drone') || 'PRYMAA95170';

  const [droneData, setDroneData] = useState(generateDroneData(droneId));
  const [logs, setLogs] = useState(generateLogs(50));
  const [filteredLogs, setFilteredLogs] = useState(logs);
  const [flightPath] = useState(generateFlightPath(droneData.baseLocation));
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [isLiveMode, setIsLiveMode] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('battery');

  // Real-time updates
  useEffect(() => {
    if (!isLiveMode) return;

    const interval = setInterval(() => {
      setDroneData(prev => ({
        ...prev,
        battery: Math.max(0, Math.min(100, prev.battery + (Math.random() - 0.5) * 2)),
        temperature: Math.max(15, Math.min(50, prev.temperature + (Math.random() - 0.5) * 1)),
        speed: Math.max(0, Math.min(30, prev.speed + (Math.random() - 0.5) * 3))
      }));

      // Add new log entry
      const newLog = {
        id: Date.now(),
        time: new Date().toISOString(),
        battery: droneData.battery,
        temp: droneData.temperature,
        lat: (droneData.baseLocation.lat + (Math.random() - 0.5) * 0.001).toFixed(6),
        lng: (droneData.baseLocation.lng + (Math.random() - 0.5) * 0.001).toFixed(6),
        altitude: Math.floor(Math.random() * 30) + 30,
        speed: droneData.speed,
        gps: Math.random() > 0.05,
        event: Math.random() > 0.9 ? 'Waypoint Reached' : null
      };

      setLogs(prev => [newLog, ...prev.slice(0, 99)]);
    }, 10000);

    return () => clearInterval(interval);
  }, [isLiveMode, droneData.battery, droneData.temperature, droneData.speed, droneData.baseLocation]);

  // Filter logs
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.event?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.lat.includes(searchTerm) ||
        log.lng.includes(searchTerm)
      );
    }

    if (dateFilter) {
      const filterDate = new Date(dateFilter);
      filtered = filtered.filter(log => {
        const logDate = new Date(log.time);
        return logDate.toDateString() === filterDate.toDateString();
      });
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, dateFilter]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-100';
      case 'FLYING': return 'text-blue-600 bg-blue-100';
      case 'INACTIVE': return 'text-red-600 bg-red-100';
      case 'MAINTENANCE': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const exportToCSV = () => {
    const csvContent = [
      ['Time', 'Battery (%)', 'Temperature (°C)', 'Latitude', 'Longitude', 'Altitude (m)', 'Speed (km/h)', 'GPS Status', 'Event'],
      ...filteredLogs.map(log => [
        new Date(log.time).toLocaleString(),
        log.battery,
        log.temp,
        log.lat,
        log.lng,
        log.altitude,
        log.speed,
        log.gps ? 'Connected' : 'Disconnected',
        log.event || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `drone_${droneId}_logs_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="flex w-full bg-gradient-to-br from-gray-50 to-gray-100 text-black h-screen overflow-hidden">
      {/* Sidebar */}
      <OrgSidebar />

      {/* Main content */}
      <div className="ml-[18em] flex-1 flex flex-col h-screen overflow-hidden">
        {/* Professional Header */}
        <div className="bg-white shadow-lg border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/orgmapsection')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg">
                  <Activity className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{droneData.name} - Detailed Logs</h2>
                  <p className="text-sm text-gray-600">{droneData.organization} • {droneData.model}</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Live Mode Toggle */}
              <button
                onClick={() => setIsLiveMode(!isLiveMode)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isLiveMode
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <div className="flex items-center gap-2">
                  {isLiveMode && <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>}
                  {isLiveMode ? 'Live' : 'Paused'}
                </div>
              </button>

              {/* Export Button */}
              <button
                onClick={exportToCSV}
                className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
              >
                <Download className="w-4 h-4" />
              </button>

              {/* Notifications */}
              <div className="relative">
                <button className="p-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                  <Bell className="w-5 h-5 text-gray-700" />
                </button>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Body */}
        <div className="flex-1 flex flex-col overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
          {/* Enhanced Drone Status Cards */}
          <div className="p-4 flex-shrink-0">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Activity className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">Drone ID</p>
                  <h3 className="font-bold text-gray-800">{droneData.id}</h3>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(droneData.status)}`}>
                {droneData.status}
              </span>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Battery className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">Battery Level</p>
                  <h3 className="font-bold text-gray-800">{droneData.battery}%</h3>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    droneData.battery > 50 ? 'bg-green-500' :
                    droneData.battery > 20 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${droneData.battery}%` }}
                ></div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Thermometer className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">Temperature</p>
                  <h3 className="font-bold text-gray-800">{droneData.temperature}°C</h3>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {droneData.temperature > 40 ? (
                  <AlertTriangle className="w-3 h-3 text-red-500" />
                ) : (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                )}
                <span className="text-xs text-gray-500">
                  {droneData.temperature > 40 ? 'High' : 'Normal'}
                </span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <MapPin className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">GPS Status</p>
                  <h3 className="font-bold text-green-600">{droneData.gpsStatus}</h3>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-500">Live tracking</span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <Gauge className="w-4 h-4 text-indigo-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">Current Speed</p>
                  <h3 className="font-bold text-gray-800">{droneData.speed} km/h</h3>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {droneData.speed > 0 ? (
                  <TrendingUp className="w-3 h-3 text-blue-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-gray-500" />
                )}
                <span className="text-xs text-gray-500">
                  {droneData.speed > 0 ? 'Moving' : 'Stationary'}
                </span>
              </div>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <BarChart3 className="w-4 h-4 text-gray-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">Organization</p>
                  <h3 className="font-bold text-gray-800">{droneData.organization}</h3>
                </div>
              </div>
              <p className="text-xs text-gray-500">{droneData.area}</p>
            </div>
            </div>
          </div>

          {/* Enhanced Map and Statistics */}
          <div className="flex gap-4 p-4 pt-0 overflow-hidden h-[500px] lg:h-[600px] flex-shrink-0">
            {/* Live Map with Flight Path */}
            <div className="flex-1 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden flex flex-col h-full">
              <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="font-bold text-lg text-gray-800">Live Flight Tracking</h2>
                      <p className="text-sm text-gray-600">Real-time position and flight path</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 px-3 py-1 bg-green-100 rounded-full">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-green-700">Live</span>
                    </div>
                    <button className="p-2 hover:bg-white rounded-lg transition-colors">
                      <Maximize2 className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 relative h-full">
                <MapContainer
                  center={[droneData.baseLocation.lat, droneData.baseLocation.lng]}
                  zoom={12}
                  scrollWheelZoom={true}
                  style={{ width: '100%', height: '100%' }}
                >
                  <TileLayer
                    url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                    attribution='Tiles &copy; <a href="https://www.esri.com/">Esri</a>'
                  />

                  {/* Flight Path */}
                  <Polyline
                    positions={flightPath}
                    pathOptions={{
                      color: '#3B82F6',
                      weight: 3,
                      opacity: 0.8,
                      dashArray: '10, 5'
                    }}
                  />

                  {/* Current Position */}
                  <Marker
                    position={[droneData.baseLocation.lat, droneData.baseLocation.lng]}
                    icon={new L.DivIcon({
                      html: `
                        <div class="relative">
                          <div class="w-6 h-6 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                          <div class="absolute -top-1 -left-1 w-8 h-8 bg-blue-200 rounded-full animate-ping opacity-75"></div>
                        </div>
                      `,
                      iconSize: [24, 24],
                      iconAnchor: [12, 12],
                      className: 'custom-drone-marker'
                    })}
                  >
                    <Popup>
                      <div className="p-2">
                        <h3 className="font-bold text-gray-800 mb-2">{droneData.name}</h3>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Battery:</span>
                            <span className="font-medium">{droneData.battery}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Speed:</span>
                            <span className="font-medium">{droneData.speed} km/h</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Altitude:</span>
                            <span className="font-medium">{droneData.altitude} m</span>
                          </div>
                        </div>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              </div>
            </div>

            {/* Enhanced Statistics Panel */}
            <div className="w-80 space-y-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
              {/* Performance Metrics */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-green-600 rounded-lg">
                    <BarChart3 className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="font-bold text-lg text-gray-800">Performance Metrics</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Clock className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-700">Flight Duration</span>
                    </div>
                    <span className="text-sm font-bold text-blue-600">{droneData.flightDuration}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <MapPin className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">Area Covered</span>
                    </div>
                    <span className="text-sm font-bold text-green-600">{droneData.areaCovered} hectares</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Navigation className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-gray-700">Altitude (avg)</span>
                    </div>
                    <span className="text-sm font-bold text-purple-600">{droneData.altitude} m</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Gauge className="w-4 h-4 text-orange-600" />
                      <span className="text-sm font-medium text-gray-700">Speed (avg)</span>
                    </div>
                    <span className="text-sm font-bold text-orange-600">{droneData.speed} km/h</span>
                  </div>
                </div>
              </div>

              {/* Mission Statistics */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="font-bold text-lg text-gray-800">Mission Data</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Fuel className="w-4 h-4 text-indigo-600" />
                      <span className="text-sm font-medium text-gray-700">Spray Volume</span>
                    </div>
                    <span className="text-sm font-bold text-indigo-600">{droneData.sprayVolume} liters</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Battery className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm font-medium text-gray-700">Battery Used</span>
                    </div>
                    <span className="text-sm font-bold text-yellow-600">{droneData.batteryUsed}%</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Thermometer className="w-4 h-4 text-red-600" />
                      <span className="text-sm font-medium text-gray-700">Temperature Peak</span>
                    </div>
                    <span className="text-sm font-bold text-red-600">{droneData.temperaturePeak}°C</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-4 h-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">Success Rate</span>
                    </div>
                    <span className="text-sm font-bold text-gray-600">{droneData.missionSuccessRate}%</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <h3 className="font-bold text-lg text-gray-800 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button className="w-full p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium">
                    Download Full Report
                  </button>
                  <button className="w-full p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium">
                    Schedule Maintenance
                  </button>
                  <button className="w-full p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm font-medium">
                    View Analytics
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Activity Logs */}
          <div className="p-4 flex-shrink-0">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden max-h-[600px]">
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg">
                    <Activity className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">Activity Logs</h3>
                    <p className="text-sm text-gray-600">{filteredLogs.length} entries • Real-time monitoring</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search logs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />

                  <select
                    value={selectedMetric}
                    onChange={(e) => setSelectedMetric(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="battery">Battery</option>
                    <option value="temperature">Temperature</option>
                    <option value="speed">Speed</option>
                    <option value="altitude">Altitude</option>
                  </select>

                  <button
                    onClick={exportToCSV}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export CSV
                  </button>
                </div>
              </div>
            </div>

            {/* Enhanced Table */}
            <div className="overflow-x-auto max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Battery
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Temperature
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Altitude
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Speed
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      GPS
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Event
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLogs.slice(0, 20).map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          {new Date(log.time).toLocaleString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className="flex items-center gap-2">
                          <Battery className={`w-4 h-4 ${
                            log.battery > 50 ? 'text-green-500' :
                            log.battery > 20 ? 'text-yellow-500' : 'text-red-500'
                          }`} />
                          <span className={`font-medium ${
                            log.battery > 50 ? 'text-green-600' :
                            log.battery > 20 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {log.battery}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className="flex items-center gap-2">
                          <Thermometer className="w-4 h-4 text-orange-500" />
                          <span className="text-gray-900">{log.temp}°C</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-purple-500" />
                          <span>{log.lat}, {log.lng}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <Navigation className="w-4 h-4 text-blue-500" />
                          <span>{log.altitude}m</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <Gauge className="w-4 h-4 text-indigo-500" />
                          <span>{log.speed} km/h</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          log.gps
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {log.gps ? (
                            <>
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Connected
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="w-3 h-3 mr-1" />
                              Disconnected
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {log.event && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {log.event}
                          </span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">1</span> to <span className="font-medium">20</span> of{' '}
                  <span className="font-medium">{filteredLogs.length}</span> results
                </p>
                <div className="flex gap-2">
                  <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 transition-colors">
                    Previous
                  </button>
                  <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 transition-colors">
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ViewLogs;
