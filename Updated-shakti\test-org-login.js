const axios = require('axios');

// Test Organization Login and Drone Addition
const BASE_URL = 'http://localhost:5000/api';

async function testOrgLogin() {
  console.log('🧪 Testing Organization Login and Drone Addition...\n');

  try {
    // Step 1: Login as organization user
    console.log('1️⃣ Testing Organization Login...');
    
    const loginData = {
      username: 'testorg',
      password: 'Test123!',
      role: 'org'
    };

    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData);
    
    if (loginResponse.data.success) {
      console.log('✅ Organization login successful');
      console.log('   - User:', loginResponse.data.data.user.username);
      console.log('   - Role:', loginResponse.data.data.user.role);
      console.log('   - Token received:', loginResponse.data.data.token ? 'Yes' : 'No');
      
      const token = loginResponse.data.data.token;
      
      // Step 2: Test organization portal endpoints
      console.log('\n2️⃣ Testing Organization Portal Access...');
      
      const api = axios.create({
        baseURL: BASE_URL,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Test dashboard
      try {
        const dashboardResponse = await api.get('/org-portal/dashboard');
        console.log('✅ Dashboard access successful:', dashboardResponse.data.success);
      } catch (error) {
        console.log('❌ Dashboard access failed:', error.response?.data?.message || error.message);
      }

      // Test add drone
      console.log('\n3️⃣ Testing Add Drone...');
      
      const testDroneData = {
        name: 'Test Organization Drone',
        model: 'DJI Phantom 4 Pro',
        manufacturer: 'DJI',
        serialNumber: `ORG-TEST-${Date.now()}`,
        registrationNumber: `REG-ORG-${Date.now()}`,
        specifications: {
          type: 'quadcopter',
          weight: 1.4,
          maxFlightTime: 30,
          batteryCapacity: 5870,
          hasGPS: true
        },
        purchase: {
          purchaseDate: new Date().toISOString(),
          purchasePrice: 1500,
          vendor: 'DJI Store'
        }
      };

      try {
        const addDroneResponse = await api.post('/org-portal/drones', testDroneData);
        console.log('✅ Drone added successfully:', addDroneResponse.data.success);
        console.log('   - Drone ID:', addDroneResponse.data.data?.drone?._id);
        console.log('   - Drone Name:', addDroneResponse.data.data?.drone?.name);
      } catch (error) {
        console.log('❌ Add drone failed:', error.response?.data?.message || error.message);
        console.log('   - Status:', error.response?.status);
        console.log('   - Full error:', error.response?.data);
      }

      // Test get drones
      console.log('\n4️⃣ Testing Get Drones...');
      try {
        const dronesResponse = await api.get('/org-portal/drones');
        console.log('✅ Get drones successful:', dronesResponse.data.success);
        console.log('   - Total drones:', dronesResponse.data.data?.total || 0);
      } catch (error) {
        console.log('❌ Get drones failed:', error.response?.data?.message || error.message);
      }

    } else {
      console.log('❌ Organization login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting Organization Login Test...');
  console.log('🌐 Make sure the server is running on http://localhost:5000\n');
  
  testOrgLogin().catch(console.error);
}

module.exports = { testOrgLogin };
