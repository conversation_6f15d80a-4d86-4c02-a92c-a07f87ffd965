import React from 'react';
import { Routes, Route } from 'react-router-dom';
import GeneralSettings from './GeneralSettings';
import UserManagement from './UserManagement';
import SecuritySettings from './SecuritySettings';
import SystemConfiguration from './SystemConfiguration';

const Settings = () => {
  return (
    <Routes>
      <Route path="/" element={<GeneralSettings />} />
      <Route path="/general" element={<GeneralSettings />} />
      <Route path="/users" element={<UserManagement />} />
      <Route path="/security" element={<SecuritySettings />} />
      <Route path="/system" element={<SystemConfiguration />} />
    </Routes>
  );
};

export default Settings;
