import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  MapPinned,
  Building,
  User,
  Boxes,
  Truck,
  Bot,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronRight
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../../context/AuthContext";
import logo from "../../../assets/logo.png";

// ✅ Enhanced Navigation Item with better error handling
const NavItem = ({ icon, label, to, isActive, isMobile, onClick }) => {
  const navigate = useNavigate();

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Ensure navigation path is valid
      if (to && typeof to === 'string') {
        console.log(`AdminSidebar: Navigating to: ${to}`); // Debug log
        console.log(`AdminSidebar: Current location: ${window.location.pathname}`); // Debug log
        navigate(to, { replace: false });

        // Close mobile menu if applicable
        if (isMobile && onClick) {
          onClick();
        }
      } else {
        console.error(`AdminSidebar: Invalid navigation path: ${to}`);
      }
    } catch (error) {
      console.error('AdminSidebar: Navigation error:', error);
    }
  };

  return (
    <div
      className={`flex items-center gap-3 px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ${
        isActive
          ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
          : 'hover:bg-gray-100 text-gray-700 hover:border-l-4 hover:border-gray-300'
      }`}
      onClick={(e) => {
        console.log(`AdminSidebar: NavItem clicked - ${label}`);
        handleClick(e);
      }}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick(e);
        }
      }}
    >
      <div className={`transition-colors ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
        {icon}
      </div>
      <span className={`font-medium transition-colors ${isActive ? 'text-blue-600' : 'text-gray-700'}`}>
        {label}
      </span>
      {isActive && <ChevronRight className="ml-auto w-4 h-4 text-blue-600" />}
    </div>
  );
};

// ✅ Sidebar Component
const AdminSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Track window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = windowWidth < 1024;

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Enhanced logout handler
  const handleLogout = () => {
    try {
      auth.logout();
      navigate("/", { replace: true });
      console.log("AdminSidebar: User logged out successfully");
    } catch (error) {
      console.error("AdminSidebar: Logout error:", error);
    }
  };

  const isActive = (path) => {
    // Enhanced active state detection
    const currentPath = location.pathname;
    return currentPath === path ||
           (path === '/map' && currentPath === '/orgstatsmap') ||
           (path === '/organizationpage' && currentPath === '/organizationform') ||
           (path === '/individualpage' && (currentPath === '/individualform' || currentPath === '/admin/individuals')) ||
           (path === '/dronepage' && (currentPath === '/adddrone' || currentPath === '/dronelogs')) ||
           (path === '/inventory' && (currentPath === '/inventory-analytics' || currentPath === '/inventory-maintenance' || currentPath === '/inventory-settings' || currentPath === '/inventory-bulk-upload')) ||
           (path === '/admindeployment' && (currentPath === '/deployment-analytics' || currentPath === '/deployment-settings' || currentPath === '/schedule-deployment' || currentPath === '/manage-zones'));
  };

  // Enhanced navigation items with validation
  const navItems = [
    {
      icon: <LayoutDashboard size={20} />,
      label: "Dashboard",
      to: "/admindashboard",
      description: "Main admin dashboard"
    },
    {
      icon: <MapPinned size={20} />,
      label: "Map",
      to: "/enhanced-map",
      description: "View organizations, individuals and their drones"
    },
    {
      icon: <Building size={20} />,
      label: "Organizations",
      to: "/organizationpage",
      description: "Manage organizations"
    },
    {
      icon: <User size={20} />,
      label: "Individuals",
      to: "/individualpage",
      description: "Manage individual registrations"
    },
    {
      icon: <Boxes size={20} />,
      label: "Inventory",
      to: "/inventory",
      description: "Manage drone inventory"
    },
    {
      icon: <Truck size={20} />,
      label: "Deployment",
      to: "/admindeployment",
      description: "Manage drone deployments"
    },
    {
      icon: <Bot size={20} />,
      label: "Drones",
      to: "/dronepage",
      description: "Manage drone fleet"
    },
    {
      icon: <Bell size={20} />,
      label: "Notifications",
      to: "/adminnotification",
      description: "View system notifications"
    }
  ];

  // Mobile menu button that's always visible on mobile
  const MobileMenuButton = () => (
    <button
      className="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white shadow-md"
      onClick={toggleMobileMenu}
      aria-label="Toggle menu"
    >
      {isMobileMenuOpen ? (
        <X size={24} className="text-gray-700" />
      ) : (
        <Menu size={24} className="text-gray-700" />
      )}
    </button>
  );

  // Mobile sidebar overlay
  const MobileSidebar = () => (
    <div
      className={`lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300 ${
        isMobileMenuOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onClick={closeMobileMenu}
    >
      <div
        className={`w-[250px] h-full bg-white shadow-xl transition-transform duration-300 transform ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col h-full p-4 overflow-hidden">
          <div className="flex items-center justify-between mb-6 flex-shrink-0">
            <h1 className="text-xl font-bold text-blue-800 whitespace-nowrap">S.H.A.K.T.I</h1>
            <button onClick={closeMobileMenu} className="flex-shrink-0 ml-2">
              <X size={20} className="text-gray-500" />
            </button>
          </div>

          <nav className="flex-1 space-y-1 overflow-y-auto">
            {navItems.map((item, index) => (
              <NavItem
                key={index}
                icon={item.icon}
                label={item.label}
                to={item.to}
                isActive={isActive(item.to)}
                isMobile={true}
                onClick={closeMobileMenu}
              />
            ))}
          </nav>

          <div className="mt-auto pt-4 border-t border-gray-200 flex-shrink-0">
            <img src={logo} alt="Logo" className="w-24 mb-3 mx-auto" />
            <button
              className="w-full flex items-center justify-between font-medium px-4 py-2 rounded-lg bg-red-50 text-red-600 hover:bg-red-100 transition-colors text-sm"
              onClick={() => {
                closeMobileMenu();
                handleLogout();
              }}
            >
              Logout <LogOut size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Desktop sidebar
  const DesktopSidebar = () => (
    <div className="hidden lg:flex w-[250px] h-screen bg-white shadow-lg fixed top-0 left-0 z-50 flex-col justify-between py-6 px-4 overflow-hidden">
      <div className="flex-1 overflow-y-auto">
        <div className="mb-8 px-2">
          <h2 className="text-lg font-bold text-blue-800 text-[30px]">
            S.H.A.K.T.I
          </h2>
        </div>
        <nav className="space-y-1">
          {navItems.map((item, index) => (
            <NavItem
              key={index}
              icon={item.icon}
              label={item.label}
              to={item.to}
              isActive={isActive(item.to)}
            />
          ))}
        </nav>
      </div>
      <div className="px-2 mt-4 pt-4 border-t border-gray-200 flex-shrink-0">
        <img src={logo} alt="Logo" className="w-24 mb-3 mx-auto" />
        <button
          className="w-full flex items-center justify-between font-medium px-4 py-2 rounded-lg bg-red-50 text-red-600 hover:bg-red-100 transition-colors text-sm"
          onClick={handleLogout}
        >
          Logout <LogOut size={16} />
        </button>
      </div>
    </div>
  );

  return (
    <>
      <MobileMenuButton />
      <MobileSidebar />
      <DesktopSidebar />
    </>
  );
};

export default AdminSidebar;
