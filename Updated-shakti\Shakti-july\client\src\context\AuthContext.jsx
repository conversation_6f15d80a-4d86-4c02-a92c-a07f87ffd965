// context/AuthContext.jsx
import React, { createContext, useContext, useState, useEffect } from "react";

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  const getDashboardPath = (role) => {
    switch (role) {
      case "admin": return "/admindashboard";
      case "org": return "/orgdashboard";
      case "maintenance": return "/qc-dashboard";
      default: return "/";
    }
  };

  useEffect(() => {
    const initializeAuth = () => {
      const storedUser = localStorage.getItem("user");
      const storedToken = localStorage.getItem("authToken");

      if (storedUser && storedToken) {
        try {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          
          // Only redirect if on the login page (/)
          if (window.location.pathname === "/") {
            const dashboardPath = getDashboardPath(userData.role);
            window.location.href = dashboardPath; // Use direct navigation for initial login
          }
        } catch (error) {
          console.error("Error parsing stored user data:", error);
          localStorage.removeItem("user");
          localStorage.removeItem("authToken");
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = (userData) => {
    setUser(userData);
    localStorage.setItem("user", JSON.stringify(userData));
    // Initial login will redirect to dashboard
    const dashboardPath = getDashboardPath(userData.role);
    if (window.location.pathname === "/") {
      window.location.href = dashboardPath;
    }
  };

  const logout = () => {
    // Clear all auth-related data
    setUser(null);
    localStorage.removeItem("user");
    localStorage.removeItem("authToken");
    localStorage.removeItem("droneDraft");
    localStorage.removeItem("lastPath");
    // Use history API for navigation
    window.history.replaceState({}, '', "/");
    window.location.reload();
  };

  const isAuthenticated = () => {
    return !!(user && localStorage.getItem("authToken"));
  };

  const hasRole = (role) => {
    return user?.role === role;
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      loading,
      isAuthenticated,
      hasRole
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// ✅ Hook to access auth
export const useAuth = () => {
  return useContext(AuthContext);
};


