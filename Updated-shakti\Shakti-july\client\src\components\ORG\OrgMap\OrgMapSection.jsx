import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import "./OrgMapStyles.css";
import {
  Bell,
  Search,
  MapPin,
  Activity,
  Battery,
  Thermometer,
  Gauge,
  RefreshCw,
  Eye,
  Layers,
  Maximize2,
  BarChart3,
  Clock,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  ArrowLeft,
  Calendar,
  MapIcon,
  Zap
} from 'lucide-react';
import L from "leaflet";
import OrgSidebar from "../common/OrgSidebar";


// Enhanced drone data with more realistic information
const generateDroneData = () => {
  const baseLocations = [
    { lat: 28.6139, lng: 77.209, area: "Delhi NCR" },
    { lat: 19.076, lng: 72.8777, area: "Mumbai" },
    { lat: 13.0827, lng: 80.2707, area: "Chennai" },
    { lat: 22.5726, lng: 88.3639, area: "Kolkata" },
    { lat: 12.9716, lng: 77.5946, area: "Bangalore" },
    { lat: 17.385, lng: 78.4867, area: "Hyderabad" },
    { lat: 23.0225, lng: 72.5714, area: "Ahmedabad" },
    { lat: 18.5204, lng: 73.8567, area: "Pune" }
  ];

  return baseLocations.map((location, index) => ({
    id: `PRYMAA${95170 + index}`,
    name: `Drone ${['Alpha', 'Beta', 'Gamma', 'Delta', 'Epsilon', 'Zeta', 'Eta', 'Theta'][index]}`,
    status: ['ACTIVE', 'FLYING', 'INACTIVE', 'MAINTENANCE'][Math.floor(Math.random() * 4)],
    lat: location.lat + (Math.random() - 0.5) * 0.01,
    lng: location.lng + (Math.random() - 0.5) * 0.01,
    area: location.area,
    battery: Math.floor(Math.random() * 100),
    temperature: Math.floor(Math.random() * 30) + 20,
    altitude: Math.floor(Math.random() * 100) + 50,
    speed: Math.floor(Math.random() * 25),
    lastUpdate: new Date(Date.now() - Math.random() * 3600000),
    missionProgress: Math.floor(Math.random() * 100),
    // Video data for spraying operations
    sprayingVideo: {
      url: `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`, // Sample video URL
      thumbnail: `https://via.placeholder.com/320x180/4F46E5/FFFFFF?text=Drone+${index + 1}+Mission`,
      duration: Math.floor(Math.random() * 300) + 120, // 2-7 minutes
      recordedDate: new Date(Date.now() - Math.random() * 86400000 * 7), // Within last week
      sprayingArea: Math.floor(Math.random() * 50) + 10, // 10-60 hectares
      chemicalUsed: ['Pesticide A', 'Fertilizer B', 'Herbicide C'][Math.floor(Math.random() * 3)],
      weatherConditions: ['Clear', 'Partly Cloudy', 'Overcast'][Math.floor(Math.random() * 3)]
    },
    fuelLevel: Math.floor(Math.random() * 100),
    signalStrength: Math.floor(Math.random() * 5) + 1
  }));
};

// Create dynamic drone icons based on status and battery level
const getDroneIcon = (status, battery = 100) => {
  let color = '#10B981'; // Green for active
  let bgColor = '#ECFDF5';

  if (status === 'FLYING') {
    color = '#3B82F6'; // Blue for flying
    bgColor = '#EFF6FF';
  } else if (status === 'INACTIVE') {
    color = '#EF4444'; // Red for inactive
    bgColor = '#FEF2F2';
  } else if (status === 'MAINTENANCE') {
    color = '#F59E0B'; // Orange for maintenance
    bgColor = '#FFFBEB';
  }

  // Add battery indicator
  const batteryColor = battery > 50 ? '#10B981' : battery > 20 ? '#F59E0B' : '#EF4444';

  const svgIcon = `
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="14" fill="${bgColor}" stroke="${color}" stroke-width="2"/>
      <path d="M12 10h8v4l2 2v4l-2 2v4h-8v-4l-2-2v-4l2-2v-4z" fill="${color}"/>
      <circle cx="24" cy="8" r="3" fill="${batteryColor}" stroke="white" stroke-width="1"/>
      <text x="24" y="10" text-anchor="middle" font-size="6" fill="white">${Math.round(battery/10)}</text>
    </svg>
  `;

  return new L.DivIcon({
    html: svgIcon,
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
    className: 'custom-drone-icon'
  });
};

const OrgMapSection = () => {
  const navigate = useNavigate();
  const [drones, setDrones] = useState(generateDroneData());
  const [filteredDrones, setFilteredDrones] = useState(drones);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [mapView, setMapView] = useState('satellite');
  const [showCoverage, setShowCoverage] = useState(false);
  const [selectedDrone, setSelectedDrone] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Analytics and Video States
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [selectedDroneForVideo, setSelectedDroneForVideo] = useState(null);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoMuted, setVideoMuted] = useState(false);
  const [videoCurrentTime, setVideoCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);

  // Real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setDrones(prevDrones =>
        prevDrones.map(drone => ({
          ...drone,
          battery: Math.max(0, Math.min(100, drone.battery + (Math.random() - 0.5) * 5)),
          temperature: Math.max(15, Math.min(50, drone.temperature + (Math.random() - 0.5) * 2)),
          speed: Math.max(0, Math.min(30, drone.speed + (Math.random() - 0.5) * 3)),
          lastUpdate: new Date(),
          lat: drone.lat + (Math.random() - 0.5) * 0.001,
          lng: drone.lng + (Math.random() - 0.5) * 0.001
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Filter drones based on search and status
  useEffect(() => {
    let filtered = drones;

    if (searchTerm) {
      filtered = filtered.filter(drone =>
        drone.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        drone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        drone.area.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(drone => drone.status === statusFilter);
    }

    setFilteredDrones(filtered);
  }, [drones, searchTerm, statusFilter]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    setDrones(generateDroneData());
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  // Video control functions
  const handleDroneVideoClick = (drone) => {
    setSelectedDroneForVideo(drone);
    setShowAnalytics(true);
    setIsVideoPlaying(false);
    setVideoCurrentTime(0);
  };

  const toggleVideoPlay = () => {
    setIsVideoPlaying(!isVideoPlaying);
  };

  const toggleVideoMute = () => {
    setVideoMuted(!videoMuted);
  };

  const handleVideoTimeUpdate = (currentTime) => {
    setVideoCurrentTime(currentTime);
  };

  const handleVideoLoadedMetadata = (duration) => {
    setVideoDuration(duration);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const closeAnalytics = () => {
    setShowAnalytics(false);
    setSelectedDroneForVideo(null);
    setIsVideoPlaying(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-100';
      case 'FLYING': return 'text-blue-600 bg-blue-100';
      case 'INACTIVE': return 'text-red-600 bg-red-100';
      case 'MAINTENANCE': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const tileLayerUrls = {
    satellite: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
    street: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    terrain: "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
  };

  return (
    <div className="flex h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 text-black">
      {/* Sidebar */}
      <OrgSidebar />

      {/* Main Layout */}
      <div className="ml-[18em] flex flex-col flex-1 relative">
        {/* Professional Header */}
        <div className="bg-white shadow-lg border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">Live Drone Tracking</h2>
                  <p className="text-sm text-gray-600">{filteredDrones.length} drones monitored</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search drones..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="ALL">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="FLYING">Flying</option>
                <option value="INACTIVE">Inactive</option>
                <option value="MAINTENANCE">Maintenance</option>
              </select>

              {/* Map View Toggle */}
              <select
                value={mapView}
                onChange={(e) => setMapView(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="satellite">Satellite</option>
                <option value="street">Street</option>
                <option value="terrain">Terrain</option>
              </select>

              {/* Coverage Toggle */}
              <button
                onClick={() => setShowCoverage(!showCoverage)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showCoverage
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Eye className="w-4 h-4" />
              </button>

              {/* Refresh */}
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>

              {/* Notifications */}
              <div className="relative">
                <button className="p-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                  <Bell className="w-5 h-5 text-gray-700" />
                </button>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden">
          {/* Map Section */}
          <div className="flex-1 relative">
            {/* Floating Controls */}
            <div className="absolute top-4 left-4 z-[1000] space-y-2">
              <div className="bg-white rounded-lg shadow-lg p-3">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Activity className="w-4 h-4 text-green-500" />
                  <span>Live Tracking Active</span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-3">
                <div className="text-xs text-gray-500 mb-1">Quick Stats</div>
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>Active:</span>
                    <span className="font-medium text-green-600">
                      {filteredDrones.filter(d => d.status === 'ACTIVE').length}
                    </span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Flying:</span>
                    <span className="font-medium text-blue-600">
                      {filteredDrones.filter(d => d.status === 'FLYING').length}
                    </span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Inactive:</span>
                    <span className="font-medium text-red-600">
                      {filteredDrones.filter(d => d.status === 'INACTIVE').length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Map Controls */}
            <div className="absolute top-4 right-4 z-[1000] space-y-2">
              <button
                onClick={() => setMapView(mapView === 'satellite' ? 'street' : 'satellite')}
                className="bg-white p-2 rounded-lg shadow-lg hover:bg-gray-50 transition-colors"
                title="Toggle Map View"
              >
                <Layers className="w-4 h-4 text-gray-700" />
              </button>

              <button
                className="bg-white p-2 rounded-lg shadow-lg hover:bg-gray-50 transition-colors"
                title="Fullscreen"
              >
                <Maximize2 className="w-4 h-4 text-gray-700" />
              </button>
            </div>

            {/* Enhanced Map */}
            <MapContainer
              center={[20.5937, 78.9629]}
              zoom={6}
              scrollWheelZoom={true}
              style={{ width: "100%", height: "100%" }}
              className="rounded-lg"
            >
              <TileLayer
                url={tileLayerUrls[mapView]}
                attribution={
                  mapView === 'satellite'
                    ? 'Tiles &copy; <a href="https://www.esri.com/">Esri</a>'
                    : mapView === 'terrain'
                    ? '&copy; <a href="https://opentopomap.org">OpenTopoMap</a>'
                    : '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                }
              />

              {filteredDrones.map((drone) => (
                <Marker
                  key={drone.id}
                  position={[drone.lat, drone.lng]}
                  icon={getDroneIcon(drone.status, drone.battery)}
                  eventHandlers={{
                    click: () => setSelectedDrone(drone)
                  }}
                >
                  <Popup className="custom-popup">
                    <div className="p-2 min-w-[250px]">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-gray-800">{drone.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(drone.status)}`}>
                          {drone.status}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Battery className="w-4 h-4 text-green-500" />
                          <span>{drone.battery}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Thermometer className="w-4 h-4 text-orange-500" />
                          <span>{drone.temperature}°C</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Gauge className="w-4 h-4 text-blue-500" />
                          <span>{drone.speed} km/h</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-purple-500" />
                          <span>{drone.area}</span>
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>ID: {drone.id}</span>
                          <span>Updated: {drone.lastUpdate.toLocaleTimeString()}</span>
                        </div>
                      </div>

                      <button
                        onClick={() => navigate(`/viewlogs?drone=${drone.id}`)}
                        className="w-full mt-3 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
                      >
                        View Details
                      </button>
                    </div>
                  </Popup>
                </Marker>
              ))}

              {/* Coverage circles for active drones */}
              {showCoverage && filteredDrones
                .filter(drone => drone.status === 'ACTIVE' || drone.status === 'FLYING')
                .map(drone => (
                  <Circle
                    key={`coverage-${drone.id}`}
                    center={[drone.lat, drone.lng]}
                    radius={5000}
                    pathOptions={{
                      color: drone.status === 'ACTIVE' ? '#10B981' : '#3B82F6',
                      fillColor: drone.status === 'ACTIVE' ? '#10B981' : '#3B82F6',
                      fillOpacity: 0.1,
                      weight: 2
                    }}
                  />
                ))
              }
            </MapContainer>
          </div>

          {/* Enhanced Drone List Sidebar */}
          <div className="w-[24rem] bg-white shadow-xl border-l border-gray-200 flex flex-col">
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-bold text-gray-800">
                  {showAnalytics ? 'Drone Analytics' : 'Drone Fleet'}
                </h2>
                <div className="flex items-center gap-2">
                  {showAnalytics && selectedDroneForVideo && (
                    <button
                      onClick={closeAnalytics}
                      className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 text-gray-600" />
                    </button>
                  )}
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-600">Live</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                {showAnalytics && selectedDroneForVideo
                  ? `${selectedDroneForVideo.name} - Mission Video`
                  : `${filteredDrones.length} of ${drones.length} drones`
                }
              </p>
            </div>

            {/* Conditional Content: Drone List or Analytics/Video */}
            <div className="flex-1 overflow-y-auto">
              {showAnalytics && selectedDroneForVideo ? (
                /* Video Analytics View */
                <div className="p-4 space-y-4">
                  {/* Video Player */}
                  <div className="bg-black rounded-lg overflow-hidden">
                    <div className="relative aspect-video">
                      <video
                        className="w-full h-full object-cover"
                        poster={selectedDroneForVideo.sprayingVideo.thumbnail}
                        controls={false}
                        muted={videoMuted}
                        onTimeUpdate={(e) => handleVideoTimeUpdate(e.target.currentTime)}
                        onLoadedMetadata={(e) => handleVideoLoadedMetadata(e.target.duration)}
                      >
                        <source src={selectedDroneForVideo.sprayingVideo.url} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>

                      {/* Video Controls Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                        <button
                          onClick={toggleVideoPlay}
                          className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200"
                        >
                          {isVideoPlaying ? (
                            <Pause className="w-8 h-8 text-gray-800" />
                          ) : (
                            <Play className="w-8 h-8 text-gray-800 ml-1" />
                          )}
                        </button>
                      </div>

                      {/* Video Progress Bar */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                        <div className="flex items-center gap-3 text-white text-sm">
                          <span>{formatTime(videoCurrentTime)}</span>
                          <div className="flex-1 bg-gray-600 rounded-full h-1">
                            <div
                              className="bg-blue-500 h-1 rounded-full transition-all duration-200"
                              style={{ width: `${videoDuration > 0 ? (videoCurrentTime / videoDuration) * 100 : 0}%` }}
                            ></div>
                          </div>
                          <span>{formatTime(videoDuration)}</span>
                          <button
                            onClick={toggleVideoMute}
                            className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                          >
                            {videoMuted ? (
                              <VolumeX className="w-4 h-4" />
                            ) : (
                              <Volume2 className="w-4 h-4" />
                            )}
                          </button>
                          <button className="p-1 hover:bg-white hover:bg-opacity-20 rounded">
                            <Maximize className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mission Details */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                      <Zap className="w-4 h-4 text-blue-600" />
                      Mission Details
                    </h3>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Date:</span>
                        <span className="font-medium">{selectedDroneForVideo.sprayingVideo.recordedDate.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Area:</span>
                        <span className="font-medium">{selectedDroneForVideo.sprayingVideo.sprayingArea} hectares</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Activity className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Chemical:</span>
                        <span className="font-medium">{selectedDroneForVideo.sprayingVideo.chemicalUsed}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Eye className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Weather:</span>
                        <span className="font-medium">{selectedDroneForVideo.sprayingVideo.weatherConditions}</span>
                      </div>
                    </div>
                  </div>

                  {/* Mission Timeline */}
                  <div className="bg-white border rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-3">Mission Timeline</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">00:00 - Mission Start</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">02:15 - Spraying Begin</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">15:30 - Area Coverage 50%</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">28:45 - Spraying Complete</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">32:10 - Return to Base</span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Regular Drone List View */
                <div className="p-4 space-y-3">
                  {filteredDrones.map((drone) => (
                <div
                  key={drone.id}
                  className={`bg-white border rounded-xl p-4 cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-blue-300 ${
                    selectedDrone?.id === drone.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => {
                    setSelectedDrone(drone);
                    navigate(`/viewlogs?drone=${drone.id}`);
                  }}
                >
                  {/* Drone Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <Activity className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">{drone.name}</h3>
                        <p className="text-xs text-gray-500">{drone.id}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(drone.status)}`}>
                      {drone.status}
                    </span>
                  </div>

                  {/* Drone Metrics */}
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="flex items-center gap-2">
                      <Battery className={`w-4 h-4 ${drone.battery > 50 ? 'text-green-500' : drone.battery > 20 ? 'text-yellow-500' : 'text-red-500'}`} />
                      <span className="text-sm font-medium">{drone.battery}%</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Thermometer className="w-4 h-4 text-orange-500" />
                      <span className="text-sm">{drone.temperature}°C</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Gauge className="w-4 h-4 text-blue-500" />
                      <span className="text-sm">{drone.speed} km/h</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-purple-500" />
                      <span className="text-sm">{drone.area}</span>
                    </div>
                  </div>

                  {/* Progress Bars */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Mission Progress</span>
                      <span className="font-medium">{drone.missionProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${drone.missionProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        <span>Updated {drone.lastUpdate.toLocaleTimeString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <div className={`w-2 h-2 rounded-full ${
                            drone.signalStrength >= 4 ? 'bg-green-500' :
                            drone.signalStrength >= 2 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                          <span>Signal</span>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDroneVideoClick(drone);
                          }}
                          className="px-2 py-1 bg-blue-500 text-white text-xs rounded-md hover:bg-blue-600 transition-colors flex items-center gap-1"
                        >
                          <Play className="w-3 h-3" />
                          Video
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

                  {filteredDrones.length === 0 && (
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Search className="w-6 h-6 text-gray-400" />
                      </div>
                      <p className="text-gray-500">No drones found</p>
                      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">
                  {showAnalytics ? 'Video Analytics' : 'Fleet Overview'}
                </span>
                {!showAnalytics && (
                  <button
                    onClick={() => setShowAnalytics(true)}
                    className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1"
                  >
                    <BarChart3 className="w-4 h-4" />
                    Analytics
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrgMapSection;
