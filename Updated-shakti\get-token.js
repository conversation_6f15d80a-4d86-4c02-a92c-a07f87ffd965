const axios = require('axios');

async function getJWTToken() {
  try {
    console.log('🔑 Getting JWT Token...');
    
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'Admin123!'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ Login successful!');
      console.log('\n🎯 YOUR JWT TOKEN:');
      console.log('==========================================');
      console.log(response.data.data.token);
      console.log('==========================================');
      console.log('\n📋 User Info:');
      console.log('Username:', response.data.data.user.username);
      console.log('Role:', response.data.data.user.role);
      console.log('ID:', response.data.data.user.id);
      
      console.log('\n📝 For Postman:');
      console.log('Authorization: Bearer ' + response.data.data.token);
      
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Error getting token:', error.response?.data || error.message);
  }
}

getJWTToken();
