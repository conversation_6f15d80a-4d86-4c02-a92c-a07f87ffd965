const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Drone = require('../models/Drone');
const Organization = require('../models/Organization');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const addSampleDrones = async () => {
  try {
    console.log('🔍 Adding sample drones to database...');

    // Get an existing organization to assign drones to
    const organization = await Organization.findOne({ status: 'active' });
    if (!organization) {
      console.log('❌ No active organization found. Please create an organization first.');
      return;
    }

    console.log(`✅ Found organization: ${organization.name}`);

    // Sample drone data
    const sampleDrones = [
      {
        name: 'Drone Alpha-001',
        model: 'DJI Phantom 4 Pro',
        manufacturer: 'DJI',
        serialNumber: 'DJI001-2024-001',
        registrationNumber: 'REG-001-2024',
        organizationId: organization._id,
        specifications: {
          type: 'quadcopter',
          weight: 1.4,
          maxPayload: 0.5,
          maxFlightTime: 30,
          maxRange: 7,
          maxAltitude: 120,
          maxSpeed: 72,
          batteryCapacity: 5870,
          cameraResolution: '4K',
          hasGimbal: true,
          hasGPS: true,
          hasObstacleAvoidance: true
        },
        status: 'active',
        condition: 'excellent',
        currentLocation: {
          latitude: 28.6139,
          longitude: 77.2090,
          altitude: 50,
          lastUpdated: new Date()
        },
        purchase: {
          purchaseDate: new Date('2024-01-15'),
          purchasePrice: 1500,
          vendor: 'DJI Store',
          warrantyExpiryDate: new Date('2026-01-15')
        },
        flightStats: {
          totalFlightTime: 45.5,
          totalFlights: 12,
          totalDistance: 125.3,
          averageFlightTime: 3.8
        },
        maintenance: {
          lastMaintenanceDate: new Date('2024-06-01'),
          nextMaintenanceDate: new Date('2024-12-01'),
          maintenanceIntervalHours: 100
        },
        createdBy: organization.createdBy
      },
      {
        name: 'Drone Beta-002',
        model: 'DJI Mavic Air 2',
        manufacturer: 'DJI',
        serialNumber: 'DJI002-2024-002',
        registrationNumber: 'REG-002-2024',
        organizationId: organization._id,
        specifications: {
          type: 'quadcopter',
          weight: 0.57,
          maxPayload: 0.3,
          maxFlightTime: 34,
          maxRange: 10,
          maxAltitude: 120,
          maxSpeed: 68,
          batteryCapacity: 3500,
          cameraResolution: '4K',
          hasGimbal: true,
          hasGPS: true,
          hasObstacleAvoidance: true
        },
        status: 'maintenance',
        condition: 'good',
        currentLocation: {
          latitude: 28.7041,
          longitude: 77.1025,
          altitude: 45,
          lastUpdated: new Date()
        },
        purchase: {
          purchaseDate: new Date('2024-02-20'),
          purchasePrice: 800,
          vendor: 'Tech Store',
          warrantyExpiryDate: new Date('2026-02-20')
        },
        flightStats: {
          totalFlightTime: 28.2,
          totalFlights: 8,
          totalDistance: 89.7,
          averageFlightTime: 3.5
        },
        maintenance: {
          lastMaintenanceDate: new Date('2024-07-15'),
          nextMaintenanceDate: new Date('2024-08-15'),
          maintenanceIntervalHours: 50
        },
        createdBy: organization.createdBy
      },
      {
        name: 'Drone Gamma-003',
        model: 'Autel EVO II',
        manufacturer: 'Autel',
        serialNumber: 'AUT003-2024-003',
        registrationNumber: 'REG-003-2024',
        organizationId: organization._id,
        specifications: {
          type: 'quadcopter',
          weight: 1.1,
          maxPayload: 0.4,
          maxFlightTime: 40,
          maxRange: 9,
          maxAltitude: 120,
          maxSpeed: 61,
          batteryCapacity: 7100,
          cameraResolution: '6K',
          hasGimbal: true,
          hasGPS: true,
          hasObstacleAvoidance: true
        },
        status: 'active',
        condition: 'excellent',
        currentLocation: {
          latitude: 28.5355,
          longitude: 77.3910,
          altitude: 55,
          lastUpdated: new Date()
        },
        purchase: {
          purchaseDate: new Date('2024-03-10'),
          purchasePrice: 1200,
          vendor: 'Autel Dealer',
          warrantyExpiryDate: new Date('2026-03-10')
        },
        flightStats: {
          totalFlightTime: 62.8,
          totalFlights: 15,
          totalDistance: 178.4,
          averageFlightTime: 4.2
        },
        maintenance: {
          lastMaintenanceDate: new Date('2024-05-20'),
          nextMaintenanceDate: new Date('2024-11-20'),
          maintenanceIntervalHours: 120
        },
        createdBy: organization.createdBy
      },
      {
        name: 'Drone Delta-004',
        model: 'Parrot Anafi',
        manufacturer: 'Parrot',
        serialNumber: 'PAR004-2024-004',
        registrationNumber: 'REG-004-2024',
        organizationId: organization._id,
        specifications: {
          type: 'quadcopter',
          weight: 0.32,
          maxPayload: 0.2,
          maxFlightTime: 25,
          maxRange: 4,
          maxAltitude: 120,
          maxSpeed: 55,
          batteryCapacity: 2700,
          cameraResolution: '4K',
          hasGimbal: true,
          hasGPS: true,
          hasObstacleAvoidance: false
        },
        status: 'inactive',
        condition: 'fair',
        currentLocation: {
          latitude: 28.4595,
          longitude: 77.0266,
          altitude: 40,
          lastUpdated: new Date()
        },
        purchase: {
          purchaseDate: new Date('2024-04-05'),
          purchasePrice: 600,
          vendor: 'Parrot Store',
          warrantyExpiryDate: new Date('2026-04-05')
        },
        flightStats: {
          totalFlightTime: 18.5,
          totalFlights: 6,
          totalDistance: 45.2,
          averageFlightTime: 3.1
        },
        maintenance: {
          lastMaintenanceDate: new Date('2024-06-10'),
          nextMaintenanceDate: new Date('2024-12-10'),
          maintenanceIntervalHours: 80
        },
        createdBy: organization.createdBy
      },
      {
        name: 'Drone Echo-005',
        model: 'DJI Mini 3 Pro',
        manufacturer: 'DJI',
        serialNumber: 'DJI005-2024-005',
        registrationNumber: 'REG-005-2024',
        organizationId: organization._id,
        specifications: {
          type: 'quadcopter',
          weight: 0.25,
          maxPayload: 0.1,
          maxFlightTime: 34,
          maxRange: 12,
          maxAltitude: 120,
          maxSpeed: 57,
          batteryCapacity: 2453,
          cameraResolution: '4K',
          hasGimbal: true,
          hasGPS: true,
          hasObstacleAvoidance: true
        },
        status: 'active',
        condition: 'excellent',
        currentLocation: {
          latitude: 28.6692,
          longitude: 77.4538,
          altitude: 60,
          lastUpdated: new Date()
        },
        purchase: {
          purchaseDate: new Date('2024-05-12'),
          purchasePrice: 750,
          vendor: 'DJI Store',
          warrantyExpiryDate: new Date('2026-05-12')
        },
        flightStats: {
          totalFlightTime: 35.7,
          totalFlights: 10,
          totalDistance: 98.6,
          averageFlightTime: 3.6
        },
        maintenance: {
          lastMaintenanceDate: new Date('2024-07-01'),
          nextMaintenanceDate: new Date('2025-01-01'),
          maintenanceIntervalHours: 100
        },
        createdBy: organization.createdBy
      }
    ];

    // Insert sample drones
    const insertedDrones = await Drone.insertMany(sampleDrones);
    console.log(`✅ Successfully added ${insertedDrones.length} sample drones to the database`);

    // Display summary
    console.log('\n📊 Drone Summary:');
    console.log(`- Active: ${insertedDrones.filter(d => d.status === 'active').length}`);
    console.log(`- Maintenance: ${insertedDrones.filter(d => d.status === 'maintenance').length}`);
    console.log(`- Inactive: ${insertedDrones.filter(d => d.status === 'inactive').length}`);

    mongoose.connection.close();
    console.log('✅ Database connection closed');

  } catch (error) {
    console.error('❌ Error adding sample drones:', error);
    mongoose.connection.close();
  }
};

// Run the script
addSampleDrones();
