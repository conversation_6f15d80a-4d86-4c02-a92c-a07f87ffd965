const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const organizationRoutes = require('./organizations');
const droneRoutes = require('./drones');
const notificationRoutes = require('./notifications');
const organizationPortalRoutes = require('./organizationPortal');
const NotificationService = require('../utils/notificationService');
// const individualRoutes = require('./individuals');

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'SHAKTI Drone Management API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/organizations', organizationRoutes);
router.use('/drones', droneRoutes);
router.use('/notifications', notificationRoutes);
router.use('/org-portal', organizationPortalRoutes);
// Real individual endpoints with MongoDB
const Individual = require('../models/Individual');

// In-memory storage for testing (when MongoDB is not available)
let inMemoryIndividuals = [];
let inMemoryOrganizations = [
  {
    _id: '507f1f77bcf86cd799439011',
    name: 'Salam Kisan',
    displayName: 'Salam Kisan Agricultural Services',
    type: 'private',
    contact: { primaryEmail: '<EMAIL>', phone: '+919876543210' },
    status: 'active',
    isVerified: true,
    createdAt: new Date('2023-01-15')
  },
  {
    _id: '507f1f77bcf86cd799439012',
    name: 'Test Organization',
    displayName: 'Test Organization Ltd.',
    type: 'government',
    contact: { primaryEmail: '<EMAIL>', phone: '+919876543211' },
    status: 'active',
    isVerified: true,
    createdAt: new Date('2023-02-20')
  }
];

// In-memory users for authentication
const bcrypt = require('bcryptjs');
let inMemoryUsers = [
  {
    _id: '507f1f77bcf86cd799439001',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // Admin123!
    role: 'admin',
    profile: { adminLevel: 'super' }
  },
  {
    _id: '507f1f77bcf86cd799439002',
    username: 'salamkisan',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Org123!
    role: 'org',
    profile: {
      organizationId: '507f1f77bcf86cd799439011',
      organizationName: 'Salam Kisan'
    }
  },
  {
    _id: '507f1f77bcf86cd799439003',
    username: 'testorg',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Test123!
    role: 'org',
    profile: {
      organizationId: '507f1f77bcf86cd799439012',
      organizationName: 'Test Organization'
    }
  }
];

// Check if MongoDB is connected
const isMongoConnected = () => {
  return mongoose.connection.readyState === 1;
};

router.post('/individuals', async (req, res) => {
  try {
    console.log('Individual creation request:', req.body);

    const mongoConnected = isMongoConnected();
    console.log(`🔍 MongoDB connection check: ${mongoConnected} (readyState: ${mongoose.connection.readyState})`);

    if (mongoConnected) {
      // Use MongoDB if connected - let MongoDB generate the ObjectId
      const individualData = {
        ...req.body,
        gender: req.body.gender?.toLowerCase(),
        createdBy: req.user?._id || '507f1f77bcf86cd799439011',
        lastModifiedBy: req.user?._id || '507f1f77bcf86cd799439011'
      };

      console.log('Normalized individual data:', individualData);

      const individual = new Individual(individualData);
      await individual.save();
      console.log('✅ Individual saved to MongoDB:', individual._id);

      // Create notification for individual creation
      try {
        await NotificationService.createIndividualNotification('created', individual, req.user);
      } catch (notificationError) {
        console.error('❌ Failed to create notification for individual creation:', notificationError);
        // Don't fail the main operation if notification fails
      }

      res.json({
        success: true,
        message: 'Individual created successfully',
        data: { individual },
        timestamp: new Date().toISOString()
      });
    } else {
      // Use in-memory storage if MongoDB not connected
      const individualData = {
        ...req.body,
        _id: new Date().getTime().toString(), // Simple ID for in-memory storage
        gender: req.body.gender?.toLowerCase(),
        createdBy: req.user?._id || '507f1f77bcf86cd799439011',
        lastModifiedBy: req.user?._id || '507f1f77bcf86cd799439011',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      console.log('Normalized individual data:', individualData);
      inMemoryIndividuals.push(individualData);
      console.log('✅ Individual saved to memory:', individualData._id);

      res.json({
        success: true,
        message: 'Individual created successfully (stored in memory)',
        data: { individual: individualData },
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ Error creating individual:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create individual',
      timestamp: new Date().toISOString()
    });
  }
});

router.get('/individuals', async (req, res) => {
  try {
    let individuals = [];
    const mongoState = mongoose.connection.readyState;
    console.log(`🔍 MongoDB connection state: ${mongoState} (0=disconnected, 1=connected, 2=connecting, 3=disconnecting)`);

    if (isMongoConnected()) {
      // Use MongoDB if connected
      console.log('📊 Using MongoDB for individuals...');
      individuals = await Individual.find()
        .populate('createdBy', 'username')
        .sort({ createdAt: -1 })
        .limit(50);
      console.log(`✅ Retrieved ${individuals.length} individuals from MongoDB`);
    } else {
      // Use in-memory storage
      console.log('📊 Using in-memory storage for individuals...');
      individuals = inMemoryIndividuals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      console.log(`✅ Retrieved ${individuals.length} individuals from memory`);
    }

    res.json({
      success: true,
      message: 'Individuals retrieved successfully',
      data: {
        individuals,
        pagination: {
          totalCount: individuals.length,
          currentPage: 1,
          totalPages: 1,
          limit: 50
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error retrieving individuals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve individuals',
      timestamp: new Date().toISOString()
    });
  }
});

router.get('/individuals/stats/overview', async (req, res) => {
  try {
    let result;

    if (isMongoConnected()) {
      // Use MongoDB if connected
      const stats = await Individual.aggregate([
        {
          $group: {
            _id: null,
            totalIndividuals: { $sum: 1 },
            pendingIndividuals: {
              $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
            },
            approvedIndividuals: {
              $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
            },
            rejectedIndividuals: {
              $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
            },
            suspendedIndividuals: {
              $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
            },
            verifiedIndividuals: {
              $sum: { $cond: ['$isVerified', 1, 0] }
            },
            totalAllocatedDrones: { $sum: { $ifNull: ['$allocatedDrones', 0] } }
          }
        }
      ]);

      result = stats[0] || {
        totalIndividuals: 0,
        pendingIndividuals: 0,
        approvedIndividuals: 0,
        rejectedIndividuals: 0,
        suspendedIndividuals: 0,
        verifiedIndividuals: 0,
        totalAllocatedDrones: 0
      };
      console.log('✅ Individual stats from MongoDB:', result);
    } else {
      // Use in-memory storage
      result = {
        totalIndividuals: inMemoryIndividuals.length,
        pendingIndividuals: inMemoryIndividuals.filter(i => i.status === 'pending').length,
        approvedIndividuals: inMemoryIndividuals.filter(i => i.status === 'approved').length,
        rejectedIndividuals: inMemoryIndividuals.filter(i => i.status === 'rejected').length,
        suspendedIndividuals: inMemoryIndividuals.filter(i => i.status === 'suspended').length,
        verifiedIndividuals: inMemoryIndividuals.filter(i => i.isVerified).length,
        totalAllocatedDrones: inMemoryIndividuals.reduce((sum, i) => sum + (i.allocatedDrones || 0), 0)
      };
      console.log('✅ Individual stats from memory:', result);
    }

    res.json({
      success: true,
      message: 'Individual statistics retrieved successfully',
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error retrieving individual stats:', error);

    // Fallback to in-memory stats
    const fallbackResult = {
      totalIndividuals: inMemoryIndividuals.length,
      pendingIndividuals: inMemoryIndividuals.filter(i => i.status === 'pending').length,
      approvedIndividuals: inMemoryIndividuals.filter(i => i.status === 'approved').length,
      rejectedIndividuals: inMemoryIndividuals.filter(i => i.status === 'rejected').length,
      suspendedIndividuals: inMemoryIndividuals.filter(i => i.status === 'suspended').length,
      verifiedIndividuals: inMemoryIndividuals.filter(i => i.isVerified).length,
      totalAllocatedDrones: inMemoryIndividuals.reduce((sum, i) => sum + (i.allocatedDrones || 0), 0)
    };

    res.json({
      success: true,
      message: 'Individual statistics retrieved successfully (fallback mode)',
      data: fallbackResult,
      timestamp: new Date().toISOString()
    });
  }
});

// Additional individual endpoints for UI functionality
router.patch('/individuals/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const updateData = {
      status,
      lastModifiedBy: req.user?._id || '507f1f77bcf86cd799439011'
    };

    // If approving, set verification details
    if (status === 'approved') {
      updateData.isVerified = true;
      updateData.verificationDate = new Date();
      updateData.verifiedBy = req.user?._id || '507f1f77bcf86cd799439011';
    }

    if (isMongoConnected()) {
      // Use MongoDB if connected
      console.log(`🔍 MongoDB connection state: ${mongoose.connection.readyState} (0=disconnected, 1=connected, 2=connecting, 3=disconnecting)`);

      // Check if ID is a valid MongoDB ObjectId
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.log(`❌ Invalid ObjectId format: ${id}`);
        return res.status(400).json({
          success: false,
          message: 'Invalid individual ID format',
          timestamp: new Date().toISOString()
        });
      }

      const individual = await Individual.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('createdBy', 'username')
       .populate('verifiedBy', 'username');

      if (!individual) {
        return res.status(404).json({
          success: false,
          message: 'Individual not found',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`✅ Individual status updated in MongoDB: ${id} -> ${status}`);

      res.json({
        success: true,
        message: 'Individual status updated successfully',
        data: { individual },
        timestamp: new Date().toISOString()
      });
    } else {
      // Use in-memory storage if MongoDB not connected
      console.log('📊 Using in-memory storage for individual status update...');

      const individualIndex = inMemoryIndividuals.findIndex(ind => ind._id === id);
      if (individualIndex === -1) {
        return res.status(404).json({
          success: false,
          message: 'Individual not found',
          timestamp: new Date().toISOString()
        });
      }

      // Update the individual in memory
      inMemoryIndividuals[individualIndex] = {
        ...inMemoryIndividuals[individualIndex],
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      console.log(`✅ Individual status updated in memory: ${id} -> ${status}`);

      res.json({
        success: true,
        message: 'Individual status updated successfully (in memory)',
        data: { individual: inMemoryIndividuals[individualIndex] },
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ Error updating individual status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update individual status',
      timestamp: new Date().toISOString()
    });
  }
});

router.delete('/individuals/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const individual = await Individual.findById(id);
    if (!individual) {
      return res.status(404).json({
        success: false,
        message: 'Individual not found',
        timestamp: new Date().toISOString()
      });
    }

    await Individual.findByIdAndDelete(id);

    // Create notification for individual deletion
    try {
      await NotificationService.createIndividualNotification('deleted', individual, req.user);
    } catch (notificationError) {
      console.error('❌ Failed to create notification for individual deletion:', notificationError);
      // Don't fail the main operation if notification fails
    }

    console.log(`✅ Individual deleted: ${id}`);

    res.json({
      success: true,
      message: 'Individual deleted successfully',
      data: { individualId: id },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error deleting individual:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete individual',
      timestamp: new Date().toISOString()
    });
  }
});

// Organization endpoints for org login
router.get('/organizations', async (req, res) => {
  try {
    let organizations = [];

    if (isMongoConnected()) {
      const Organization = require('../models/Organization');
      organizations = await Organization.find()
        .populate('createdBy', 'username')
        .sort({ createdAt: -1 })
        .limit(50);
    } else {
      // Use in-memory storage
      organizations = inMemoryOrganizations;
    }

    res.json({
      success: true,
      message: 'Organizations retrieved successfully',
      data: {
        organizations,
        pagination: {
          totalCount: organizations.length,
          currentPage: 1,
          totalPages: 1,
          limit: 50
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error retrieving organizations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve organizations',
      timestamp: new Date().toISOString()
    });
  }
});

router.get('/organizations/stats', async (req, res) => {
  try {
    let stats = {
      totalOrganizations: 0,
      activeOrganizations: 0,
      pendingOrganizations: 0,
      verifiedOrganizations: 0
    };

    if (isMongoConnected()) {
      const Organization = require('../models/Organization');
      // Only get non-deleted organizations
      const organizations = await Organization.find({ isDeleted: false });
      stats = {
        totalOrganizations: organizations.length,
        activeOrganizations: organizations.filter(o => o.status === 'active').length,
        pendingOrganizations: organizations.filter(o => o.status === 'pending').length,
        verifiedOrganizations: organizations.filter(o => o.isVerified).length
      };
    } else {
      stats = {
        totalOrganizations: inMemoryOrganizations.length,
        activeOrganizations: inMemoryOrganizations.filter(o => o.status === 'active').length,
        pendingOrganizations: 0,
        verifiedOrganizations: inMemoryOrganizations.length,
        // Add mock drone data for development
        totalDrones: 45,
        activeDrones: 32,
        totalUsers: 15,
        totalFlightHours: 1250
      };
    }

    res.json({
      success: true,
      message: 'Organization statistics retrieved successfully',
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error retrieving organization stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve organization statistics',
      timestamp: new Date().toISOString()
    });
  }
});



// router.use('/individuals', individualRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
