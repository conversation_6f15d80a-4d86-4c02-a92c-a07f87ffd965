import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Download,
  Calendar,
  DollarSign,
  Wrench,
  MapPin,
  ChevronRight
} from 'lucide-react';

const WorkOrders = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [workOrders, setWorkOrders] = useState([]);

  // Initialize with sample work orders data
  React.useEffect(() => {
    if (workOrders.length === 0) {
      setWorkOrders([
    {
      id: 'WO-2024-001',
      title: 'Emergency Battery Replacement',
      description: 'Critical battery failure detected during pre-flight check. Immediate replacement required.',
      droneId: 'DRN-001',
      droneName: 'Surveyor Alpha',
      priority: 'Critical',
      status: 'In Progress',
      type: 'Corrective',
      assignedTo: '<PERSON>',
      createdBy: '<PERSON>',
      createdDate: '2024-01-15',
      dueDate: '2024-01-15',
      estimatedHours: 3,
      actualHours: 2.5,
      estimatedCost: 850,
      actualCost: 820,
      location: 'Hangar A',
      parts: ['Battery Pack LiPo 6S', 'Battery Connector', 'Thermal Pad'],
      progress: 75,
      lastUpdate: '2 hours ago'
    },
    {
      id: 'WO-2024-002',
      title: 'Routine Propeller Inspection',
      description: 'Scheduled inspection of all propellers for wear, damage, and balance.',
      droneId: 'DRN-003',
      droneName: 'Scout Beta',
      priority: 'Medium',
      status: 'Pending',
      type: 'Preventive',
      assignedTo: 'Mike Wilson',
      createdBy: 'Lisa Chen',
      createdDate: '2024-01-14',
      dueDate: '2024-01-18',
      estimatedHours: 1.5,
      actualHours: 0,
      estimatedCost: 120,
      actualCost: 0,
      location: 'Workshop B',
      parts: ['Propeller Set', 'Balance Weights'],
      progress: 0,
      lastUpdate: '1 day ago'
    },
    {
      id: 'WO-2024-003',
      title: 'Camera System Calibration',
      description: 'Recalibrate camera system after minor impact. Check alignment and focus.',
      droneId: 'DRN-005',
      droneName: 'Mapper Gamma',
      priority: 'High',
      status: 'Completed',
      type: 'Corrective',
      assignedTo: 'Sarah Johnson',
      createdBy: 'John Smith',
      createdDate: '2024-01-12',
      dueDate: '2024-01-14',
      estimatedHours: 4,
      actualHours: 3.5,
      estimatedCost: 200,
      actualCost: 180,
      location: 'Lab C',
      parts: ['Calibration Target', 'Lens Cleaning Kit'],
      progress: 100,
      lastUpdate: '2 days ago'
    },
    {
      id: 'WO-2024-004',
      title: 'Firmware Update Package',
      description: 'Update flight control firmware and sensor calibration software.',
      droneId: 'DRN-002',
      droneName: 'Inspector Delta',
      priority: 'Low',
      status: 'On Hold',
      type: 'Preventive',
      assignedTo: 'Lisa Chen',
      createdBy: 'Mike Wilson',
      createdDate: '2024-01-10',
      dueDate: '2024-01-20',
      estimatedHours: 2,
      actualHours: 0,
      estimatedCost: 0,
      actualCost: 0,
      location: 'Remote',
      parts: [],
      progress: 0,
      lastUpdate: '5 days ago'
    }
      ]);
    }
  }, [workOrders.length]);

  // Form state for creating new work order
  const [newWorkOrder, setNewWorkOrder] = useState({
    title: '',
    description: '',
    droneId: '',
    droneName: '',
    priority: 'Medium',
    type: 'Corrective',
    assignedTo: '',
    dueDate: '',
    estimatedHours: '',
    estimatedCost: '',
    location: ''
  });

  // Add new work order function
  const handleCreateWorkOrder = (e) => {
    e.preventDefault();
    const newOrder = {
      id: `WO-2024-${String(workOrders.length + 1).padStart(3, '0')}`,
      ...newWorkOrder,
      status: 'Pending',
      createdBy: 'Current User',
      createdDate: new Date().toISOString().split('T')[0],
      actualHours: 0,
      actualCost: 0,
      parts: [],
      progress: 0,
      lastUpdate: 'Just now'
    };
    setWorkOrders([...workOrders, newOrder]);
    setNewWorkOrder({
      title: '',
      description: '',
      droneId: '',
      droneName: '',
      priority: 'Medium',
      type: 'Corrective',
      assignedTo: '',
      dueDate: '',
      estimatedHours: '',
      estimatedCost: '',
      location: ''
    });
    setShowCreateModal(false);
  };

  // Update work order status
  const handleUpdateStatus = (id, newStatus) => {
    setWorkOrders(workOrders.map(order =>
      order.id === id ? { ...order, status: newStatus, lastUpdate: 'Just now' } : order
    ));
  };

  // Delete work order
  const handleDeleteWorkOrder = (id) => {
    setWorkOrders(workOrders.filter(order => order.id !== id));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'On Hold': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'Cancelled': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical': return 'text-red-600 bg-red-50';
      case 'High': return 'text-orange-600 bg-orange-50';
      case 'Medium': return 'text-yellow-600 bg-yellow-50';
      case 'Low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pending': return <Clock className="w-4 h-4" />;
      case 'In Progress': return <Wrench className="w-4 h-4" />;
      case 'Completed': return <CheckCircle className="w-4 h-4" />;
      case 'On Hold': return <AlertTriangle className="w-4 h-4" />;
      case 'Cancelled': return <XCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const filteredWorkOrders = workOrders.filter(order => {
    const matchesSearch = order.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.assignedTo.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || order.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesPriority = filterPriority === 'all' || order.priority.toLowerCase() === filterPriority.toLowerCase();
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search work orders..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="pending">Pending</option>
        <option value="in progress">In Progress</option>
        <option value="completed">Completed</option>
        <option value="on hold">On Hold</option>
      </select>

      <select
        value={filterPriority}
        onChange={(e) => setFilterPriority(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Priority</option>
        <option value="critical">Critical</option>
        <option value="high">High</option>
        <option value="medium">Medium</option>
        <option value="low">Low</option>
      </select>

      <button
        onClick={() => setShowCreateModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">Create Work Order</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Work Orders"
      subtitle="Create and manage maintenance work orders"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(wo => wo.status === 'In Progress' || wo.status === 'Pending').length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Wrench className="w-3 h-3" />
                  In progress
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <FileText className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(wo => wo.status === 'Completed').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  This month
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Priority</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workOrders.filter(wo => wo.priority === 'Critical').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Needs attention
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Cost</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${workOrders.reduce((sum, wo) => sum + wo.actualCost, 0).toLocaleString()}
                </p>
                <p className="text-sm text-purple-600 flex items-center gap-1 mt-1">
                  <DollarSign className="w-3 h-3" />
                  This month
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#faf5ff'}}>
                <DollarSign className="w-6 h-6 text-purple-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Work Orders List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Work Orders</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage all maintenance work orders</p>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-1">
                  <Download className="w-4 h-4" />
                  Export
                </button>
              </div>
            </div>
          </div>

          <div className="divide-y divide-gray-200">
            {filteredWorkOrders.map((order) => (
              <div key={order.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{order.title}</h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">{order.status}</span>
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(order.priority)}`}>
                        {order.priority}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{order.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-500 uppercase tracking-wider">Work Order ID</p>
                        <p className="text-sm font-medium text-gray-900">{order.id}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 uppercase tracking-wider">Drone</p>
                        <p className="text-sm font-medium text-gray-900">{order.droneId}</p>
                        <p className="text-xs text-gray-600">{order.droneName}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 uppercase tracking-wider">Assigned To</p>
                        <p className="text-sm font-medium text-gray-900 flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {order.assignedTo}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 uppercase tracking-wider">Due Date</p>
                        <p className="text-sm font-medium text-gray-900 flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {order.dueDate}
                        </p>
                      </div>
                    </div>

                    {order.status === 'In Progress' && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium text-gray-900">{order.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${order.progress}%`,
                              background: 'linear-gradient(to right, #a5b4fc, #c7d2fe)'
                            }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-6 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {order.actualHours || order.estimatedHours}h
                      </span>
                      <span className="flex items-center gap-1">
                        <DollarSign className="w-4 h-4" />
                        ${order.actualCost || order.estimatedCost}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {order.location}
                      </span>
                      <span className="text-gray-500">Updated {order.lastUpdate}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <select
                      value={order.status}
                      onChange={(e) => handleUpdateStatus(order.id, e.target.value)}
                      className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="Pending">Pending</option>
                      <option value="In Progress">In Progress</option>
                      <option value="Completed">Completed</option>
                      <option value="On Hold">On Hold</option>
                      <option value="Cancelled">Cancelled</option>
                    </select>
                    <button
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                      onClick={() => handleDeleteWorkOrder(order.id)}
                      title="Delete Work Order"
                    >
                      <XCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Create Work Order Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Create New Work Order</h3>
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <form onSubmit={handleCreateWorkOrder} className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Work Order Title*</label>
                    <input
                      type="text"
                      required
                      value={newWorkOrder.title}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, title: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Emergency Battery Replacement"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID*</label>
                    <input
                      type="text"
                      required
                      value={newWorkOrder.droneId}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name*</label>
                    <input
                      type="text"
                      required
                      value={newWorkOrder.droneName}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Priority*</label>
                    <select
                      required
                      value={newWorkOrder.priority}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, priority: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                      <option value="Critical">Critical</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Work Order Type*</label>
                    <select
                      required
                      value={newWorkOrder.type}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Preventive">Preventive</option>
                      <option value="Corrective">Corrective</option>
                      <option value="Emergency">Emergency</option>
                      <option value="Routine">Routine</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To*</label>
                    <input
                      type="text"
                      required
                      value={newWorkOrder.assignedTo}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, assignedTo: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Due Date*</label>
                    <input
                      type="date"
                      required
                      value={newWorkOrder.dueDate}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, dueDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Estimated Hours</label>
                    <input
                      type="number"
                      step="0.5"
                      value={newWorkOrder.estimatedHours}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, estimatedHours: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 3"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Estimated Cost</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newWorkOrder.estimatedCost}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, estimatedCost: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 850"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location*</label>
                    <input
                      type="text"
                      required
                      value={newWorkOrder.location}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Hangar A"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description*</label>
                    <textarea
                      required
                      rows={4}
                      value={newWorkOrder.description}
                      onChange={(e) => setNewWorkOrder({...newWorkOrder, description: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Describe the work to be performed..."
                    />
                  </div>
                </div>

                <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Work Order
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default WorkOrders;
