{"name": "shakti-drone-management-server", "version": "1.0.0", "description": "Backend API for SHAKTI Drone Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node utils/seedDatabase.js"}, "keywords": ["drone", "management", "api", "nodejs", "express", "mongodb"], "author": "SHAKTI Development Team", "license": "ISC", "engines": {"node": ">=14.0.0"}, "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.19.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "minimatch": "^10.0.3", "mongoose": "^8.16.5", "morgan": "^1.10.1", "node-cron": "^4.2.1"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}