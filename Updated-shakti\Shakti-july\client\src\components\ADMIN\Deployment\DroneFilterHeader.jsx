import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  Plus,
  Calendar,
  MapPin,
  Building2,
  Users,
  TrendingUp,
  BarChart3,
  Settings,
  X
} from 'lucide-react';
import {
  FaCheckCircle,
  FaMapMarkerAlt,
  FaSortAmountDown,
  FaBuilding,
  FaPlane,
  FaExclamationTriangle,
  FaClock,
  FaFilter,
  FaDownload,
  FaUserPlus
} from "react-icons/fa";

const DroneFilterHeader = ({
  searchTerm = '',
  setSearchTerm,
  statusFilter = 'All',
  setStatusFilter,
  locationFilter = 'All',
  setLocationFilter,
  sortBy = 'date',
  setSortBy,
  sortOrder = 'desc',
  setSortOrder,
  showFilters = false,
  setShowFilters
}) => {
  const navigate = useNavigate();
  const [activeFilters, setActiveFilters] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRegisterOrganization = () => {
    navigate("/organizationform");
  };

  const handleExportData = () => {
    console.log('Exporting deployment data...');
    // Here you would implement the export functionality
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => setIsRefreshing(false), 1000);
    console.log('Refreshing data...');
  };

  const handleNewRequest = () => {
    navigate('/admin/deployment/new');
  };

  const statusOptions = [
    { value: 'All', label: 'All Status', color: 'text-gray-600' },
    { value: 'Pending', label: 'Pending', color: 'text-orange-600' },
    { value: 'Approved', label: 'Approved', color: 'text-green-600' },
    { value: 'Rejected', label: 'Rejected', color: 'text-red-600' },
    { value: 'Active', label: 'Active', color: 'text-blue-600' }
  ];

  const locationOptions = [
    { value: 'All', label: 'All Locations' },
    { value: 'Maharashtra', label: 'Maharashtra' },
    { value: 'Karnataka', label: 'Karnataka' },
    { value: 'Gujarat', label: 'Gujarat' },
    { value: 'Punjab', label: 'Punjab' },
    { value: 'Telangana', label: 'Telangana' },
    { value: 'Haryana', label: 'Haryana' },
    { value: 'Kerala', label: 'Kerala' },
    { value: 'Uttar Pradesh', label: 'Uttar Pradesh' },
    { value: 'Madhya Pradesh', label: 'Madhya Pradesh' },
    { value: 'Bihar', label: 'Bihar' }
  ];

  const sortOptions = [
    { value: 'date', label: 'Date Created', icon: Calendar },
    { value: 'name', label: 'Drone Name', icon: FaPlane },
    { value: 'organization', label: 'Organization', icon: Building2 },
    { value: 'priority', label: 'Priority', icon: FaExclamationTriangle },
    { value: 'status', label: 'Status', icon: FaCheckCircle }
  ];

  const clearAllFilters = () => {
    setStatusFilter('All');
    setLocationFilter('All');
    setSortBy('date');
    setSortOrder('desc');
    setSearchTerm('');
    setActiveFilters([]);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (statusFilter !== 'All') count++;
    if (locationFilter !== 'All') count++;
    if (searchTerm) count++;
    return count;
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Main Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-4">
          {/* Left Section - Search and Quick Stats */}
          <div className="flex-1 space-y-4 xl:space-y-0">
            {/* Search Bar */}
            <div className="relative max-w-lg">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search drones, organizations, requests, or IDs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm && setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Quick Filter Tags */}
            <div className="flex flex-wrap items-center gap-2">
              {getActiveFilterCount() > 0 && (
                <>
                  <span className="text-sm text-gray-500 font-medium">Active Filters:</span>
                  {statusFilter !== 'All' && (
                    <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                      <FaCheckCircle className="w-3 h-3" />
                      {statusFilter}
                      <button
                        onClick={() => setStatusFilter('All')}
                        className="ml-1 hover:text-blue-900"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  {locationFilter !== 'All' && (
                    <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      <MapPin className="w-3 h-3" />
                      {locationFilter}
                      <button
                        onClick={() => setLocationFilter('All')}
                        className="ml-1 hover:text-green-900"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  {searchTerm && (
                    <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                      <Search className="w-3 h-3" />
                      "{searchTerm}"
                      <button
                        onClick={() => setSearchTerm('')}
                        className="ml-1 hover:text-purple-900"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  <button
                    onClick={clearAllFilters}
                    className="text-xs text-gray-500 hover:text-gray-700 underline"
                  >
                    Clear all
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Right Section - Action Buttons */}
          <div className="flex flex-wrap items-center gap-3">
            <button
              onClick={() => setShowFilters && setShowFilters(!showFilters)}
              className={`flex items-center gap-2 px-4 py-2 border rounded-lg transition-all duration-200 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700 shadow-sm'
                  : 'border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
            >
              <FaFilter className="w-4 h-4" />
              <span className="hidden sm:inline">Advanced Filters</span>
              <span className="sm:hidden">Filters</span>
              {getActiveFilterCount() > 0 && (
                <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getActiveFilterCount()}
                </span>
              )}
            </button>

            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
            </button>

            <button
              onClick={handleNewRequest}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 shadow-sm"
            >
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">New Request</span>
              <span className="sm:hidden">New</span>
            </button>

            <button
              onClick={handleRegisterOrganization}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm"
            >
              <FaUserPlus className="w-4 h-4" />
              <span className="hidden lg:inline">Register Organization</span>
              <span className="lg:hidden">Register Org</span>
            </button>

            <button
              onClick={handleExportData}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
            >
              <FaDownload className="w-4 h-4" />
              <span className="hidden sm:inline">Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Expandable Filters Row */}
      {showFilters && (
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FaCheckCircle className="text-blue-600" size={14} />
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter && setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                <option value="All">All Status</option>
                <option value="Pending">Pending</option>
                <option value="Approved">Approved</option>
                <option value="Rejected">Rejected</option>
                <option value="Active">Active</option>
              </select>
            </div>

            {/* Location Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FaMapMarkerAlt className="text-green-600" size={14} />
                Location
              </label>
              <select
                value={locationFilter}
                onChange={(e) => setLocationFilter && setLocationFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                <option value="All">All Locations</option>
                <option value="Maharashtra">Maharashtra</option>
                <option value="Karnataka">Karnataka</option>
                <option value="Gujarat">Gujarat</option>
                <option value="Punjab">Punjab</option>
                <option value="Telangana">Telangana</option>
                <option value="Haryana">Haryana</option>
                <option value="Kerala">Kerala</option>
                <option value="Uttar Pradesh">Uttar Pradesh</option>
                <option value="Madhya Pradesh">Madhya Pradesh</option>
                <option value="Bihar">Bihar</option>
              </select>
            </div>

            {/* Sort By Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FaSortAmountDown className="text-purple-600" size={14} />
                Sort By
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy && setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                <option value="date">Date</option>
                <option value="droneName">Drone Name</option>
                <option value="orgName">Organization</option>
                <option value="status">Status</option>
                <option value="priority">Priority</option>
              </select>
            </div>

            {/* Sort Order Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FaSortAmountDown className="text-orange-600" size={14} />
                Order
              </label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder && setSortOrder(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>

          {/* Filter Summary */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-600">Active Filters:</span>
              {statusFilter !== 'All' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Status: {statusFilter}
                </span>
              )}
              {locationFilter !== 'All' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Location: {locationFilter}
                </span>
              )}
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Sort: {sortBy} ({sortOrder === 'desc' ? 'Newest' : 'Oldest'} First)
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DroneFilterHeader;
