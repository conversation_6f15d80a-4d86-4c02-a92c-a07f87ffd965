require('dotenv').config({ path: './server/.env' });
const mongoose = require('mongoose');

async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing database connection...');
    console.log('MongoDB URI:', process.env.MONGODB_URI ? 'Set' : 'Not set');
    
    if (!process.env.MONGODB_URI) {
      console.log('❌ MONGODB_URI environment variable is not set');
      return;
    }
    
    // Test connection
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
    });
    
    console.log('✅ Database connected successfully!');
    console.log('Host:', conn.connection.host);
    console.log('Database:', conn.connection.name);
    
    // Close connection
    await mongoose.connection.close();
    console.log('🔒 Connection closed');
    
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    
    if (error.message.includes('ENOTFOUND')) {
      console.log('💡 This looks like a network/DNS issue. Check your internet connection.');
    } else if (error.message.includes('authentication')) {
      console.log('💡 This looks like an authentication issue. Check your MongoDB credentials.');
    } else if (error.message.includes('timeout')) {
      console.log('💡 Connection timeout. The MongoDB server might be unreachable.');
    }
  }
}

testDatabaseConnection();
