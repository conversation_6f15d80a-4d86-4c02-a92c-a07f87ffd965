import React, { useState } from 'react';
import {
    CheckC<PERSON>cle,
    Eye,
    EyeOff,
    Archive,
    Trash2,
    Download,
    Share2,
    X,
    <PERSON><PERSON><PERSON>riangle,
    <PERSON>,
    <PERSON>,
    Settings,
    Filter,
    MoreHorizontal
} from 'lucide-react';

const NotificationActions = ({
    selectedCount,
    onMarkAsRead,
    onMarkAsUnread,
    onArchive,
    onDelete,
    onClearSelection
}) => {
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [showMoreActions, setShowMoreActions] = useState(false);

    const ActionButton = ({ 
        icon: Icon, 
        label, 
        onClick, 
        color = "blue", 
        variant = "primary",
        disabled = false 
    }) => {
        const baseClasses = "inline-flex items-center gap-2 px-3 lg:px-4 py-2 lg:py-2.5 rounded-lg font-medium text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
        
        const variants = {
            primary: `bg-${color}-600 text-white hover:bg-${color}-700 shadow-md hover:shadow-lg`,
            secondary: `bg-white text-${color}-600 border border-${color}-300 hover:bg-${color}-50 hover:shadow-md`,
            danger: "bg-red-600 text-white hover:bg-red-700 shadow-md hover:shadow-lg",
            ghost: "bg-gray-100 text-gray-700 hover:bg-gray-200"
        };

        return (
            <button
                onClick={onClick}
                disabled={disabled}
                className={`${baseClasses} ${variants[variant]}`}
            >
                <Icon className="w-4 h-4 flex-shrink-0" />
                <span className="hidden sm:inline">{label}</span>
            </button>
        );
    };

    const bulkActions = [
        {
            icon: Eye,
            label: "Mark as Read",
            onClick: onMarkAsRead,
            color: "green",
            variant: "secondary"
        },
        {
            icon: EyeOff,
            label: "Mark as Unread",
            onClick: onMarkAsUnread,
            color: "orange",
            variant: "secondary"
        },
        {
            icon: Archive,
            label: "Archive",
            onClick: onArchive,
            color: "blue",
            variant: "secondary"
        }
    ];

    const additionalActions = [
        {
            icon: Download,
            label: "Export",
            onClick: () => console.log("Export notifications"),
            color: "purple",
            variant: "ghost"
        },
        {
            icon: Share2,
            label: "Share",
            onClick: () => console.log("Share notifications"),
            color: "indigo",
            variant: "ghost"
        },
        {
            icon: Settings,
            label: "Settings",
            onClick: () => console.log("Open settings"),
            color: "gray",
            variant: "ghost"
        }
    ];

    return (
        <div className="bg-white rounded-xl p-4 lg:p-6 border border-gray-200 shadow-sm">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4 lg:gap-0 lg:justify-between">
                {/* Selection Info */}
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-blue-600" />
                        <span className="font-semibold text-gray-900">
                            {selectedCount} notification{selectedCount !== 1 ? 's' : ''} selected
                        </span>
                    </div>
                    <button
                        onClick={onClearSelection}
                        className="flex items-center gap-1 px-2 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-white rounded-md transition-colors"
                    >
                        <X className="w-3 h-3" />
                        Clear
                    </button>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap items-center gap-2 lg:gap-3">
                    {/* Primary Bulk Actions */}
                    <div className="flex flex-wrap gap-2 lg:gap-3">
                        {bulkActions.map((action, index) => (
                            <ActionButton key={index} {...action} />
                        ))}
                    </div>

                    {/* Separator */}
                    <div className="hidden lg:block w-px h-8 bg-gray-300"></div>

                    {/* Delete Action */}
                    <div className="relative">
                        {!showDeleteConfirm ? (
                            <ActionButton
                                icon={Trash2}
                                label="Delete"
                                onClick={() => setShowDeleteConfirm(true)}
                                variant="danger"
                            />
                        ) : (
                            <div className="flex items-center gap-2 bg-white rounded-lg p-2 border border-red-200">
                                <AlertTriangle className="w-4 h-4 text-red-600" />
                                <span className="text-sm font-medium text-red-800">Confirm delete?</span>
                                <button
                                    onClick={() => {
                                        onDelete();
                                        setShowDeleteConfirm(false);
                                    }}
                                    className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
                                >
                                    Yes
                                </button>
                                <button
                                    onClick={() => setShowDeleteConfirm(false)}
                                    className="px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 transition-colors"
                                >
                                    No
                                </button>
                            </div>
                        )}
                    </div>

                    {/* More Actions Dropdown */}
                    <div className="relative">
                        <button
                            onClick={() => setShowMoreActions(!showMoreActions)}
                            className="p-2 lg:p-2.5 bg-white text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 hover:shadow-md transition-all duration-200"
                        >
                            <MoreHorizontal className="w-4 h-4" />
                        </button>

                        {showMoreActions && (
                            <div className="absolute right-0 top-12 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-20 min-w-[160px]">
                                {additionalActions.map((action, index) => {
                                    const IconComponent = action.icon;
                                    return (
                                        <button
                                            key={index}
                                            onClick={() => {
                                                action.onClick();
                                                setShowMoreActions(false);
                                            }}
                                            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-3 text-gray-700"
                                        >
                                            <IconComponent className="w-4 h-4" />
                                            {action.label}
                                        </button>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Quick Stats */}
            <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-blue-600" />
                            <span className="text-sm font-medium text-gray-700">Avg. Response</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900 mt-1">2.3 min</p>
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-gray-700">Active Pilots</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900 mt-1">12</p>
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-red-600" />
                            <span className="text-sm font-medium text-gray-700">Critical</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900 mt-1">3</p>
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="flex items-center gap-2">
                            <Archive className="w-4 h-4 text-purple-600" />
                            <span className="text-sm font-medium text-gray-700">Resolved</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900 mt-1">87%</p>
                    </div>
                </div>
            </div>

            {/* Action Shortcuts */}
            <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex flex-wrap items-center gap-2 text-xs text-gray-600 text-black">
                    <span className="font-medium">Quick shortcuts:</span>
                    <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">Ctrl+A</kbd>
                    <span>Select All</span>
                    <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">Ctrl+R</kbd>
                    <span>Mark Read</span>
                    <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">Del</kbd>
                    <span>Delete</span>
                </div>
            </div>
        </div>
    );
};

export default NotificationActions;
