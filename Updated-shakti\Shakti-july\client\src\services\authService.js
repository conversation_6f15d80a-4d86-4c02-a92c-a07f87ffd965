import api from './api';

/**
 * Authentication service for handling login, logout, and user management
 */
class AuthService {
  /**
   * Login user with username/email and password
   * @param {string} username - Username or email
   * @param {string} password - User password
   * @param {string} role - Optional role filter
   * @returns {Promise<Object>} Login response with user data and token
   */
  async login(username, password, role = null) {
    try {
      const loginData = { username, password };
      if (role) {
        loginData.role = role;
      }

      const response = await api.post('/auth/login', loginData);
      
      if (response.data.success) {
        const { user, token } = response.data.data;
        
        // Store token and user data
        localStorage.setItem('authToken', token);
        localStorage.setItem('user', JSON.stringify(user));
        
        return {
          success: true,
          user,
          token,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      
      // Extract error message from response
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Login failed. Please try again.';
      
      throw new Error(errorMessage);
    }
  }

  /**
   * Register new user (Admin only)
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration response
   */
  async register(userData) {
    try {
      const response = await api.post('/auth/register', userData);
      
      if (response.data.success) {
        return {
          success: true,
          user: response.data.data.user,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Registration failed. Please try again.';
      
      throw new Error(errorMessage);
    }
  }

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile data
   */
  async getProfile() {
    try {
      const response = await api.get('/auth/me');
      
      if (response.data.success) {
        return {
          success: true,
          user: response.data.data.user
        };
      } else {
        throw new Error(response.data.message || 'Failed to get profile');
      }
    } catch (error) {
      console.error('Get profile error:', error);
      throw new Error(error.response?.data?.message || 'Failed to get profile');
    }
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile update data
   * @returns {Promise<Object>} Updated profile response
   */
  async updateProfile(profileData) {
    try {
      const response = await api.put('/auth/profile', profileData);
      
      if (response.data.success) {
        const updatedUser = response.data.data.user;
        
        // Update stored user data
        localStorage.setItem('user', JSON.stringify(updatedUser));
        
        return {
          success: true,
          user: updatedUser,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error(error.response?.data?.message || 'Profile update failed');
    }
  }

  /**
   * Change user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Password change response
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await api.put('/auth/change-password', {
        currentPassword,
        newPassword,
        confirmPassword: newPassword
      });
      
      if (response.data.success) {
        return {
          success: true,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Password change failed');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw new Error(error.response?.data?.message || 'Password change failed');
    }
  }

  /**
   * Logout user
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      // Call logout endpoint (optional for JWT)
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('droneDraft');
    }
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    if (!token || !user) return false;
    
    try {
      // Verify the user data is valid JSON
      const userData = JSON.parse(user);
      return !!(userData && userData.role);
    } catch (error) {
      // If there's any error parsing the user data, consider it as not authenticated
      console.error('Error parsing user data:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      return false;
    }
  }

  /**
   * Get current user from localStorage
   * @returns {Object|null} Current user data
   */
  getCurrentUser() {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  /**
   * Get current auth token
   * @returns {string|null} Current auth token
   */
  getToken() {
    return localStorage.getItem('authToken');
  }

  /**
   * Check if current user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} Role check result
   */
  hasRole(role) {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  /**
   * Check if current user is admin
   * @returns {boolean} Admin status
   */
  isAdmin() {
    return this.hasRole('admin');
  }

  /**
   * Check if current user is organization
   * @returns {boolean} Organization status
   */
  isOrganization() {
    return this.hasRole('org');
  }

  /**
   * Check if current user is maintenance/QC
   * @returns {boolean} Maintenance status
   */
  isMaintenance() {
    return this.hasRole('maintenance');
  }
}

// Export singleton instance
const authService = new AuthService();
export default authService;
