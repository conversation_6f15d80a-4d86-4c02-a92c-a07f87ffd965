import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  Activity,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Battery,
  Wifi,
  Camera,
  Settings,
  Thermometer,
  Zap,
  HardDrive,
  Cpu
} from 'lucide-react';

const SystemHealth = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [healthChecks, setHealthChecks] = useState([]);

  // Initialize with sample health check data
  React.useEffect(() => {
    if (healthChecks.length === 0) {
      setHealthChecks([
        {
          id: 'SH-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          technician: '<PERSON>',
          date: '2024-01-15',
          time: '10:30',
          location: 'Maintenance Bay A',
          status: 'Healthy',
          overallScore: 95,
          duration: '15 minutes',
          systems: {
            battery: { status: 'Healthy', score: 98, temperature: '22°C', voltage: '25.2V', cycles: '145' },
            motors: { status: 'Healthy', score: 96, temperature: '28°C', vibration: 'Low', efficiency: '98%' },
            sensors: { status: 'Warning', score: 85, calibration: 'Drift detected', accuracy: 'Good', temperature: '24°C' },
            communication: { status: 'Healthy', score: 94, signal: 'Strong', latency: '12ms', range: 'Full' },
            storage: { status: 'Healthy', score: 92, usage: '68%', speed: 'Normal', errors: '0' },
            processor: { status: 'Healthy', score: 97, load: '45%', temperature: '35°C', memory: '78%' }
          },
          issues: ['Minor sensor drift detected in IMU'],
          recommendations: ['Recalibrate IMU sensors', 'Monitor sensor performance'],
          nextCheck: '2024-01-22'
        },
        {
          id: 'SH-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          technician: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:15',
          location: 'Field Station B',
          status: 'Critical',
          overallScore: 65,
          duration: '25 minutes',
          systems: {
            battery: { status: 'Warning', score: 75, temperature: '35°C', voltage: '23.8V', cycles: '298' },
            motors: { status: 'Critical', score: 45, temperature: '68°C', vibration: 'High', efficiency: '82%' },
            sensors: { status: 'Healthy', score: 88, calibration: 'Good', accuracy: 'High', temperature: '26°C' },
            communication: { status: 'Healthy', score: 91, signal: 'Strong', latency: '15ms', range: 'Full' },
            storage: { status: 'Warning', score: 78, usage: '89%', speed: 'Slow', errors: '3' },
            processor: { status: 'Healthy', score: 89, load: '62%', temperature: '42°C', memory: '85%' }
          },
          issues: ['Motor #2 overheating', 'High vibration levels', 'Storage near capacity', 'Battery degradation'],
          recommendations: ['Replace motor #2 immediately', 'Clean storage and defragment', 'Consider battery replacement'],
          nextCheck: '2024-01-15'
        },
        {
          id: 'SH-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          technician: 'Mike Wilson',
          date: '2024-01-13',
          time: '16:45',
          location: 'Hangar A',
          status: 'Warning',
          overallScore: 82,
          duration: '18 minutes',
          systems: {
            battery: { status: 'Healthy', score: 94, temperature: '24°C', voltage: '25.0V', cycles: '89' },
            motors: { status: 'Healthy', score: 92, temperature: '32°C', vibration: 'Normal', efficiency: '95%' },
            sensors: { status: 'Warning', score: 76, calibration: 'Needs adjustment', accuracy: 'Fair', temperature: '28°C' },
            communication: { status: 'Warning', score: 79, signal: 'Weak', latency: '28ms', range: 'Reduced' },
            storage: { status: 'Healthy', score: 88, usage: '45%', speed: 'Normal', errors: '0' },
            processor: { status: 'Healthy', score: 91, load: '38%', temperature: '31°C', memory: '65%' }
          },
          issues: ['Communication signal degradation', 'Sensor calibration drift'],
          recommendations: ['Check antenna connections', 'Recalibrate all sensors', 'Update communication firmware'],
          nextCheck: '2024-01-20'
        }
      ]);
    }
  }, [healthChecks.length]);

  // Form state for new health check
  const [newHealthCheck, setNewHealthCheck] = useState({
    droneId: '',
    droneName: '',
    technician: '',
    date: '',
    time: '',
    location: '',
    systems: {
      battery: { status: 'Healthy', score: 95, temperature: '', voltage: '', cycles: '' },
      motors: { status: 'Healthy', score: 95, temperature: '', vibration: '', efficiency: '' },
      sensors: { status: 'Healthy', score: 95, calibration: '', accuracy: '', temperature: '' },
      communication: { status: 'Healthy', score: 95, signal: '', latency: '', range: '' },
      storage: { status: 'Healthy', score: 95, usage: '', speed: '', errors: '' },
      processor: { status: 'Healthy', score: 95, load: '', temperature: '', memory: '' }
    },
    issues: [],
    recommendations: [],
    notes: ''
  });

  // Add new health check function
  const handleAddHealthCheck = (e) => {
    e.preventDefault();
    
    // Calculate overall score and status
    const scores = Object.values(newHealthCheck.systems).map(s => s.score);
    const overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
    
    let status = 'Healthy';
    if (overallScore < 70) status = 'Critical';
    else if (overallScore < 85) status = 'Warning';

    const newCheck = {
      id: `SH-2024-${String(healthChecks.length + 1).padStart(3, '0')}`,
      ...newHealthCheck,
      status,
      overallScore,
      duration: '20 minutes',
      nextCheck: new Date(Date.now() + 7*24*60*60*1000).toISOString().split('T')[0]
    };
    
    setHealthChecks([...healthChecks, newCheck]);
    setNewHealthCheck({
      droneId: '',
      droneName: '',
      technician: '',
      date: '',
      time: '',
      location: '',
      systems: {
        battery: { status: 'Healthy', score: 95, temperature: '', voltage: '', cycles: '' },
        motors: { status: 'Healthy', score: 95, temperature: '', vibration: '', efficiency: '' },
        sensors: { status: 'Healthy', score: 95, calibration: '', accuracy: '', temperature: '' },
        communication: { status: 'Healthy', score: 95, signal: '', latency: '', range: '' },
        storage: { status: 'Healthy', score: 95, usage: '', speed: '', errors: '' },
        processor: { status: 'Healthy', score: 95, load: '', temperature: '', memory: '' }
      },
      issues: [],
      recommendations: [],
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete health check function
  const handleDeleteHealthCheck = (id) => {
    setHealthChecks(healthChecks.filter(check => check.id !== id));
  };

  // Update health check status
  const handleUpdateStatus = (id, newStatus) => {
    setHealthChecks(healthChecks.map(check => 
      check.id === id ? { ...check, status: newStatus } : check
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Healthy': return 'bg-green-100 text-green-700 border-green-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Critical': return 'bg-red-100 text-red-700 border-red-200';
      case 'Checking': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Healthy': return <CheckCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      case 'Critical': return <XCircle className="w-4 h-4" />;
      case 'Checking': return <Clock className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSystemStatusColor = (status) => {
    switch (status) {
      case 'Healthy': return 'text-green-600';
      case 'Warning': return 'text-yellow-600';
      case 'Critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const filteredHealthChecks = healthChecks.filter(check => {
    const matchesSearch = check.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         check.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         check.technician.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || check.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesDrone = filterDrone === 'all' || check.droneId === filterDrone;
    return matchesSearch && matchesStatus && matchesDrone;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search health checks..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="healthy">Healthy</option>
        <option value="warning">Warning</option>
        <option value="critical">Critical</option>
        <option value="checking">Checking</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Health Check</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="System Health Monitoring"
      subtitle="Monitor and analyze drone system health status"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Checks</p>
                <p className="text-2xl font-bold text-gray-900">{filteredHealthChecks.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Activity className="w-3 h-3" />
                  Completed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Activity className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Healthy</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredHealthChecks.filter(c => c.status === 'Healthy').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Operational
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Warnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredHealthChecks.filter(c => c.status === 'Warning').length}
                </p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Attention needed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredHealthChecks.filter(c => c.status === 'Critical').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <XCircle className="w-3 h-3" />
                  Immediate action
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <XCircle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Health Checks Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">System Health Records</h3>
                <p className="text-sm text-gray-600 mt-1">Monitor and track drone system health diagnostics</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Health Check Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Overall Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    System Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issues & Recommendations
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredHealthChecks.map((check) => (
                  <tr key={check.id} className="hover:bg-gray-50 transition-colors">
                    {/* Health Check Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Activity className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{check.id}</h4>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(check.status)}`}>
                              {getStatusIcon(check.status)}
                              <span className="ml-1">{check.status}</span>
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{check.droneId} - {check.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {check.technician}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {check.date} at {check.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {check.location}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {check.duration}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Overall Status */}
                    <td className="px-6 py-4">
                      <div className="text-center">
                        <div className={`text-2xl font-bold ${getScoreColor(check.overallScore)}`}>
                          {check.overallScore}%
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className={`h-2 rounded-full ${check.overallScore >= 90 ? 'bg-green-500' : check.overallScore >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                            style={{width: `${check.overallScore}%`}}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Health Score</p>
                        <p className="text-xs text-gray-500">Next: {check.nextCheck}</p>
                      </div>
                    </td>

                    {/* System Status */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Battery className="w-3 h-3" />
                            Battery
                          </span>
                          <span className={`text-xs font-medium ${getSystemStatusColor(check.systems.battery.status)}`}>
                            {check.systems.battery.score}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Zap className="w-3 h-3" />
                            Motors
                          </span>
                          <span className={`text-xs font-medium ${getSystemStatusColor(check.systems.motors.status)}`}>
                            {check.systems.motors.score}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Settings className="w-3 h-3" />
                            Sensors
                          </span>
                          <span className={`text-xs font-medium ${getSystemStatusColor(check.systems.sensors.status)}`}>
                            {check.systems.sensors.score}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Wifi className="w-3 h-3" />
                            Communication
                          </span>
                          <span className={`text-xs font-medium ${getSystemStatusColor(check.systems.communication.status)}`}>
                            {check.systems.communication.score}%
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Issues & Recommendations */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {check.issues.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Issues:</p>
                            <div className="space-y-1">
                              {check.issues.slice(0, 2).map((issue, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{issue}</span>
                                </div>
                              ))}
                              {check.issues.length > 2 && (
                                <p className="text-xs text-gray-500">+{check.issues.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {check.recommendations.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Recommendations:</p>
                            <div className="space-y-1">
                              {check.recommendations.slice(0, 2).map((recommendation, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-blue-700">{recommendation}</span>
                                </div>
                              ))}
                              {check.recommendations.length > 2 && (
                                <p className="text-xs text-gray-500">+{check.recommendations.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-diagnostics/system-health/${check.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={check.status}
                          onChange={(e) => handleUpdateStatus(check.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Healthy">Healthy</option>
                          <option value="Warning">Warning</option>
                          <option value="Critical">Critical</option>
                          <option value="Checking">Checking</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteHealthCheck(check.id)}
                          title="Delete Health Check"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Health Check Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">New Health Check</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new system health check record</p>
              </div>

              <form onSubmit={handleAddHealthCheck} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID</label>
                    <input
                      type="text"
                      value={newHealthCheck.droneId}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name</label>
                    <input
                      type="text"
                      value={newHealthCheck.droneName}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Technician</label>
                    <input
                      type="text"
                      value={newHealthCheck.technician}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, technician: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={newHealthCheck.location}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Maintenance Bay A"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input
                      type="date"
                      value={newHealthCheck.date}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newHealthCheck.time}
                      onChange={(e) => setNewHealthCheck({...newHealthCheck, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={newHealthCheck.notes}
                    onChange={(e) => setNewHealthCheck({...newHealthCheck, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the health check..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Health Check
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default SystemHealth;
