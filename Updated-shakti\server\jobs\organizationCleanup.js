const cron = require('node-cron');
const { permanentlyDeleteExpiredOrganizations } = require('../controllers/organizationController');

/**
 * Background job to permanently delete organizations that have exceeded their grace period
 * Runs every day at 2:00 AM
 */
class OrganizationCleanupJob {
  constructor() {
    this.isRunning = false;
    this.lastRun = null;
    this.deletedCount = 0;
  }

  /**
   * Start the cleanup job
   */
  start() {
    console.log('🧹 Starting organization cleanup job...');
    
    // Run every day at 2:00 AM
    this.job = cron.schedule('0 2 * * *', async () => {
      await this.runCleanup();
    }, {
      scheduled: true,
      timezone: 'UTC'
    });

    // Also run immediately on startup (for testing)
    setTimeout(() => {
      this.runCleanup();
    }, 5000); // Wait 5 seconds after startup

    console.log('✅ Organization cleanup job scheduled to run daily at 2:00 AM UTC');
  }

  /**
   * Stop the cleanup job
   */
  stop() {
    if (this.job) {
      this.job.stop();
      console.log('🛑 Organization cleanup job stopped');
    }
  }

  /**
   * Run the cleanup process
   */
  async runCleanup() {
    if (this.isRunning) {
      console.log('⏳ Organization cleanup already running, skipping...');
      return;
    }

    this.isRunning = true;
    this.lastRun = new Date();

    try {
      console.log('🧹 Running organization cleanup job...');
      
      const deletedCount = await permanentlyDeleteExpiredOrganizations();
      this.deletedCount += deletedCount;

      if (deletedCount > 0) {
        console.log(`✅ Organization cleanup completed. Deleted ${deletedCount} expired organizations.`);
      } else {
        console.log('✅ Organization cleanup completed. No expired organizations found.');
      }

    } catch (error) {
      console.error('❌ Error during organization cleanup:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get job status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRun,
      totalDeleted: this.deletedCount,
      nextRun: this.job ? this.job.nextDate() : null
    };
  }

  /**
   * Manually trigger cleanup (for admin use)
   */
  async triggerManualCleanup() {
    console.log('🔧 Manual organization cleanup triggered');
    await this.runCleanup();
  }
}

// Create singleton instance
const organizationCleanupJob = new OrganizationCleanupJob();

module.exports = organizationCleanupJob;
