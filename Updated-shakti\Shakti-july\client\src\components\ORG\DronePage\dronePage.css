/* Professional Drone Page Styling */

/* Prevent horizontal overflow */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

.drone-page-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Responsive container fixes */
.responsive-container {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

.responsive-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
  min-width: 0; /* Allow grid items to shrink */
}

/* Responsive breakpoints */
@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1025px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.3s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Professional Card Hover Effects */
.drone-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drone-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.drone-card:hover .drone-card-content {
  transform: scale(1.02);
}

/* Status Indicators */
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.status-indicator:hover::before {
  left: 100%;
}

/* Professional Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Professional Scrollbars */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Glass Morphism Effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional Gradients */
.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Interactive Elements */
.interactive-element {
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: scale(1.05);
}

.interactive-element:active {
  transform: scale(0.95);
}

/* Professional Focus States */
.focus-ring:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
  ring-offset: 2px;
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .drone-card:hover {
    transform: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:hover,
  .btn-secondary:hover {
    transform: none;
  }

  /* Mobile specific fixes */
  .drone-page-main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .drone-page-header {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .drone-stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  .drone-actions-container {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .drone-filter-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* Tablet specific fixes */
@media (min-width: 769px) and (max-width: 1024px) {
  .drone-stats-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .drone-filter-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1025px) {
  .drone-page-main {
    margin-left: 18rem;
  }
}

/* Professional Table Styles */
.professional-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  min-width: 800px; /* Minimum width for table */
}

.professional-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.75rem;
  white-space: nowrap;
  padding: 0.75rem 1rem;
}

.professional-table td {
  padding: 0.75rem 1rem;
  white-space: nowrap;
}

.professional-table tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Responsive table container */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .professional-table {
    min-width: 600px;
  }

  .professional-table th,
  .professional-table td {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* Status Badge Styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.status-inactive {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.status-maintenance {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.status-crashed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.status-pending {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

/* Professional Transitions */
.transition-professional {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom Checkbox Styles */
.custom-checkbox {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-checkbox:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.custom-checkbox:checked::after {
  content: '✓';
  color: white;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
