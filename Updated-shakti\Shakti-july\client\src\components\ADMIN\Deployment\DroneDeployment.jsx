import React, { useState, useMemo } from "react";
import {
  Edit2,
  MapPin,
  Calendar,
  Building
} from 'lucide-react';

// React Icons
import {
  FaPlane,
  FaUnlock,
  FaLock,
  FaEye,
  FaEdit,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
  FaClock,
  FaRocket,
  FaTimes
} from 'react-icons/fa';

const DroneDeployment = ({
  searchTerm = '',
  statusFilter = 'All',
  locationFilter = 'All',
  sortBy = 'date',
  sortOrder = 'desc'
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [actionRequest, setActionRequest] = useState(null);

  const requests = [
    {
      id: 1,
      droneId: "PRYMAJ852741",
      droneName: "Arjuna",
      orgName: "Sky Drone Solutions",
      location: "Maharashtra",
      date: "24 Apr 2025",
      status: "Pending",
      priority: "High",
      requestedBy: "John Doe",
      contact: "<EMAIL>",
      phone: "+91 9876543210",
      purpose: "Agricultural Survey",
      duration: "3 days",
      area: "500 acres"
    },
    {
      id: 2,
      droneId: "PRYMAJ123654",
      droneName: "Arjuna",
      orgName: "EcoDrone Solutions",
      location: "Karnataka",
      date: "23 Apr 2025",
      status: "Approved",
      priority: "Medium",
      requestedBy: "Jane Smith",
      contact: "<EMAIL>",
      phone: "+91 9876543211",
      purpose: "Environmental Monitoring",
      duration: "5 days",
      area: "750 acres"
    },
    {
      id: 3,
      droneId: "PRYMAJ987456",
      droneName: "Arjuna Advance",
      orgName: "FarmVue Aerial",
      location: "Maharashtra",
      date: "23 Apr 2025",
      status: "Rejected",
      priority: "Low",
      requestedBy: "Mike Johnson",
      contact: "<EMAIL>",
      phone: "+91 9876543212",
      purpose: "Crop Monitoring",
      duration: "2 days",
      area: "300 acres"
    },
    {
      id: 4,
      droneId: "PRYMAJ654123",
      droneName: "Arjuna",
      orgName: "AgriTech India",
      location: "Maharashtra",
      date: "22 Apr 2025",
      status: "Pending",
      priority: "High",
      requestedBy: "Sarah Wilson",
      contact: "<EMAIL>",
      phone: "+91 9876543213",
      purpose: "Pest Detection",
      duration: "4 days",
      area: "600 acres"
    },
    {
      id: 5,
      droneId: "PRYMAJ963741",
      droneName: "Arjuna Advance",
      orgName: "SkyFarm Ltd.",
      location: "Gujarat",
      date: "22 Apr 2025",
      status: "Active",
      priority: "Medium",
      requestedBy: "David Brown",
      contact: "<EMAIL>",
      phone: "+91 9876543214",
      purpose: "Irrigation Mapping",
      duration: "6 days",
      area: "800 acres"
    },
    {
      id: 6,
      droneId: "PRYMAJ963369",
      droneName: "Arjuna",
      orgName: "SkyFarm Ltd.",
      location: "Gujarat",
      date: "22 Apr 2025",
      status: "Pending",
      priority: "Low",
      requestedBy: "Lisa Davis",
      contact: "<EMAIL>",
      phone: "+91 9876543215",
      purpose: "Land Survey",
      duration: "3 days",
      area: "400 acres"
    }
  ];

  // Filtering and sorting logic
  const filteredAndSortedRequests = useMemo(() => {
    let filtered = requests.filter(request => {
      const matchesSearch = request.droneName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          request.droneId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          request.orgName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          request.requestedBy.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'All' || request.status === statusFilter;
      const matchesLocation = locationFilter === 'All' || request.location === locationFilter;

      return matchesSearch && matchesStatus && matchesLocation;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'droneName':
          aValue = a.droneName.toLowerCase();
          bValue = b.droneName.toLowerCase();
          break;
        case 'orgName':
          aValue = a.orgName.toLowerCase();
          bValue = b.orgName.toLowerCase();
          break;
        case 'status':
          aValue = a.status.toLowerCase();
          bValue = b.status.toLowerCase();
          break;
        case 'priority':
          const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
          aValue = priorityOrder[a.priority];
          bValue = priorityOrder[b.priority];
          break;
        default:
          aValue = new Date(a.date);
          bValue = new Date(b.date);
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [requests, searchTerm, statusFilter, locationFilter, sortBy, sortOrder]);

  const rowsPerPage = 10;
  const indexOfLast = currentPage * rowsPerPage;
  const indexOfFirst = indexOfLast - rowsPerPage;
  const currentRequests = filteredAndSortedRequests.slice(indexOfFirst, indexOfLast);
  const totalPages = Math.ceil(filteredAndSortedRequests.length / rowsPerPage);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pending': return <FaClock className="text-orange-600" />;
      case 'Approved': return <FaCheckCircle className="text-green-600" />;
      case 'Rejected': return <FaTimesCircle className="text-red-600" />;
      case 'Active': return <FaRocket className="text-blue-600" />;
      default: return <FaExclamationTriangle className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'bg-orange-100 text-orange-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'Active': return 'bg-blue-100 text-blue-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApprove = (request) => {
    setActionRequest(request);
    setShowApproveModal(true);
  };

  const handleReject = (request) => {
    setActionRequest(request);
    setShowRejectModal(true);
  };

  const handleEdit = (request) => {
    setActionRequest(request);
    setShowEditModal(true);
  };

  const confirmApprove = () => {
    console.log('Approving request:', actionRequest.id);
    // Here you would handle the approval logic
    setShowApproveModal(false);
    setActionRequest(null);
  };

  const confirmReject = () => {
    console.log('Rejecting request:', actionRequest.id);
    // Here you would handle the rejection logic
    setShowRejectModal(false);
    setActionRequest(null);
  };

  const confirmEdit = () => {
    console.log('Editing request:', actionRequest.id);
    // Here you would handle the edit logic
    setShowEditModal(false);
    setActionRequest(null);
  };

  const closeModals = () => {
    setShowApproveModal(false);
    setShowRejectModal(false);
    setShowEditModal(false);
    setActionRequest(null);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-blue-600 flex items-center gap-2">
            <FaRocket className="text-blue-600" />
            Deployment Requests ({filteredAndSortedRequests.length})
          </h2>
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
              {filteredAndSortedRequests.filter(r => r.status === 'Pending').length} Pending
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              {filteredAndSortedRequests.filter(r => r.status === 'Approved').length} Approved
            </span>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                Drone Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-44">
                Organization
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                Request Info
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                Status & Priority
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                Purpose & Area
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentRequests.map((request) => (
              <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap w-48">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <FaPlane className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4 min-w-0 flex-1">
                      <div className="text-sm font-medium text-gray-900">{request.droneName}</div>
                      <div className="text-xs text-gray-500">ID: {request.droneId}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap w-44">
                  <div className="text-sm text-gray-900 font-medium">{request.orgName}</div>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <MapPin className="w-3 h-3 flex-shrink-0" />
                    <span>{request.location}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap w-40">
                  <div className="text-sm text-gray-900 font-medium">{request.requestedBy}</div>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3 flex-shrink-0" />
                    <span>{request.date}</span>
                  </div>
                  <div className="text-xs text-gray-400">{request.duration}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap w-36">
                  <div className="space-y-2">
                    <div className="flex items-center gap-1">
                      {getStatusIcon(request.status)}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                        {request.status}
                      </span>
                    </div>
                    <div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(request.priority)}`}>
                        {request.priority}
                      </span>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap w-40">
                  <div className="text-sm text-gray-900 font-medium">{request.purpose}</div>
                  <div className="text-xs text-gray-500">{request.area}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium w-32">
                  <div className="flex items-center justify-end gap-2">
                    <button
                      onClick={() => setSelectedRequest(request)}
                      className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                      title="View Details"
                    >
                      <FaEye size={16} />
                    </button>
                    {request.status === 'Pending' && (
                      <>
                        <button
                          onClick={() => handleApprove(request)}
                          className="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                          title="Approve"
                        >
                          <FaUnlock size={16} />
                        </button>
                        <button
                          onClick={() => handleReject(request)}
                          className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                          title="Reject"
                        >
                          <FaLock size={16} />
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => handleEdit(request)}
                      className="text-gray-600 hover:text-gray-900 p-1 rounded transition-colors"
                      title="Edit"
                    >
                      <FaEdit size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {indexOfFirst + 1} to {Math.min(indexOfLast, filteredAndSortedRequests.length)} of {filteredAndSortedRequests.length} results
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Request Detail Modal */}
      {selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" onClick={() => setSelectedRequest(null)}>
          <div
            className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-100"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="sticky top-0 bg-white p-6 border-b border-gray-200 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FaRocket className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Deployment Request Details</h2>
                    <p className="text-sm text-gray-500">{selectedRequest.droneName} - {selectedRequest.droneId}</p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedRequest(null)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 group"
                  title="Close"
                >
                  <FaTimes size={20} className="text-gray-500 group-hover:text-gray-700" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 text-black">
              {/* Status Badge */}
              <div className="mb-6">
                <div className="flex items-center gap-4">
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(selectedRequest.status)}`}>
                    {selectedRequest.status}
                  </span>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getPriorityColor(selectedRequest.priority)}`}>
                    {selectedRequest.priority} Priority
                  </span>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Request Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FaRocket className="w-5 h-5 text-blue-600" />
                      Request Information
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Purpose</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.purpose}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Duration</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.duration}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Coverage Area</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.area}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Location</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.location}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Building className="w-5 h-5 text-green-600" />
                      Organization & Contact
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Organization</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.orgName}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Requested By</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.requestedBy}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.contact}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Phone</label>
                        <p className="text-gray-900 font-medium">{selectedRequest.phone}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex flex-wrap gap-3 justify-end">
                  {selectedRequest.status === 'Pending' && (
                    <>
                      <button
                        onClick={() => {
                          handleApprove(selectedRequest.id);
                          setSelectedRequest(null);
                        }}
                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <FaUnlock size={16} />
                        Approve Request
                      </button>
                      <button
                        onClick={() => {
                          handleReject(selectedRequest.id);
                          setSelectedRequest(null);
                        }}
                        className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                      >
                        <FaLock size={16} />
                        Reject Request
                      </button>
                    </>
                  )}
                  <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <Edit2 size={16} />
                    Edit Request
                  </button>
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaTimes size={16} />
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Approve Modal */}
      {showApproveModal && actionRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaUnlock className="text-green-600" />
                  Approve Request
                </h3>
                <button
                  onClick={closeModals}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <FaTimes className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaCheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Approve Deployment Request</h4>
                <p className="text-gray-600">
                  Are you sure you want to approve the deployment request for <strong>{actionRequest.droneName}</strong>
                  by <strong>{actionRequest.orgName}</strong>?
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Drone:</p>
                    <p className="font-medium">{actionRequest.droneName}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Organization:</p>
                    <p className="font-medium">{actionRequest.orgName}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Purpose:</p>
                    <p className="font-medium">{actionRequest.purpose}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Priority:</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(actionRequest.priority)}`}>
                      {actionRequest.priority}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={closeModals}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmApprove}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Approve Request
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && actionRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaLock className="text-red-600" />
                  Reject Request
                </h3>
                <button
                  onClick={closeModals}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <FaTimes className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaTimesCircle className="w-8 h-8 text-red-600" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Reject Deployment Request</h4>
                <p className="text-gray-600">
                  Are you sure you want to reject the deployment request for <strong>{actionRequest.droneName}</strong>
                  by <strong>{actionRequest.orgName}</strong>?
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Drone:</p>
                    <p className="font-medium">{actionRequest.droneName}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Organization:</p>
                    <p className="font-medium">{actionRequest.orgName}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Purpose:</p>
                    <p className="font-medium">{actionRequest.purpose}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Priority:</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(actionRequest.priority)}`}>
                      {actionRequest.priority}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for rejection (optional)
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  rows="3"
                  placeholder="Enter reason for rejection..."
                ></textarea>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={closeModals}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmReject}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reject Request
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && actionRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaEdit className="text-blue-600" />
                  Edit Deployment Request
                </h3>
                <button
                  onClick={closeModals}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <FaTimes className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Name
                    </label>
                    <input
                      type="text"
                      defaultValue={actionRequest.droneName}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Organization
                    </label>
                    <input
                      type="text"
                      defaultValue={actionRequest.orgName}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Requested By
                    </label>
                    <input
                      type="text"
                      defaultValue={actionRequest.requestedBy}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date
                    </label>
                    <input
                      type="date"
                      defaultValue={actionRequest.date}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <select
                      defaultValue={actionRequest.priority}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="High">High</option>
                      <option value="Medium">Medium</option>
                      <option value="Low">Low</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration
                    </label>
                    <input
                      type="text"
                      defaultValue={actionRequest.duration}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Purpose
                  </label>
                  <input
                    type="text"
                    defaultValue={actionRequest.purpose}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Coverage Area
                  </label>
                  <input
                    type="text"
                    defaultValue={actionRequest.area}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    defaultValue={actionRequest.location}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </form>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={closeModals}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DroneDeployment;




