import React, { useState, useEffect } from 'react';
import {
  CheckCircle,
  AlertTriangle,
  Info,
  XCircle,
  Clock,
  MapPin,
  User,
  Settings,
  Activity
} from 'lucide-react';

const ActivityFeed = () => {
  const [activities, setActivities] = useState([]);

  // Mock activity data
  const mockActivities = [
    {
      id: 1,
      type: 'success',
      title: 'Drone ARJUNA completed mission',
      description: 'Successfully sprayed 15.2 hectares in Maharashtra',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      icon: <CheckCircle size={20} />,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      id: 2,
      type: 'warning',
      title: 'Low battery alert',
      description: 'Drone TEJAS battery level at 15% - returning to base',
      timestamp: new Date(Date.now() - 12 * 60 * 1000),
      icon: <AlertTriangle size={20} />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      id: 3,
      type: 'info',
      title: 'New pilot assigned',
      description: '<PERSON><PERSON><PERSON> assigned to Drone VIKRANT',
      timestamp: new Date(Date.now() - 25 * 60 * 1000),
      icon: <User size={20} />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      id: 4,
      type: 'error',
      title: 'Connection lost',
      description: 'Lost communication with Drone KARNA in Gujarat',
      timestamp: new Date(Date.now() - 35 * 60 * 1000),
      icon: <XCircle size={20} />,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      id: 5,
      type: 'info',
      title: 'Maintenance scheduled',
      description: 'Routine maintenance for 3 drones scheduled for tomorrow',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      icon: <Settings size={20} />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100'
    },
    {
      id: 6,
      type: 'success',
      title: 'Mission started',
      description: 'Drone ARJUNA ADVANCE started spraying in Haryana',
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      icon: <MapPin size={20} />,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    }
  ];

  useEffect(() => {
    setActivities(mockActivities);

    
    const interval = setInterval(() => {
      const newActivity = {
        id: Date.now(),
        type: ['success', 'warning', 'info'][Math.floor(Math.random() * 3)],
        title: 'System update',
        description: 'Real-time data synchronized',
        timestamp: new Date(),
        icon: <Info size={20} />,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100'
      };

      setActivities(prev => [newActivity, ...prev.slice(0, 5)]);
    }, 30000); // Update every 30 seconds per minute 

    return () => clearInterval(interval);
  }, []);

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 h-full flex flex-col">
      {/* Professional Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Activity className="text-green-600" size={20} />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-800">Recent Activity</h2>
            <p className="text-sm text-gray-500">Live system updates</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-500">Live</span>
        </div>
      </div>

      {/* Professional Activity List */}
      <div className="flex-1 overflow-hidden">
        <div className="activity-feed-content p-4 space-y-3">
          {activities.map((activity, index) => (
            <div
              key={activity.id}
              className={`flex items-start gap-3 p-3 rounded-xl border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-200 ${
                index === 0 ? 'animate-slideInDown' : ''
              }`}
            >
              {/* Professional Icon */}
              <div className={`${activity.bgColor} ${activity.color} p-2 rounded-lg flex-shrink-0`}>
                {activity.icon}
              </div>

              {/* Professional Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-1">
                  <h3 className="font-semibold text-gray-800 text-sm leading-tight">
                    {activity.title}
                  </h3>
                  <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
                    {formatTimestamp(activity.timestamp)}
                  </span>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
                  {activity.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer button - Fixed at bottom */}
      <div className="flex-shrink-0 p-4 border-t border-gray-100">
        <button className="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors py-2">
          View All Activities
        </button>
      </div>
    </div>
  );
};

export default ActivityFeed;
