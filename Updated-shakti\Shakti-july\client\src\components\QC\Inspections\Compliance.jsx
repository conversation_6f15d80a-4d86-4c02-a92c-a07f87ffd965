import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  Shield,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  FileText,
  Scale,
  AlertCircle,
  BookOpen
} from 'lucide-react';

const Compliance = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [complianceRecords, setComplianceRecords] = useState([]);

  // Initialize with sample compliance data
  React.useEffect(() => {
    if (complianceRecords.length === 0) {
      setComplianceRecords([
        {
          id: 'COMP-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          auditor: '<PERSON>',
          date: '2024-01-15',
          time: '10:30',
          location: 'Compliance Office',
          type: 'FAA Compliance',
          status: 'Compliant',
          validUntil: '2024-12-31',
          requirements: {
            registration: { status: 'Pass', details: 'Valid FAA registration number', expiry: '2024-12-31' },
            certification: { status: 'Pass', details: 'Part 107 certified operator', expiry: '2025-06-15' },
            insurance: { status: 'Pass', details: 'Liability coverage active', expiry: '2024-11-30' },
            maintenance: { status: 'Pass', details: 'Maintenance logs up to date', expiry: 'N/A' },
            operations: { status: 'Pass', details: 'Operating within authorized airspace', expiry: 'N/A' }
          },
          violations: [],
          recommendations: ['Renew insurance before November 2024'],
          notes: 'All compliance requirements met. Excellent record keeping.',
          nextAudit: '2024-07-15'
        },
        {
          id: 'COMP-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          auditor: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:15',
          location: 'Field Office',
          type: 'Safety Compliance',
          status: 'Non-Compliant',
          validUntil: '2024-01-21',
          requirements: {
            registration: { status: 'Pass', details: 'Valid registration', expiry: '2024-12-31' },
            certification: { status: 'Warning', details: 'Certification expires soon', expiry: '2024-02-28' },
            insurance: { status: 'Pass', details: 'Coverage adequate', expiry: '2024-10-15' },
            maintenance: { status: 'Fail', details: 'Overdue maintenance items', expiry: 'Overdue' },
            operations: { status: 'Pass', details: 'Proper flight procedures', expiry: 'N/A' }
          },
          violations: ['Overdue propeller inspection', 'Missing maintenance log entries'],
          recommendations: ['Complete overdue maintenance immediately', 'Update maintenance logs', 'Renew certification'],
          notes: 'Immediate action required to restore compliance status.',
          nextAudit: '2024-01-21'
        },
        {
          id: 'COMP-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          auditor: 'Mike Wilson',
          date: '2024-01-13',
          time: '11:45',
          location: 'Compliance Office',
          type: 'Environmental Compliance',
          status: 'Conditional',
          validUntil: '2024-03-13',
          requirements: {
            registration: { status: 'Pass', details: 'Registration current', expiry: '2024-12-31' },
            certification: { status: 'Pass', details: 'Environmental permits valid', expiry: '2024-08-30' },
            insurance: { status: 'Pass', details: 'Environmental liability covered', expiry: '2024-09-15' },
            maintenance: { status: 'Warning', details: 'Some maintenance items due soon', expiry: '2024-02-01' },
            operations: { status: 'Pass', details: 'Operating within environmental guidelines', expiry: 'N/A' }
          },
          violations: [],
          recommendations: ['Schedule maintenance before February 1st', 'Monitor environmental impact metrics'],
          notes: 'Generally compliant with minor maintenance scheduling needed.',
          nextAudit: '2024-04-13'
        }
      ]);
    }
  }, [complianceRecords.length]);

  // Form state for new compliance record
  const [newCompliance, setNewCompliance] = useState({
    droneId: '',
    droneName: '',
    auditor: '',
    date: '',
    time: '',
    location: '',
    type: 'FAA Compliance',
    requirements: {
      registration: { status: 'Pass', details: '', expiry: '' },
      certification: { status: 'Pass', details: '', expiry: '' },
      insurance: { status: 'Pass', details: '', expiry: '' },
      maintenance: { status: 'Pass', details: '', expiry: '' },
      operations: { status: 'Pass', details: '', expiry: '' }
    },
    violations: [],
    recommendations: [],
    notes: ''
  });

  // Add new compliance record function
  const handleAddCompliance = (e) => {
    e.preventDefault();
    
    // Determine overall status based on requirements
    const hasFailures = Object.values(newCompliance.requirements).some(req => req.status === 'Fail');
    const hasWarnings = Object.values(newCompliance.requirements).some(req => req.status === 'Warning');
    
    let overallStatus = 'Compliant';
    if (hasFailures) overallStatus = 'Non-Compliant';
    else if (hasWarnings) overallStatus = 'Conditional';

    const newComp = {
      id: `COMP-2024-${String(complianceRecords.length + 1).padStart(3, '0')}`,
      ...newCompliance,
      status: overallStatus,
      validUntil: new Date(Date.now() + 180*24*60*60*1000).toISOString().split('T')[0],
      nextAudit: new Date(Date.now() + 180*24*60*60*1000).toISOString().split('T')[0]
    };
    
    setComplianceRecords([...complianceRecords, newComp]);
    setNewCompliance({
      droneId: '',
      droneName: '',
      auditor: '',
      date: '',
      time: '',
      location: '',
      type: 'FAA Compliance',
      requirements: {
        registration: { status: 'Pass', details: '', expiry: '' },
        certification: { status: 'Pass', details: '', expiry: '' },
        insurance: { status: 'Pass', details: '', expiry: '' },
        maintenance: { status: 'Pass', details: '', expiry: '' },
        operations: { status: 'Pass', details: '', expiry: '' }
      },
      violations: [],
      recommendations: [],
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete compliance record function
  const handleDeleteCompliance = (id) => {
    setComplianceRecords(complianceRecords.filter(record => record.id !== id));
  };

  // Update compliance status
  const handleUpdateStatus = (id, newStatus) => {
    setComplianceRecords(complianceRecords.map(record => 
      record.id === id ? { ...record, status: newStatus } : record
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Compliant': return 'bg-green-100 text-green-700 border-green-200';
      case 'Non-Compliant': return 'bg-red-100 text-red-700 border-red-200';
      case 'Conditional': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Under Review': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Compliant': return <CheckCircle className="w-4 h-4" />;
      case 'Non-Compliant': return <XCircle className="w-4 h-4" />;
      case 'Conditional': return <AlertTriangle className="w-4 h-4" />;
      case 'Under Review': return <Clock className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getRequirementColor = (status) => {
    switch (status) {
      case 'Pass': return 'text-green-600';
      case 'Fail': return 'text-red-600';
      case 'Warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const filteredRecords = complianceRecords.filter(record => {
    const matchesSearch = record.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.auditor.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || record.status.toLowerCase().replace(/[^a-z]/g, '') === filterStatus.toLowerCase();
    const matchesType = filterType === 'all' || record.type.toLowerCase().includes(filterType.toLowerCase());
    return matchesSearch && matchesStatus && matchesType;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search compliance..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="compliant">Compliant</option>
        <option value="noncompliant">Non-Compliant</option>
        <option value="conditional">Conditional</option>
        <option value="underreview">Under Review</option>
      </select>

      <select
        value={filterType}
        onChange={(e) => setFilterType(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Types</option>
        <option value="faa">FAA Compliance</option>
        <option value="safety">Safety Compliance</option>
        <option value="environmental">Environmental Compliance</option>
        <option value="operational">Operational Compliance</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Audit</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Compliance Management"
      subtitle="Monitor and manage regulatory compliance requirements"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Audits</p>
                <p className="text-2xl font-bold text-gray-900">{filteredRecords.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Shield className="w-3 h-3" />
                  Completed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Shield className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Compliance Records Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Compliance Audit Records</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage regulatory compliance audits</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Audit Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Compliance Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Requirements
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issues & Actions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRecords.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50 transition-colors">
                    {/* Audit Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Shield className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{record.id}</h4>
                            <span className="text-xs text-gray-500">({record.type})</span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{record.droneId} - {record.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {record.auditor}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {record.date} at {record.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {record.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Compliance Status */}
                    <td className="px-6 py-4">
                      <div className="text-center">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(record.status)}`}>
                          {getStatusIcon(record.status)}
                          <span className="ml-2">{record.status}</span>
                        </span>
                        <div className="mt-2 text-xs text-gray-500">
                          Valid until: {record.validUntil}
                        </div>
                        <div className="text-xs text-gray-500">
                          Next audit: {record.nextAudit}
                        </div>
                      </div>
                    </td>

                    {/* Requirements */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <FileText className="w-3 h-3" />
                            Registration
                          </span>
                          <span className={`text-xs font-medium ${getRequirementColor(record.requirements.registration.status)}`}>
                            {record.requirements.registration.status}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <BookOpen className="w-3 h-3" />
                            Certification
                          </span>
                          <span className={`text-xs font-medium ${getRequirementColor(record.requirements.certification.status)}`}>
                            {record.requirements.certification.status}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Shield className="w-3 h-3" />
                            Insurance
                          </span>
                          <span className={`text-xs font-medium ${getRequirementColor(record.requirements.insurance.status)}`}>
                            {record.requirements.insurance.status}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Scale className="w-3 h-3" />
                            Maintenance
                          </span>
                          <span className={`text-xs font-medium ${getRequirementColor(record.requirements.maintenance.status)}`}>
                            {record.requirements.maintenance.status}
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Issues & Actions */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {record.violations.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Violations:</p>
                            <div className="space-y-1">
                              {record.violations.slice(0, 2).map((violation, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertCircle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{violation}</span>
                                </div>
                              ))}
                              {record.violations.length > 2 && (
                                <p className="text-xs text-gray-500">+{record.violations.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {record.recommendations.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Recommendations:</p>
                            <div className="space-y-1">
                              {record.recommendations.slice(0, 2).map((recommendation, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-blue-700">{recommendation}</span>
                                </div>
                              ))}
                              {record.recommendations.length > 2 && (
                                <p className="text-xs text-gray-500">+{record.recommendations.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-inspections/compliance/${record.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={record.status}
                          onChange={(e) => handleUpdateStatus(record.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Compliant">Compliant</option>
                          <option value="Non-Compliant">Non-Compliant</option>
                          <option value="Conditional">Conditional</option>
                          <option value="Under Review">Under Review</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteCompliance(record.id)}
                          title="Delete Record"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default Compliance;
