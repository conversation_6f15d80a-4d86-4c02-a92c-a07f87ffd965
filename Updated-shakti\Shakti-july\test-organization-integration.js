// Test script to verify organization integration
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test data for creating an organization
const testOrganization = {
  name: "Test Integration Org",
  type: "private",
  contact: {
    primaryEmail: "<EMAIL>",
    phone: "+************"
  },
  address: {
    street: "123 Test Street",
    city: "Test City",
    state: "Test State",
    country: "India",
    postalCode: "123456"
  },
  registration: {
    registrationNumber: "TEST-REG-001",
    registrationDate: "2025-01-01"
  },
  primaryContact: {
    name: "Test Contact",
    designation: "Manager",
    email: "<EMAIL>",
    phone: "+************"
  }
};

async function testIntegration() {
  try {
    console.log('🚀 Starting Organization Integration Test...\n');

    // Step 1: Login to get token
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'Admin123!'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful\n');

    // Set up headers with token
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Get current organization stats
    console.log('2. Getting current organization statistics...');
    const statsResponse = await axios.get(`${API_BASE_URL}/organizations/stats`, { headers });
    
    if (statsResponse.data.success) {
      console.log('✅ Current stats:', statsResponse.data.data.stats);
    }
    console.log('');

    // Step 3: Create a new organization
    console.log('3. Creating test organization...');
    const createResponse = await axios.post(`${API_BASE_URL}/organizations`, testOrganization, { headers });
    
    if (createResponse.data.success) {
      console.log('✅ Organization created successfully!');
      console.log('   ID:', createResponse.data.data.organization._id);
      console.log('   Name:', createResponse.data.data.organization.name);
    }
    console.log('');

    // Step 4: Get all organizations
    console.log('4. Fetching all organizations...');
    const allOrgsResponse = await axios.get(`${API_BASE_URL}/organizations`, { headers });
    
    if (allOrgsResponse.data.success) {
      console.log('✅ Organizations fetched successfully!');
      console.log('   Total organizations:', allOrgsResponse.data.data.organizations.length);
      console.log('   Organizations:');
      allOrgsResponse.data.data.organizations.forEach((org, index) => {
        console.log(`   ${index + 1}. ${org.name} (${org.type}) - ${org.status}`);
      });
    }
    console.log('');

    // Step 5: Get updated stats
    console.log('5. Getting updated organization statistics...');
    const updatedStatsResponse = await axios.get(`${API_BASE_URL}/organizations/stats`, { headers });
    
    if (updatedStatsResponse.data.success) {
      console.log('✅ Updated stats:', updatedStatsResponse.data.data.stats);
    }
    console.log('');

    console.log('🎉 Integration test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Backend API is working correctly');
    console.log('- Organization creation is functional');
    console.log('- Statistics are being calculated properly');
    console.log('- Frontend can now integrate with these endpoints');

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testIntegration();
