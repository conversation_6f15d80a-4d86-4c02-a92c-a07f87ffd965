const mongoose = require('mongoose');

// Organization-specific data collection schema
// This collection will contain all organization-related data in one place
const organizationDataSchema = new mongoose.Schema({
  // Organization Reference
  organizationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: [true, 'Organization reference is required'],
    unique: true
  },

  // Organization Basic Info (cached for performance)
  organizationInfo: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    displayName: {
      type: String,
      trim: true
    },
    type: {
      type: String,
      enum: ['government', 'private', 'ngo', 'research', 'military', 'other'],
      required: true
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'pending'],
      default: 'active'
    }
  },

  // Drones Collection - All drones belonging to this organization
  drones: [{
    droneId: {
      type: String,
      required: true,
      unique: true
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    model: {
      type: String,
      required: true,
      trim: true
    },
    manufacturer: {
      type: String,
      required: true,
      trim: true
    },
    serialNumber: {
      type: String,
      required: true,
      unique: true
    },
    registrationNumber: {
      type: String,
      required: true,
      unique: true
    },
    
    // Technical Specifications
    specifications: {
      type: {
        type: String,
        enum: ['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'],
        default: 'quadcopter'
      },
      weight: {
        type: Number,
        min: 0.1,
        max: 25
      },
      maxPayload: {
        type: Number,
        min: 0,
        max: 10
      },
      maxFlightTime: {
        type: Number,
        min: 1,
        max: 300
      },
      maxRange: {
        type: Number,
        min: 0.1,
        max: 100
      },
      maxAltitude: {
        type: Number,
        min: 1,
        max: 500
      },
      maxSpeed: {
        type: Number,
        min: 1,
        max: 200
      },
      batteryCapacity: {
        type: Number,
        min: 100,
        max: 50000
      },
      cameraResolution: String,
      hasGimbal: {
        type: Boolean,
        default: false
      },
      hasGPS: {
        type: Boolean,
        default: true
      },
      hasObstacleAvoidance: {
        type: Boolean,
        default: false
      }
    },

    // Status and Operational Info
    status: {
      type: String,
      enum: ['active', 'inactive', 'maintenance', 'retired', 'lost'],
      default: 'active'
    },
    condition: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor', 'damaged'],
      default: 'excellent'
    },

    // Current Location
    currentLocation: {
      latitude: {
        type: Number,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        min: -180,
        max: 180
      },
      altitude: {
        type: Number,
        min: 0,
        max: 500,
        default: 0
      },
      lastUpdated: {
        type: Date,
        default: Date.now
      }
    },

    // Flight Statistics
    flightStats: {
      totalFlights: {
        type: Number,
        default: 0,
        min: 0
      },
      totalFlightTime: {
        type: Number,
        default: 0,
        min: 0
      },
      totalDistance: {
        type: Number,
        default: 0,
        min: 0
      },
      averageFlightTime: {
        type: Number,
        default: 0,
        min: 0
      },
      lastFlightDate: Date
    },

    // Maintenance Information
    maintenance: {
      lastMaintenanceDate: Date,
      nextMaintenanceDate: Date,
      maintenanceIntervalHours: {
        type: Number,
        default: 50,
        min: 1
      },
      maintenanceHistory: [{
        date: {
          type: Date,
          required: true
        },
        type: {
          type: String,
          enum: ['routine', 'repair', 'upgrade', 'inspection'],
          required: true
        },
        description: {
          type: String,
          required: true,
          maxlength: 500
        },
        cost: {
          type: Number,
          min: 0
        },
        performedBy: {
          type: String,
          required: true
        }
      }]
    },

    // Purchase Information
    purchase: {
      purchaseDate: {
        type: Date,
        required: true
      },
      purchasePrice: {
        type: Number,
        required: true,
        min: 0
      },
      vendor: {
        type: String,
        required: true,
        trim: true
      },
      warrantyExpiryDate: Date
    },

    // Assignment
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },

    // Notes and Documentation
    notes: [{
      content: {
        type: String,
        required: true,
        maxlength: 1000
      },
      addedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      addedAt: {
        type: Date,
        default: Date.now
      },
      type: {
        type: String,
        enum: ['general', 'maintenance', 'incident', 'upgrade'],
        default: 'general'
      }
    }],

    // Timestamps
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],

  // Organization Users
  users: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['admin', 'operator', 'viewer'],
      default: 'operator'
    },
    permissions: [{
      type: String,
      enum: ['view_drones', 'add_drones', 'edit_drones', 'delete_drones', 'view_analytics', 'manage_users']
    }],
    joinedAt: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],

  // Organization Statistics (cached for performance)
  statistics: {
    totalDrones: {
      type: Number,
      default: 0
    },
    activeDrones: {
      type: Number,
      default: 0
    },
    inactiveDrones: {
      type: Number,
      default: 0
    },
    maintenanceDrones: {
      type: Number,
      default: 0
    },
    totalFlightHours: {
      type: Number,
      default: 0
    },
    totalFlights: {
      type: Number,
      default: 0
    },
    totalDistance: {
      type: Number,
      default: 0
    },
    averageFlightTime: {
      type: Number,
      default: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },

  // Organization Settings
  settings: {
    timezone: {
      type: String,
      default: 'UTC'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: true
      }
    },
    mapPreferences: {
      defaultView: {
        type: String,
        enum: ['satellite', 'terrain', 'street'],
        default: 'satellite'
      },
      defaultZoom: {
        type: Number,
        min: 1,
        max: 20,
        default: 10
      }
    }
  }
}, {
  timestamps: true,
  collection: 'organizationData'
});

// Indexes for better performance
organizationDataSchema.index({ organizationId: 1 });
organizationDataSchema.index({ 'drones.droneId': 1 });
organizationDataSchema.index({ 'drones.serialNumber': 1 });
organizationDataSchema.index({ 'drones.registrationNumber': 1 });
organizationDataSchema.index({ 'drones.status': 1 });
organizationDataSchema.index({ 'users.userId': 1 });

// Pre-save middleware to update statistics
organizationDataSchema.pre('save', function(next) {
  if (this.isModified('drones')) {
    this.updateStatistics();
  }
  next();
});

// Instance methods
organizationDataSchema.methods.updateStatistics = function() {
  const drones = this.drones;

  this.statistics.totalDrones = drones.length;
  this.statistics.activeDrones = drones.filter(d => d.status === 'active').length;
  this.statistics.inactiveDrones = drones.filter(d => d.status === 'inactive').length;
  this.statistics.maintenanceDrones = drones.filter(d => d.status === 'maintenance').length;

  this.statistics.totalFlightHours = drones.reduce((sum, d) => sum + (d.flightStats.totalFlightTime || 0), 0);
  this.statistics.totalFlights = drones.reduce((sum, d) => sum + (d.flightStats.totalFlights || 0), 0);
  this.statistics.totalDistance = drones.reduce((sum, d) => sum + (d.flightStats.totalDistance || 0), 0);

  if (this.statistics.totalFlights > 0) {
    this.statistics.averageFlightTime = this.statistics.totalFlightHours / this.statistics.totalFlights;
  }

  this.statistics.lastUpdated = new Date();
};

// Method to add a new drone
organizationDataSchema.methods.addDrone = function(droneData, createdBy) {
  const newDrone = {
    ...droneData,
    droneId: droneData.droneId || `DRN-${Date.now()}`,
    createdBy,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  this.drones.push(newDrone);
  this.updateStatistics();
  return this.save();
};

// Method to update a drone
organizationDataSchema.methods.updateDrone = function(droneId, updateData) {
  const drone = this.drones.id(droneId);
  if (!drone) {
    throw new Error('Drone not found');
  }

  Object.assign(drone, updateData);
  drone.updatedAt = new Date();
  this.updateStatistics();
  return this.save();
};

// Method to remove a drone
organizationDataSchema.methods.removeDrone = function(droneId) {
  this.drones.id(droneId).remove();
  this.updateStatistics();
  return this.save();
};

// Method to get drone by ID
organizationDataSchema.methods.getDrone = function(droneId) {
  return this.drones.id(droneId);
};

// Method to get drones by status
organizationDataSchema.methods.getDronesByStatus = function(status) {
  return this.drones.filter(drone => drone.status === status);
};

// Static method to create organization data
organizationDataSchema.statics.createForOrganization = async function(organizationId, organizationInfo) {
  const existingData = await this.findOne({ organizationId });
  if (existingData) {
    throw new Error('Organization data already exists');
  }

  const orgData = new this({
    organizationId,
    organizationInfo,
    drones: [],
    users: [],
    statistics: {
      totalDrones: 0,
      activeDrones: 0,
      inactiveDrones: 0,
      maintenanceDrones: 0,
      totalFlightHours: 0,
      totalFlights: 0,
      totalDistance: 0,
      averageFlightTime: 0,
      lastUpdated: new Date()
    }
  });

  return orgData.save();
};

module.exports = mongoose.model('OrganizationData', organizationDataSchema);
