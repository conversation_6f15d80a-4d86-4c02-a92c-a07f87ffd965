const mongoose = require('mongoose');

const individualSchema = new mongoose.Schema({
  // Basic Information
  registrationType: {
    type: String,
    enum: ['individual'],
    default: 'individual',
    required: true
  },
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    minlength: [2, 'Full name must be at least 2 characters'],
    maxlength: [100, 'Full name cannot exceed 100 characters']
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: [true, 'Gender is required']
  },
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required'],
    validate: {
      validator: function(value) {
        const today = new Date();
        const age = today.getFullYear() - value.getFullYear();
        return age >= 18 && age <= 100;
      },
      message: 'Age must be between 18 and 100 years'
    }
  },

  // Contact Information
  contact: {
    primaryEmail: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address']
    },
    phone: {
      type: String,
      required: [true, 'Phone number is required'],
      match: [/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number']
    },
    alternativePhone: {
      type: String,
      match: [/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number']
    }
  },

  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Address is required'],
      trim: true,
      maxlength: [200, 'Address cannot exceed 200 characters']
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
      maxlength: [50, 'City cannot exceed 50 characters']
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
      maxlength: [50, 'State cannot exceed 50 characters']
    },
    country: {
      type: String,
      default: 'India',
      trim: true
    },
    postalCode: {
      type: String,
      required: [true, 'Postal code is required'],
      match: [/^\d{6}$/, 'Please enter a valid 6-digit postal code']
    }
  },

  // Identity Documents
  documents: {
    panNumber: {
      type: String,
      required: [true, 'PAN number is required'],
      unique: true,
      uppercase: true,
      match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number']
    },
    aadharNumber: {
      type: String,
      required: [true, 'Aadhar number is required'],
      unique: true,
      match: [/^\d{12}$/, 'Please enter a valid 12-digit Aadhar number']
    },
    idProofPath: {
      type: String,
      required: [true, 'ID proof document is required']
    },
    kycDocumentPath: {
      type: String
    }
  },

  // Registration Status
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'suspended'],
    default: 'pending'
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDate: {
    type: Date
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Drone Allocation
  allocatedDrones: {
    type: Number,
    default: 0,
    min: [0, 'Allocated drones cannot be negative'],
    max: [100, 'Allocated drones cannot exceed 100']
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for age calculation
individualSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual for full address
individualSchema.virtual('fullAddress').get(function() {
  if (!this.address) return '';
  return `${this.address.street}, ${this.address.city}, ${this.address.state} - ${this.address.postalCode}, ${this.address.country}`;
});

// Indexes for better performance
individualSchema.index({ 'contact.primaryEmail': 1 });
individualSchema.index({ 'documents.panNumber': 1 });
individualSchema.index({ 'documents.aadharNumber': 1 });
individualSchema.index({ status: 1 });
individualSchema.index({ createdAt: -1 });

// Pre-save middleware
individualSchema.pre('save', function(next) {
  if (this.isModified('documents.panNumber')) {
    this.documents.panNumber = this.documents.panNumber.toUpperCase();
  }
  next();
});

module.exports = mongoose.model('Individual', individualSchema);
