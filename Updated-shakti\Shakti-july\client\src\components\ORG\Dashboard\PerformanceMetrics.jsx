import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  Zap,
  Award,
  AlertCircle
} from 'lucide-react';

const PerformanceMetrics = () => {
  const [metrics, setMetrics] = useState({
    efficiency: 87,
    uptime: 94,
    accuracy: 96,
    speed: 78,
    fuelSaving: 23,
    coverage: 89,
    satisfaction: 92
  });

  useEffect(() => {
    // Simulate real-time metric updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        efficiency: Math.max(70, Math.min(100, prev.efficiency + (Math.random() - 0.5) * 4)),
        uptime: Math.max(80, Math.min(100, prev.uptime + (Math.random() - 0.5) * 2)),
        accuracy: Math.max(85, Math.min(100, prev.accuracy + (Math.random() - 0.5) * 2)),
        speed: Math.max(60, Math.min(100, prev.speed + (Math.random() - 0.5) * 6)),
        fuelSaving: Math.max(10, Math.min(40, prev.fuelSaving + (Math.random() - 0.5) * 3)),
        coverage: Math.max(70, Math.min(100, prev.coverage + (Math.random() - 0.5) * 4)),
        satisfaction: Math.max(80, Math.min(100, prev.satisfaction + (Math.random() - 0.5) * 2))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const MetricCard = ({ title, value, unit, icon, color, trend, target }) => {
    const percentage = (value / target) * 100;
    const isGood = value >= target * 0.8;

    return (
      <div className="bg-gradient-to-br from-white to-gray-50 p-3 sm:p-4 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300">
        {/* Header - Mobile: Smaller spacing */}
        <div className="flex items-center justify-between mb-2 sm:mb-3">
          <div className={`${color} p-1.5 sm:p-2 rounded-lg`}>
            <div className="w-3 h-3 sm:w-4 sm:h-4">
              {icon}
            </div>
          </div>
          <div className={`flex items-center gap-1 text-xs ${isGood ? 'text-green-600' : 'text-red-600'}`}>
            <div className="w-3 h-3">
              {isGood ? <TrendingUp className="w-full h-full" /> : <TrendingDown className="w-full h-full" />}
            </div>
            <span className="text-xs">{trend}%</span>
          </div>
        </div>

        {/* Value section */}
        <div className="mb-2">
          <h3 className="text-xs sm:text-sm font-semibold text-gray-700 mb-1 leading-tight">{title}</h3>
          <div className="flex items-baseline gap-1">
            <span className="text-xl sm:text-2xl font-bold text-gray-800">
              {Math.round(value)}
            </span>
            <span className="text-xs sm:text-sm text-gray-500">{unit}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2 mb-2">
          <div
            className={`h-1.5 sm:h-2 rounded-full transition-all duration-1000 ${
              isGood ? 'bg-green-500' : 'bg-red-500'
            }`}
            style={{ width: `${Math.min(100, percentage)}%` }}
          ></div>
        </div>

        {/* Footer - Mobile: Smaller text */}
        <div className="flex justify-between text-xs text-gray-500">
          <span className="truncate">Target: {target}{unit}</span>
          <span className="ml-2">{Math.round(percentage)}%</span>
        </div>
      </div>
    );
  };

  const performanceData = [
    {
      title: 'Fleet Efficiency',
      value: metrics.efficiency,
      unit: '%',
      icon: <Zap size={16} className="text-white" />,
      color: 'bg-blue-500',
      trend: '****',
      target: 85
    },
    {
      title: 'System Uptime',
      value: metrics.uptime,
      unit: '%',
      icon: <Clock size={16} className="text-white" />,
      color: 'bg-green-500',
      trend: '****',
      target: 90
    },
    {
      title: 'Mission Accuracy',
      value: metrics.accuracy,
      unit: '%',
      icon: <Target size={16} className="text-white" />,
      color: 'bg-purple-500',
      trend: '+0.5',
      target: 95
    },
    {
      title: 'Avg Speed',
      value: metrics.speed,
      unit: 'km/h',
      icon: <TrendingUp size={16} className="text-white" />,
      color: 'bg-orange-500',
      trend: '-1.2',
      target: 80
    },
    {
      title: 'Fuel Savings',
      value: metrics.fuelSaving,
      unit: '%',
      icon: <Award size={16} className="text-white" />,
      color: 'bg-teal-500',
      trend: '****',
      target: 20
    },
    {
      title: 'Area Coverage',
      value: metrics.coverage,
      unit: '%',
      icon: <Target size={16} className="text-white" />,
      color: 'bg-indigo-500',
      trend: '****',
      target: 85
    }
  ];

  const overallScore = Math.round(
    (metrics.efficiency + metrics.uptime + metrics.accuracy + metrics.coverage) / 4
  );

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
      {/* Header - Mobile responsive */}
      <div className="p-3 sm:p-4 lg:p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-3 sm:mb-4 gap-2 sm:gap-0">
          <h2 className="text-lg sm:text-xl font-bold text-gray-800">Performance Metrics</h2>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-xs sm:text-sm text-gray-500">Real-time</span>
          </div>
        </div>

        {/* Overall Performance Score - Mobile responsive */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 sm:p-4 rounded-xl">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
            <div className="flex-1">
              <h3 className="text-xs sm:text-sm font-semibold text-gray-700 mb-1 sm:mb-2">Overall Performance</h3>
              <div className="flex items-center gap-2">
                <span className="text-2xl sm:text-3xl font-bold text-blue-600">{overallScore}</span>
                <span className="text-xs sm:text-sm text-gray-500">/ 100</span>
                <div className="w-4 h-4 sm:w-5 sm:h-5">
                  {overallScore >= 85 ? (
                    <Award className="text-yellow-500 w-full h-full" />
                  ) : overallScore >= 70 ? (
                    <TrendingUp className="text-green-500 w-full h-full" />
                  ) : (
                    <AlertCircle className="text-red-500 w-full h-full" />
                  )}
                </div>
              </div>
            </div>
            <div className="text-left sm:text-right">
              <div className={`text-xs sm:text-sm font-medium ${
                overallScore >= 85 ? 'text-green-600' :
                overallScore >= 70 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {overallScore >= 85 ? 'Excellent' :
                 overallScore >= 70 ? 'Good' : 'Needs Improvement'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Metrics Grid - Mobile responsive with auto height */}
      <div className="p-3 sm:p-4 lg:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-3 sm:gap-4">
          {performanceData.map((metric, index) => (
            <MetricCard key={index} {...metric} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetrics;
