/* Reset and Base Styles */
* {
  /* margin: 0;
  padding: 0;
  box-sizing: border-box; */
  font-family: 'Segoe UI', sans-serif;
}

/* body {
  height: 100vh;
  background: linear-gradient(135deg, #f2f5f7, rgb(119, 180, 238));
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
} */

.login-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #f2f5f7, rgb(119, 180, 238));
  overflow: hidden;
}

/* Main Container */
.container {
  max-width: 400px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  text-align: center;
  animation: fadeIn 1s ease-in;
  position: relative;
  cursor: none;
}

.container button {
  cursor: none;
}

/* Custom Cursor */
.custom-cursor {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #1f3b64;
  border-radius: 50%;
  position: absolute;
  pointer-events: none;
  transition: transform 0.15s ease, background 0.2s ease;
  z-index: 1000;
  display: none;
}

/* Logo */
  .logo {
  width: 120px;
  height: auto;
  margin: 0 auto 10px;
  display: block;
  object-fit: contain;
}



/* Title & Subtitle */
.title {
  font-size: 28px;
  color: #1f3b64;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 10px;
  text-shadow: 0 0 5px #ccc;
}

.subtitle {
  font-size: 14px;
  color: #6e7b8a;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* Buttons Section */
.buttons button {
  width: 100%;
  padding: 14px 0;
  margin: 10px 0;
  font-size: 17px;
  color: white;
  background: #1f3b64;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(31, 59, 100, 0.3);
  transition: all 0.3s ease;
}

.buttons button:hover {
  background: #162d4a;
  transform: scale(1.05);
  box-shadow: 0 6px 18px rgba(22, 45, 74, 0.5);
}

/* Auth Form */
.auth-form {
  margin-top: 20px;
  animation: fadeIn 0.4s ease;
}

.auth-form h2 {
  color: #1f3b64;
  margin-bottom: 15px;
}

.auth-form input {
  width: 100%;
  padding: 12px;
  margin: 8px 0;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 15px;
  color: black;
}

.auth-form button {
  width: 100%;
  padding: 12px 0;
  margin-top: 10px;
  background: #1f3b64;
  color: white;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: none;
}

.auth-form button:hover {
  background: #162d4a;
}

/* Sign-in/Sign-up Toggle Link */
.toggle-link {
  font-size: 13px;
  color: #6e7b8a;
  margin-top: 10px;
}

.toggle-link span {
  color: #1f3b64;
  cursor: pointer;
  text-decoration: underline;
}

/* Forgot Password */
.forgot-password {
  font-size: 13px;
  color: #1f3b64;
  text-align: right;
  margin: 5px 0 10px;
  cursor: pointer;
  text-decoration: underline;
}

.forgot-password:hover {
  color: #162d4a;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}