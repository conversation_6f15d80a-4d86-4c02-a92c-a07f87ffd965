/**
 * Standardized API response utility functions
 */

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {Object} data - Response data
 * @param {String} message - Success message
 * @param {Number} statusCode - HTTP status code (default: 200)
 */
const sendSuccess = (res, data = null, message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
};

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {String} message - Error message
 * @param {Number} statusCode - HTTP status code (default: 500)
 * @param {Object} errors - Detailed error information
 */
const sendError = (res, message = 'Internal Server Error', statusCode = 500, errors = null) => {
  const response = {
    success: false,
    message,
    errors,
    timestamp: new Date().toISOString()
  };

  // Don't send stack trace in production
  if (process.env.NODE_ENV === 'development' && errors && errors.stack) {
    response.stack = errors.stack;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send validation error response
 * @param {Object} res - Express response object
 * @param {Array} validationErrors - Array of validation errors
 */
const sendValidationError = (res, validationErrors) => {
  const errors = validationErrors.map(error => ({
    field: error.path || error.param,
    message: error.msg || error.message,
    value: error.value
  }));

  return sendError(res, 'Validation failed', 400, errors);
};

/**
 * Send authentication error response
 * @param {Object} res - Express response object
 * @param {String} message - Error message
 */
const sendAuthError = (res, message = 'Authentication failed') => {
  return sendError(res, message, 401);
};

/**
 * Send authorization error response
 * @param {Object} res - Express response object
 * @param {String} message - Error message
 */
const sendForbiddenError = (res, message = 'Access forbidden') => {
  return sendError(res, message, 403);
};

/**
 * Send not found error response
 * @param {Object} res - Express response object
 * @param {String} message - Error message
 */
const sendNotFoundError = (res, message = 'Resource not found') => {
  return sendError(res, message, 404);
};

module.exports = {
  sendSuccess,
  sendError,
  sendValidationError,
  sendAuthError,
  sendForbiddenError,
  sendNotFoundError
};
