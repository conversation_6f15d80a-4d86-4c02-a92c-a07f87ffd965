const mongoose = require('mongoose');
const { getOrgConnection } = require('../config/organizationDatabase');

// Organization Drone Schema - Simplified for organization portal
const orgDroneSchema = new mongoose.Schema({
  // Basic Information
  droneId: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: [true, 'Drone name is required'],
    trim: true,
    maxlength: [100, 'Drone name cannot exceed 100 characters']
  },
  model: {
    type: String,
    required: [true, 'Model is required'],
    trim: true,
    maxlength: [100, 'Model cannot exceed 100 characters']
  },
  manufacturer: {
    type: String,
    required: [true, 'Manufacturer is required'],
    trim: true,
    maxlength: [100, 'Manufacturer cannot exceed 100 characters']
  },
  serialNumber: {
    type: String,
    required: [true, 'Serial number is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Serial number cannot exceed 50 characters']
  },
  registrationNumber: {
    type: String,
    required: [true, 'Registration number is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Registration number cannot exceed 50 characters']
  },

  // Organization Reference
  organizationId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Organization ID is required'],
    index: true
  },
  organizationName: {
    type: String,
    required: true,
    trim: true
  },

  // Drone Type and Status
  droneType: {
    type: String,
    enum: ['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'agriculture', 'surveillance', 'delivery', 'racing', 'other'],
    default: 'quadcopter'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'retired', 'lost'],
    default: 'active'
  },
  condition: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'damaged'],
    default: 'excellent'
  },

  // Technical Specifications
  specifications: {
    weight: {
      type: Number,
      min: [0.1, 'Weight must be at least 0.1 kg'],
      max: [25, 'Weight cannot exceed 25 kg']
    },
    maxPayload: {
      type: Number,
      min: [0, 'Max payload cannot be negative'],
      max: [10, 'Max payload cannot exceed 10 kg']
    },
    maxFlightTime: {
      type: Number,
      min: [1, 'Max flight time must be at least 1 minute'],
      max: [300, 'Max flight time cannot exceed 300 minutes']
    },
    maxRange: {
      type: Number,
      min: [0.1, 'Max range must be at least 0.1 km'],
      max: [100, 'Max range cannot exceed 100 km']
    },
    maxAltitude: {
      type: Number,
      min: [1, 'Max altitude must be at least 1 meter'],
      max: [500, 'Max altitude cannot exceed 500 meters']
    },
    maxSpeed: {
      type: Number,
      min: [1, 'Max speed must be at least 1 km/h'],
      max: [200, 'Max speed cannot exceed 200 km/h']
    },
    batteryCapacity: {
      type: Number,
      min: [100, 'Battery capacity must be at least 100 mAh'],
      max: [50000, 'Battery capacity cannot exceed 50000 mAh']
    },
    cameraResolution: {
      type: String,
      trim: true,
      maxlength: [50, 'Camera resolution cannot exceed 50 characters']
    },
    hasGimbal: {
      type: Boolean,
      default: false
    },
    hasGPS: {
      type: Boolean,
      default: true
    },
    hasObstacleAvoidance: {
      type: Boolean,
      default: false
    }
  },

  // Purchase Information
  purchase: {
    purchaseDate: {
      type: Date,
      required: [true, 'Purchase date is required']
    },
    purchasePrice: {
      type: Number,
      required: [true, 'Purchase price is required'],
      min: [0, 'Purchase price cannot be negative']
    },
    vendor: {
      type: String,
      required: [true, 'Vendor is required'],
      trim: true,
      maxlength: [100, 'Vendor name cannot exceed 100 characters']
    },
    warrantyExpiryDate: {
      type: Date
    }
  },

  // Current Location
  currentLocation: {
    latitude: {
      type: Number,
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90'],
      default: 0
    },
    longitude: {
      type: Number,
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180'],
      default: 0
    },
    altitude: {
      type: Number,
      min: [0, 'Altitude cannot be negative'],
      max: [500, 'Altitude cannot exceed 500 meters'],
      default: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },

  // Flight Statistics
  flightStats: {
    totalFlights: {
      type: Number,
      default: 0,
      min: [0, 'Total flights cannot be negative']
    },
    totalFlightTime: {
      type: Number,
      default: 0,
      min: [0, 'Total flight time cannot be negative']
    },
    totalDistance: {
      type: Number,
      default: 0,
      min: [0, 'Total distance cannot be negative']
    },
    averageFlightTime: {
      type: Number,
      default: 0,
      min: [0, 'Average flight time cannot be negative']
    },
    lastFlightDate: {
      type: Date
    }
  },

  // Maintenance Information
  maintenance: {
    lastMaintenanceDate: {
      type: Date
    },
    nextMaintenanceDate: {
      type: Date
    },
    maintenanceIntervalHours: {
      type: Number,
      default: 50,
      min: [1, 'Maintenance interval must be at least 1 hour']
    }
  },

  // Assignment
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    default: null
  },
  assignedToName: {
    type: String,
    trim: true
  },

  // Notes
  notes: [{
    content: {
      type: String,
      required: true,
      maxlength: [1000, 'Note content cannot exceed 1000 characters']
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    addedByName: {
      type: String,
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['general', 'maintenance', 'incident', 'upgrade'],
      default: 'general'
    }
  }],

  // Audit Fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  createdByName: {
    type: String,
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId
  },
  updatedByName: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'orgDrones'
});

// Indexes for better performance
orgDroneSchema.index({ organizationId: 1 });
orgDroneSchema.index({ droneId: 1 });
orgDroneSchema.index({ serialNumber: 1 });
orgDroneSchema.index({ registrationNumber: 1 });
orgDroneSchema.index({ status: 1 });
orgDroneSchema.index({ organizationId: 1, status: 1 });
orgDroneSchema.index({ createdAt: -1 });

// Static method to generate unique drone ID
orgDroneSchema.statics.generateDroneId = function() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `DRN-${new Date().getFullYear()}-${random}-${timestamp.toString().slice(-6)}`;
};

// Instance method to update flight stats
orgDroneSchema.methods.updateFlightStats = function(flightData) {
  this.flightStats.totalFlights += 1;
  this.flightStats.totalFlightTime += flightData.flightTime || 0;
  this.flightStats.totalDistance += flightData.distance || 0;
  
  if (this.flightStats.totalFlights > 0) {
    this.flightStats.averageFlightTime = this.flightStats.totalFlightTime / this.flightStats.totalFlights;
  }
  
  this.flightStats.lastFlightDate = new Date();
  return this.save();
};

// Create model using organization database connection
let OrgDrone;
try {
  const orgConnection = getOrgConnection();
  OrgDrone = orgConnection.model('OrgDrone', orgDroneSchema);
} catch (error) {
  // Fallback for when connection is not ready
  OrgDrone = null;
}

// Export a function that returns the model
module.exports = () => {
  if (!OrgDrone) {
    const orgConnection = getOrgConnection();
    OrgDrone = orgConnection.model('OrgDrone', orgDroneSchema);
  }
  return OrgDrone;
};
