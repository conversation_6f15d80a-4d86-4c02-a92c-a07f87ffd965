/* Enhanced Admin Map Layout Styles */

/* Ensure proper flexbox behavior */
.admin-map-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.admin-map-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.admin-map-content {
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* Sidebar scrolling */
.entity-list-container {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #F7FAFC;
}

.entity-list-container::-webkit-scrollbar {
  width: 6px;
}

.entity-list-container::-webkit-scrollbar-track {
  background: #F7FAFC;
  border-radius: 3px;
}

.entity-list-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #CBD5E0, #A0AEC0);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.entity-list-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #A0AEC0, #718096);
}

/* Map container enhancements */
.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  height: 100% !important;
  width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .admin-map-content {
    flex-direction: column;
  }
  
  .entity-sidebar {
    height: 33.333333% !important;
    width: 100% !important;
  }
  
  .map-container {
    height: 66.666667% !important;
  }
}

@media (min-width: 1025px) {
  .admin-map-content {
    flex-direction: row;
  }
  
  .entity-sidebar {
    width: 320px !important;
    height: 100% !important;
  }
  
  .map-container {
    flex: 1 !important;
    height: 100% !important;
  }
}

/* Stats cards responsive grid */
.stats-grid {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 0.75rem;
  }
}

/* Entity card hover effects */
.entity-card {
  transition: all 0.2s ease-in-out;
}

.entity-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Ensure proper spacing */
.admin-header {
  flex-shrink: 0;
}

.admin-stats {
  flex-shrink: 0;
}

.admin-map-wrapper {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}
