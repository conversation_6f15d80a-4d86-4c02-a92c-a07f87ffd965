import React, { useState } from 'react';

const QCAddForm = () => {
  const [formData, setFormData] = useState({
    droneName: '',
    droneType: '',
    droneId: '',
    manufacturer: '',
    droneStatus: '',
    deploymentDate: '',
    maxWeight: '',
    payloadCapacity: '',
    spraySystemType: '',
    numberOfNozzles: '',
    controllerType: '',
    controllerSerial: '',
    proportionType: '',
    flightControllerSerial: '',
    propCW1: '',
    motorCW1: '',
    propCW2: '',
    motorCW2: '',
    propCCW1: '',
    motorCCW1: '',
    propCCW2: '',
    motorCCW2: '',
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form Submitted:", formData);
  };


const navigate = useNavigate();
  const NavItem = ({ icon, label, to }) => {


  return (
    <div
      className="flex items-center gap-3 px-2 py-2 hover:bg-[#f0f8ff] rounded-md cursor-pointer transition"
      onClick={() => navigate(to)}
    >
      {icon}
      <span className="font-medium text-[#1e2c5f]">{label}</span>
    </div>
  );
};




  return (
    <div className="min-h-screen bg-gray-50 py-10 px-6">
        {/* Sidebar */}
      <div className="w-full lg:w-[18em] bg-white shadow-lg flex flex-col justify-between py-6 px-4">
        <div>
          <h1 className="text-[40px] font-bold text-justify text-[#1e2c5f] mb-6 px-2 underline">
            S.H.A.K.T.I
          </h1>
          <nav className="space-y-2">
            <NavItem icon={<LayoutDashboard color={'black'} size={20} />} label="Dashboard" to="/admindashboard" />
            <NavItem icon={<MapPinned color={'black'} size={20} />} label="Map" to="/map" />
            <NavItem icon={<Building color={'black'} size={20} />} label="Organizations" to="/organizationpage" />
            <NavItem icon={<Boxes color={'black'} size={20} />} label="Inventory" to="/inventory" />
            <NavItem icon={<Truck color={'black'} size={20} />} label="Deployment" to="/admindeployment" />
            <NavItem icon={<Bot color={'black'} size={20} />} label="Drones" to="/dronepage" />
            <NavItem icon={<Bell color={'black'} size={20} />} label="Notifications" to="/adminnotification" />
          </nav>
        </div>
        <div className="px-2 mt-4">
          <img src={logo} alt="Logo" className="w-28 mb-3" />

          <button className="w-full flex items-center justify-between text-[#ffffff] font-semibold px-4 py-2 rounded-md bg-white text-black border-2"
            onClick={() => navigate('/')}>
            Logout <LogOut size={18} />
          </button>
        </div>
      </div>





      <form
        onSubmit={handleSubmit}
        className="max-w-6xl mx-auto bg-white p-6 rounded-md shadow-md"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium">Drone Name</label>
            <input
              name="droneName"
              value={formData.droneName}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1"
              type="text"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Drone Type</label>
            <input
              name="droneType"
              value={formData.droneType}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1"
              type="text"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Drone ID</label>
            <input
              name="droneId"
              value={formData.droneId}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1"
              type="text"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Manufacturer</label>
            <input
              name="manufacturer"
              value={formData.manufacturer}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1 font-bold"
              type="text"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Drone Status</label>
            <input
              name="droneStatus"
              value={formData.droneStatus}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1 text-green-600 font-medium"
              type="text"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Deployment Date</label>
            <input
              name="deploymentDate"
              value={formData.deploymentDate}
              onChange={handleChange}
              className="w-full border rounded px-3 py-1"
              type="text"
            />
          </div>
        </div>

        <div className="mt-6">
          <h2 className="text-md font-semibold mb-4">DRONE SPECIFICATIONS</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <InputField label="Max Take-off Weight (kg)" name="maxWeight" value={formData.maxWeight} onChange={handleChange} />
            <InputField label="Payload Capacity (kg)" name="payloadCapacity" value={formData.payloadCapacity} onChange={handleChange} />
            <InputField label="Spray System Type" name="spraySystemType" value={formData.spraySystemType} onChange={handleChange} />
            <InputField label="Number of nozzles" name="numberOfNozzles" value={formData.numberOfNozzles} onChange={handleChange} />
            <InputField label="Remote Controller Type" name="controllerType" value={formData.controllerType} onChange={handleChange} />
            <InputField label="Remote Controller Serial No" name="controllerSerial" value={formData.controllerSerial} onChange={handleChange} />
            <InputField label="Proportion Type" name="proportionType" value={formData.proportionType} onChange={handleChange} />
            <InputField label="Flight Controller Serial No" name="flightControllerSerial" value={formData.flightControllerSerial} onChange={handleChange} />
            <InputField label="Propellar CW-1" name="propCW1" value={formData.propCW1} onChange={handleChange} />
            <InputField label="Motor CW-1" name="motorCW1" value={formData.motorCW1} onChange={handleChange} />
            <InputField label="Propellar CW-2" name="propCW2" value={formData.propCW2} onChange={handleChange} />
            <InputField label="Motor CW-2" name="motorCW2" value={formData.motorCW2} onChange={handleChange} />
            <InputField label="Propellar CCW-1" name="propCCW1" value={formData.propCCW1} onChange={handleChange} />
            <InputField label="Motor CCW-1" name="motorCCW1" value={formData.motorCCW1} onChange={handleChange} />
            <InputField label="Propellar CCW-2" name="propCCW2" value={formData.propCCW2} onChange={handleChange} />
            <InputField label="Motor CCW-2" name="motorCCW2" value={formData.motorCCW2} onChange={handleChange} />
          </div>
        </div>

        <div className="mt-6 text-center">
          <button type="submit" className="px-6 py-2 bg-black text-white rounded hover:bg-gray-800">
            SUBMIT
          </button>
        </div>
      </form>
    </div>
  );
};

const InputField = ({ label, name, value, onChange }) => (
  <div>
    <label className="text-sm font-medium">{label}</label>
    <input
      name={name}
      value={value}
      onChange={onChange}
      className="w-full border rounded px-3 py-1"
      type="text"
    />
  </div>
);

export default QCAddForm;
