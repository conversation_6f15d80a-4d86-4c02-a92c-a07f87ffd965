import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  FileText,
  Scale,
  AlertCircle,
  BookOpen,
  Edit,
  Download,
  Printer,
  Award,
  ExternalLink
} from 'lucide-react';

const ComplianceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [compliance, setCompliance] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample compliance data
  useEffect(() => {
    setTimeout(() => {
      const sampleCompliance = {
        id: id || 'COMP-2024-001',
        droneId: 'DRN-001',
        droneName: 'Surveyor Alpha',
        auditor: '<PERSON>',
        date: '2024-01-15',
        time: '10:30',
        location: 'Compliance Office',
        type: 'FAA Compliance',
        status: 'Compliant',
        validUntil: '2024-12-31',
        requirements: {
          registration: { 
            status: 'Pass', 
            details: 'Valid FAA registration number: FA3ABC123DEF456', 
            expiry: '2024-12-31',
            certificate: 'REG-2024-001.pdf',
            issueDate: '2023-01-15',
            authority: 'Federal Aviation Administration'
          },
          certification: { 
            status: 'Pass', 
            details: 'Part 107 certified operator with current license', 
            expiry: '2025-06-15',
            certificate: 'CERT-107-2023.pdf',
            issueDate: '2023-06-15',
            authority: 'FAA Part 107'
          },
          insurance: { 
            status: 'Pass', 
            details: 'Comprehensive liability coverage active', 
            expiry: '2024-11-30',
            certificate: 'INS-2024-DRONE.pdf',
            coverage: '$2,000,000',
            provider: 'AeroInsure Corp'
          },
          maintenance: { 
            status: 'Pass', 
            details: 'Maintenance logs up to date with scheduled inspections', 
            expiry: 'N/A',
            lastInspection: '2024-01-10',
            nextDue: '2024-04-10',
            technician: 'Mike Wilson'
          },
          operations: { 
            status: 'Pass', 
            details: 'Operating within authorized airspace and regulations', 
            expiry: 'N/A',
            airspaceAuth: 'LAANC-2024-001',
            flightLimits: '400ft AGL',
            restrictions: 'Daylight operations only'
          }
        },
        violations: [],
        recommendations: [
          'Renew insurance before November 2024',
          'Schedule Part 107 recertification training',
          'Update emergency procedures documentation',
          'Review airspace authorization requirements quarterly'
        ],
        notes: 'All compliance requirements met. Excellent record keeping and documentation. Drone operations are fully compliant with current FAA regulations.',
        nextAudit: '2024-07-15',
        complianceHistory: [
          { date: '2023-07-15', status: 'Compliant', auditor: 'Sarah Johnson' },
          { date: '2023-01-15', status: 'Compliant', auditor: 'Mike Wilson' },
          { date: '2022-07-15', status: 'Conditional', auditor: 'John Smith' }
        ],
        documents: [
          { name: 'FAA Registration Certificate', file: 'REG-2024-001.pdf', date: '2024-01-15' },
          { name: 'Part 107 License', file: 'CERT-107-2023.pdf', date: '2023-06-15' },
          { name: 'Insurance Certificate', file: 'INS-2024-DRONE.pdf', date: '2024-01-01' },
          { name: 'Maintenance Log', file: 'MAINT-LOG-2024.pdf', date: '2024-01-10' },
          { name: 'Operations Manual', file: 'OPS-MANUAL-v2.pdf', date: '2023-12-01' }
        ],
        riskAssessment: {
          overall: 'Low',
          operational: 'Low',
          regulatory: 'Low',
          financial: 'Medium',
          safety: 'Low'
        }
      };
      setCompliance(sampleCompliance);
      setLoading(false);
    }, 500);
  }, [id]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pass':
      case 'Compliant': return 'bg-green-100 text-green-700 border-green-200';
      case 'Fail':
      case 'Non-Compliant': return 'bg-red-100 text-red-700 border-red-200';
      case 'Warning':
      case 'Conditional': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Under Review': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pass':
      case 'Compliant': return <CheckCircle className="w-4 h-4" />;
      case 'Fail':
      case 'Non-Compliant': return <XCircle className="w-4 h-4" />;
      case 'Warning':
      case 'Conditional': return <AlertTriangle className="w-4 h-4" />;
      case 'Under Review': return <Clock className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getRiskColor = (risk) => {
    switch (risk.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      <button
        onClick={() => navigate('/qc-inspections/compliance')}
        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to List
      </button>
      <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
        <Edit className="w-4 h-4" />
        Edit
      </button>
      <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
        <Download className="w-4 h-4" />
        Export
      </button>
      <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
        <Printer className="w-4 h-4" />
        Print
      </button>
    </div>
  );

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!compliance) {
    return (
      <QCLayout title="Compliance Record Not Found" subtitle="The requested compliance record could not be found">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Compliance record not found</p>
          <button
            onClick={() => navigate('/qc-inspections/compliance')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Compliance
          </button>
        </div>
      </QCLayout>
    );
  }

  return (
    <QCLayout
      title={`Compliance Audit - ${compliance.id}`}
      subtitle={`${compliance.droneName} (${compliance.droneId}) - ${compliance.type}`}
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Compliance Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Shield className="w-8 h-8 text-blue-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{compliance.id}</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Auditor:</span>
                    <span className="font-medium">{compliance.auditor}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{compliance.date} at {compliance.time}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{compliance.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{compliance.type}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(compliance.status)}`}>
                {getStatusIcon(compliance.status)}
                <span className="ml-2">{compliance.status}</span>
              </span>
              <p className="text-sm text-gray-500 mt-2">Valid until: {compliance.validUntil}</p>
              <p className="text-sm text-gray-500">Next audit: {compliance.nextAudit}</p>
            </div>
          </div>
        </div>

        {/* Risk Assessment */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Assessment</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskColor(compliance.riskAssessment.overall)}`}>
                {compliance.riskAssessment.overall}
              </p>
              <p className="text-sm text-gray-600 mt-1">Overall Risk</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskColor(compliance.riskAssessment.operational)}`}>
                {compliance.riskAssessment.operational}
              </p>
              <p className="text-sm text-gray-600 mt-1">Operational</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskColor(compliance.riskAssessment.regulatory)}`}>
                {compliance.riskAssessment.regulatory}
              </p>
              <p className="text-sm text-gray-600 mt-1">Regulatory</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskColor(compliance.riskAssessment.financial)}`}>
                {compliance.riskAssessment.financial}
              </p>
              <p className="text-sm text-gray-600 mt-1">Financial</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskColor(compliance.riskAssessment.safety)}`}>
                {compliance.riskAssessment.safety}
              </p>
              <p className="text-sm text-gray-600 mt-1">Safety</p>
            </div>
          </div>
        </div>

        {/* Compliance Requirements */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Registration */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <FileText className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Registration</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(compliance.requirements.registration.status)}`}>
                  {getStatusIcon(compliance.requirements.registration.status)}
                  <span className="ml-1">{compliance.requirements.registration.status}</span>
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{compliance.requirements.registration.details}</p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Authority:</span>
                <span className="font-medium">{compliance.requirements.registration.authority}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Issue Date:</span>
                <span className="font-medium">{compliance.requirements.registration.issueDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expiry:</span>
                <span className="font-medium">{compliance.requirements.registration.expiry}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Certificate:</span>
                <button className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  <span className="font-medium">{compliance.requirements.registration.certificate}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>

          {/* Certification */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <BookOpen className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Certification</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(compliance.requirements.certification.status)}`}>
                  {getStatusIcon(compliance.requirements.certification.status)}
                  <span className="ml-1">{compliance.requirements.certification.status}</span>
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{compliance.requirements.certification.details}</p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Authority:</span>
                <span className="font-medium">{compliance.requirements.certification.authority}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Issue Date:</span>
                <span className="font-medium">{compliance.requirements.certification.issueDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expiry:</span>
                <span className="font-medium">{compliance.requirements.certification.expiry}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Certificate:</span>
                <button className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  <span className="font-medium">{compliance.requirements.certification.certificate}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>

          {/* Insurance */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Shield className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Insurance</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(compliance.requirements.insurance.status)}`}>
                  {getStatusIcon(compliance.requirements.insurance.status)}
                  <span className="ml-1">{compliance.requirements.insurance.status}</span>
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{compliance.requirements.insurance.details}</p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Provider:</span>
                <span className="font-medium">{compliance.requirements.insurance.provider}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Coverage:</span>
                <span className="font-medium">{compliance.requirements.insurance.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Expiry:</span>
                <span className="font-medium">{compliance.requirements.insurance.expiry}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Certificate:</span>
                <button className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  <span className="font-medium">{compliance.requirements.insurance.certificate}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>

          {/* Maintenance */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Scale className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Maintenance</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(compliance.requirements.maintenance.status)}`}>
                  {getStatusIcon(compliance.requirements.maintenance.status)}
                  <span className="ml-1">{compliance.requirements.maintenance.status}</span>
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{compliance.requirements.maintenance.details}</p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Technician:</span>
                <span className="font-medium">{compliance.requirements.maintenance.technician}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Inspection:</span>
                <span className="font-medium">{compliance.requirements.maintenance.lastInspection}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Next Due:</span>
                <span className="font-medium">{compliance.requirements.maintenance.nextDue}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default ComplianceDetail;
