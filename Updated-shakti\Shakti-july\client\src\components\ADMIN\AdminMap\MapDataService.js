import api from '../../../services/api';
import organizationService from '../../../services/organizationService';
import individualService from '../../../services/individualService';

// Map Data Service - Manages organization and drone data for map components with backend integration
class MapDataService {
  constructor() {
    this.subscribers = new Map();
    this.cache = {
      organizations: [],
      individuals: [],
      dronesByOrg: {},
      dronesByIndividual: {},
      lastFetch: {
        organizations: null,
        individuals: null
      }
    };
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache

    // Fallback mock data for development
    this.mockData = {
      organizations: [
        {
          id: 'ORG-001',
          name: "<PERSON><PERSON>",
          orgId: "ORG ID:-0002501",
          state: "Maharashtra",
          district: "Aurangabad",
          totalDrones: 10,
          activeDrones: 7,
          position: [19.848, 75.883],
          status: 'active',
          registrationDate: '2023-01-15',
          contactPerson: '<PERSON><PERSON>',
          phone: '+91-9876543210',
          email: '<EMAIL>'
        },
        {
          id: 'ORG-002',
          name: "Aero Pvt. Ltd.",
          orgId: "ORG ID:-0002502",
          state: "Maharashtra",
          district: "Pune",
          totalDrones: 5,
          activeDrones: 4,
          position: [19.874, 75.884],
          status: 'active',
          registrationDate: '2023-02-20',
          contactPerson: 'Priya Sharma',
          phone: '+91-9876543211',
          email: '<EMAIL>'
        },
        {
          id: 'ORG-003',
          name: "MDM Pvt. Ltd.",
          orgId: "ORG ID:-0002503",
          state: "Maharashtra",
          district: "Mumbai",
          totalDrones: 10,
          activeDrones: 8,
          position: [19.848, 75.915],
          status: 'active',
          registrationDate: '2023-03-10',
          contactPerson: 'Amit Patel',
          phone: '+91-9876543212',
          email: '<EMAIL>'
        },
        {
          id: 'ORG-004',
          name: "ABD Pvt. Ltd.",
          orgId: "ORG ID:-0002504",
          state: "Maharashtra",
          district: "Nagpur",
          totalDrones: 12,
          activeDrones: 10,
          position: [19.837, 75.879],
          status: 'active',
          registrationDate: '2023-04-05',
          contactPerson: 'Sunita Desai',
          phone: '+91-9876543213',
          email: '<EMAIL>'
        },
        {
          id: 'ORG-005',
          name: "GreenTech Solutions",
          orgId: "ORG ID:-0002505",
          state: "Karnataka",
          district: "Bangalore",
          totalDrones: 8,
          activeDrones: 6,
          position: [12.9716, 77.5946],
          status: 'pending',
          registrationDate: '2023-05-12',
          contactPerson: 'Vikram Singh',
          phone: '+91-9876543214',
          email: '<EMAIL>'
        }
      ],
      individuals: [
        {
          id: 'IND-001',
          fullName: "Rajesh Kumar Singh",
          individualId: "IND-0001",
          gender: "male",
          age: 34,
          state: "Maharashtra",
          district: "Pune",
          city: "Pune",
          totalDrones: 2,
          activeDrones: 1,
          position: [18.5204, 73.8567],
          status: 'approved',
          registrationDate: '2023-06-15',
          phone: '+91-9876543220',
          email: '<EMAIL>',
          allocatedDrones: 2
        },
        {
          id: 'IND-002',
          fullName: "Priya Sharma",
          individualId: "IND-0002",
          gender: "female",
          age: 28,
          state: "Karnataka",
          district: "Bangalore",
          city: "Bangalore",
          totalDrones: 1,
          activeDrones: 1,
          position: [12.9716, 77.5946],
          status: 'approved',
          registrationDate: '2023-07-20',
          phone: '+91-9876543221',
          email: '<EMAIL>',
          allocatedDrones: 1
        },
        {
          id: 'IND-003',
          fullName: "Amit Patel",
          individualId: "IND-0003",
          gender: "male",
          age: 42,
          state: "Gujarat",
          district: "Ahmedabad",
          city: "Ahmedabad",
          totalDrones: 3,
          activeDrones: 2,
          position: [23.0225, 72.5714],
          status: 'approved',
          registrationDate: '2023-08-10',
          phone: '+91-9876543222',
          email: '<EMAIL>',
          allocatedDrones: 3
        },
        {
          id: 'IND-004',
          fullName: "Sunita Desai",
          individualId: "IND-0004",
          gender: "female",
          age: 36,
          state: "Maharashtra",
          district: "Mumbai",
          city: "Mumbai",
          totalDrones: 1,
          activeDrones: 0,
          position: [19.0760, 72.8777],
          status: 'pending',
          registrationDate: '2023-09-05',
          phone: '+91-9876543223',
          email: '<EMAIL>',
          allocatedDrones: 1
        },
        {
          id: 'IND-005',
          fullName: "Vikram Singh",
          individualId: "IND-0005",
          gender: "male",
          age: 31,
          state: "Rajasthan",
          district: "Jaipur",
          city: "Jaipur",
          totalDrones: 2,
          activeDrones: 1,
          position: [26.9124, 75.7873],
          status: 'approved',
          registrationDate: '2023-09-15',
          phone: '+91-9876543224',
          email: '<EMAIL>',
          allocatedDrones: 2
        }
      ],
      dronesByOrg: {
        'ORG-001': [
          { id: "PRYMAJ95175", status: "ACTIVE", lat: 19.851, lng: 75.895, battery: 85, altitude: 120, lastUpdate: '2 min ago' },
          { id: "PRYMAJ95173", status: "FLYING", lat: 19.852, lng: 75.896, battery: 78, altitude: 95, lastUpdate: '1 min ago' },
          { id: "PRYMAJ95178", status: "ACTIVE", lat: 19.853, lng: 75.897, battery: 92, altitude: 110, lastUpdate: '3 min ago' },
          { id: "PRYMAJ95176", status: "FLYING", lat: 19.854, lng: 75.898, battery: 67, altitude: 88, lastUpdate: '1 min ago' },
          { id: "PRYMAJ93175", status: "ACTIVE", lat: 19.855, lng: 75.899, battery: 89, altitude: 105, lastUpdate: '4 min ago' },
          { id: "PRYMAJ87388", status: "CRASHED", lat: 19.856, lng: 75.893, battery: 0, altitude: 0, lastUpdate: '2 hours ago' },
          { id: "PRYMAJ86853", status: "INACTIVE", lat: 19.857, lng: 75.892, battery: 45, altitude: 0, lastUpdate: '1 hour ago' },
          { id: "PRYMAJ86854", status: "MAINTENANCE", lat: 19.858, lng: 75.891, battery: 60, altitude: 0, lastUpdate: '30 min ago' },
          { id: "PRYMAJ86855", status: "ACTIVE", lat: 19.859, lng: 75.890, battery: 94, altitude: 115, lastUpdate: '1 min ago' },
          { id: "PRYMAJ86856", status: "FLYING", lat: 19.860, lng: 75.889, battery: 71, altitude: 102, lastUpdate: '2 min ago' }
        ]
      },
      dronesByIndividual: {
        'IND-001': [
          { id: "INDRAJ001", status: "ACTIVE", lat: 18.5204, lng: 73.8567, battery: 88, altitude: 95, lastUpdate: '3 min ago' },
          { id: "INDRAJ002", status: "MAINTENANCE", lat: 18.5205, lng: 73.8568, battery: 45, altitude: 0, lastUpdate: '1 hour ago' }
        ],
        'IND-002': [
          { id: "INDPRI001", status: "FLYING", lat: 12.9716, lng: 77.5946, battery: 92, altitude: 120, lastUpdate: '1 min ago' }
        ],
        'IND-003': [
          { id: "INDAMT001", status: "ACTIVE", lat: 23.0225, lng: 72.5714, battery: 85, altitude: 110, lastUpdate: '2 min ago' },
          { id: "INDAMT002", status: "FLYING", lat: 23.0226, lng: 72.5715, battery: 78, altitude: 98, lastUpdate: '1 min ago' },
          { id: "INDAMT003", status: "INACTIVE", lat: 23.0227, lng: 72.5716, battery: 60, altitude: 0, lastUpdate: '30 min ago' }
        ],
        'IND-004': [
          { id: "INDSUN001", status: "INACTIVE", lat: 19.0760, lng: 72.8777, battery: 55, altitude: 0, lastUpdate: '2 hours ago' }
        ],
        'IND-005': [
          { id: "INDVIK001", status: "ACTIVE", lat: 26.9124, lng: 75.7873, battery: 90, altitude: 105, lastUpdate: '4 min ago' },
          { id: "INDVIK002", status: "MAINTENANCE", lat: 26.9125, lng: 75.7874, battery: 40, altitude: 0, lastUpdate: '45 min ago' }
        ]
      },
      mapSettings: {
        defaultCenter: [20.5937, 78.9629], // India center
        defaultZoom: 6,
        organizationZoom: 14,
        droneZoom: 15
      }
    };
    
    // Start real-time updates
    this.startRealTimeUpdates();
  }

  // Transform backend organization data to map format
  transformOrganizationData(orgData) {
    return {
      id: orgData._id,
      name: orgData.name || orgData.displayName,
      orgId: orgData.registration?.registrationNumber || `ORG-${orgData._id.slice(-6)}`,
      state: orgData.address?.state || 'Unknown',
      district: orgData.address?.city || 'Unknown',
      totalDrones: orgData.allocatedDrones || 0,
      activeDrones: Math.floor((orgData.allocatedDrones || 0) * 0.7), // Estimate 70% active
      position: this.getCoordinatesFromAddress(orgData.address),
      status: orgData.status || 'pending',
      registrationDate: orgData.createdAt || new Date().toISOString(),
      contactPerson: orgData.contact?.contactPerson || 'N/A',
      phone: orgData.contact?.phone || 'N/A',
      email: orgData.contact?.primaryEmail || 'N/A'
    };
  }

  // Transform backend individual data to map format
  transformIndividualData(indData) {
    return {
      id: indData._id,
      fullName: indData.fullName,
      individualId: `IND-${indData._id.slice(-6)}`,
      gender: indData.gender || 'other',
      age: this.calculateAge(indData.dateOfBirth),
      state: indData.address?.state || 'Unknown',
      district: indData.address?.city || 'Unknown',
      city: indData.address?.city || 'Unknown',
      totalDrones: indData.allocatedDrones || 0,
      activeDrones: Math.floor((indData.allocatedDrones || 0) * 0.6), // Estimate 60% active for individuals
      position: this.getCoordinatesFromAddress(indData.address),
      status: indData.status || 'pending',
      registrationDate: indData.createdAt || new Date().toISOString(),
      phone: indData.contact?.phone || 'N/A',
      email: indData.contact?.primaryEmail || 'N/A',
      allocatedDrones: indData.allocatedDrones || 0
    };
  }

  // Get coordinates from address or return default location
  getCoordinatesFromAddress(address) {
    if (address?.coordinates?.coordinates) {
      const coords = address.coordinates.coordinates;
      return [coords[1], coords[0]]; // Convert from [lng, lat] to [lat, lng]
    }

    // Default coordinates based on state
    const stateCoordinates = {
      'Maharashtra': [19.7515, 75.7139],
      'Karnataka': [15.3173, 75.7139],
      'Gujarat': [23.0225, 72.5714],
      'Rajasthan': [27.0238, 74.2179],
      'Delhi': [28.7041, 77.1025],
      'Tamil Nadu': [11.1271, 78.6569],
      'Uttar Pradesh': [26.8467, 80.9462]
    };

    return stateCoordinates[address?.state] || [20.5937, 78.9629]; // Default to India center
  }

  // Calculate age from date of birth
  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  // Check if cache is valid
  isCacheValid(type) {
    const lastFetch = this.cache.lastFetch[type];
    if (!lastFetch) return false;
    return (Date.now() - lastFetch) < this.cacheTimeout;
  }

  // Subscribe to data updates
  subscribe(key, callback) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    this.subscribers.get(key).add(callback);
    
    return () => {
      const callbacks = this.subscribers.get(key);
      if (callbacks) {
        callbacks.delete(callback);
      }
    };
  }

  // Notify subscribers
  notify(key, data) {
    const callbacks = this.subscribers.get(key);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Fetch organizations from backend
  async fetchOrganizations(filters = {}) {
    try {
      // Check cache first
      if (this.isCacheValid('organizations') && !filters.search && !filters.status) {
        console.log('Using cached organizations data');
        this.notify('organizations', this.cache.organizations);
        return this.cache.organizations;
      }

      console.log('Fetching organizations from backend...');

      // Try to fetch from backend
      try {
        const response = await organizationService.getAllOrganizations({
          page: 1,
          limit: 100,
          status: filters.status !== 'all' ? filters.status : undefined,
          search: filters.search || undefined
        });

        if (response.success && response.data.organizations) {
          // Transform backend data to map format
          const transformedOrgs = response.data.organizations.map(org =>
            this.transformOrganizationData(org)
          );

          // Update cache
          this.cache.organizations = transformedOrgs;
          this.cache.lastFetch.organizations = Date.now();

          console.log(`✅ Fetched ${transformedOrgs.length} organizations from backend`);
          this.notify('organizations', transformedOrgs);
          return transformedOrgs;
        }
      } catch (backendError) {
        console.warn('Backend fetch failed, using mock data:', backendError.message);
      }

      // Fallback to mock data
      console.log('Using mock organizations data');
      let filteredOrgs = [...this.mockData.organizations];

      if (filters.state) {
        filteredOrgs = filteredOrgs.filter(org =>
          org.state.toLowerCase().includes(filters.state.toLowerCase())
        );
      }

      if (filters.status && filters.status !== 'all') {
        filteredOrgs = filteredOrgs.filter(org => org.status === filters.status);
      }

      if (filters.search) {
        filteredOrgs = filteredOrgs.filter(org =>
          org.name.toLowerCase().includes(filters.search.toLowerCase()) ||
          org.orgId.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      this.cache.organizations = filteredOrgs;
      this.cache.lastFetch.organizations = Date.now();
      this.notify('organizations', filteredOrgs);
      return filteredOrgs;

    } catch (error) {
      console.error('Error fetching organizations:', error);
      throw new Error('Failed to fetch organizations');
    }
  }

  // Fetch drones for specific organization
  async fetchOrganizationDrones(orgId) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() < 0.02) {
          reject(new Error('Failed to fetch drone data'));
          return;
        }

        const drones = this.cache.dronesByOrg[orgId] || this.mockData.dronesByOrg[orgId] || [];
        
        // Update active drone positions
        const updatedDrones = drones.map(drone => {
          if (drone.status === 'ACTIVE' || drone.status === 'FLYING') {
            return {
              ...drone,
              lat: drone.lat + (Math.random() - 0.5) * 0.001,
              lng: drone.lng + (Math.random() - 0.5) * 0.001,
              battery: Math.max(10, drone.battery - Math.random() * 1),
              altitude: Math.max(50, Math.min(150, drone.altitude + (Math.random() - 0.5) * 10)),
              lastUpdate: 'Just now'
            };
          }
          return drone;
        });

        this.cache.dronesByOrg[orgId] = updatedDrones;
        resolve(updatedDrones);
        this.notify(`drones-${orgId}`, updatedDrones);
      }, Math.random() * 600 + 300);
    });
  }

  // Search functionality
  searchOrganizations(query) {
    const orgs = this.cache.organizations.length > 0 ? this.cache.organizations : this.mockData.organizations;
    if (!query) return orgs;

    return orgs.filter(org =>
      org.name.toLowerCase().includes(query.toLowerCase()) ||
      org.orgId.toLowerCase().includes(query.toLowerCase()) ||
      org.state.toLowerCase().includes(query.toLowerCase()) ||
      org.district.toLowerCase().includes(query.toLowerCase())
    );
  }

  // Get organization by ID
  getOrganizationById(id) {
    const orgs = this.cache.organizations.length > 0 ? this.cache.organizations : this.mockData.organizations;
    return orgs.find(org => org.id === id);
  }

  // Get organization statistics
  getOrganizationStats() {
    const orgs = this.cache.organizations.length > 0 ? this.cache.organizations : this.mockData.organizations;
    const totalOrgs = orgs.length;
    const activeOrgs = orgs.filter(org => org.status === 'active').length;
    const totalDrones = orgs.reduce((sum, org) => sum + org.totalDrones, 0);
    const activeDrones = orgs.reduce((sum, org) => sum + org.activeDrones, 0);

    return {
      totalOrganizations: totalOrgs,
      activeOrganizations: activeOrgs,
      totalDrones,
      activeDrones,
      pendingOrganizations: totalOrgs - activeOrgs
    };
  }

  // Fetch individuals from backend
  async fetchIndividuals(filters = {}) {
    try {
      // Check cache first
      if (this.isCacheValid('individuals') && !filters.search && !filters.status) {
        console.log('Using cached individuals data');
        this.notify('individuals', this.cache.individuals);
        return this.cache.individuals;
      }

      console.log('Fetching individuals from backend...');

      // Try to fetch from backend
      try {
        const response = await individualService.getAllIndividuals({
          page: 1,
          limit: 100,
          status: filters.status !== 'all' ? filters.status : undefined,
          search: filters.search || undefined
        });

        if (response.success && response.data.individuals) {
          // Transform backend data to map format
          const transformedInds = response.data.individuals.map(ind =>
            this.transformIndividualData(ind)
          );

          // Update cache
          this.cache.individuals = transformedInds;
          this.cache.lastFetch.individuals = Date.now();

          console.log(`✅ Fetched ${transformedInds.length} individuals from backend`);
          this.notify('individuals', transformedInds);
          return transformedInds;
        }
      } catch (backendError) {
        console.warn('Backend fetch failed, using mock data:', backendError.message);
      }

      // Fallback to mock data
      console.log('Using mock individuals data');
      let filteredIndividuals = [...this.mockData.individuals];

      if (filters.state) {
        filteredIndividuals = filteredIndividuals.filter(individual =>
          individual.state.toLowerCase().includes(filters.state.toLowerCase())
        );
      }

      if (filters.status && filters.status !== 'all') {
        filteredIndividuals = filteredIndividuals.filter(individual => individual.status === filters.status);
      }

      if (filters.search) {
        filteredIndividuals = filteredIndividuals.filter(individual =>
          individual.fullName.toLowerCase().includes(filters.search.toLowerCase()) ||
          individual.individualId.toLowerCase().includes(filters.search.toLowerCase()) ||
          individual.email.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      this.cache.individuals = filteredIndividuals;
      this.cache.lastFetch.individuals = Date.now();
      this.notify('individuals', filteredIndividuals);
      return filteredIndividuals;

    } catch (error) {
      console.error('Error fetching individuals:', error);
      throw new Error('Failed to fetch individuals');
    }
  }

  // Fetch drones for specific individual
  async fetchIndividualDrones(individualId) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() < 0.02) {
          reject(new Error('Failed to fetch drone data'));
          return;
        }

        const drones = this.cache.dronesByIndividual[individualId] || this.mockData.dronesByIndividual[individualId] || [];

        // Update active drone positions
        const updatedDrones = drones.map(drone => {
          if (drone.status === 'ACTIVE' || drone.status === 'FLYING') {
            return {
              ...drone,
              lat: drone.lat + (Math.random() - 0.5) * 0.001,
              lng: drone.lng + (Math.random() - 0.5) * 0.001,
              battery: Math.max(10, drone.battery - Math.random() * 1),
              altitude: Math.max(50, Math.min(150, drone.altitude + (Math.random() - 0.5) * 10)),
              lastUpdate: 'Just now'
            };
          }
          return drone;
        });

        this.cache.dronesByIndividual[individualId] = updatedDrones;
        resolve(updatedDrones);
        this.notify(`drones-${individualId}`, updatedDrones);
      }, Math.random() * 600 + 300);
    });
  }

  // Search individuals
  searchIndividuals(query) {
    const individuals = this.cache.individuals.length > 0 ? this.cache.individuals : this.mockData.individuals;
    if (!query) return individuals;

    return individuals.filter(individual =>
      individual.fullName.toLowerCase().includes(query.toLowerCase()) ||
      individual.individualId.toLowerCase().includes(query.toLowerCase()) ||
      individual.state.toLowerCase().includes(query.toLowerCase()) ||
      individual.district.toLowerCase().includes(query.toLowerCase()) ||
      individual.email.toLowerCase().includes(query.toLowerCase())
    );
  }

  // Get individual by ID
  getIndividualById(id) {
    const individuals = this.cache.individuals.length > 0 ? this.cache.individuals : this.mockData.individuals;
    return individuals.find(individual => individual.id === id);
  }

  // Get individual statistics
  getIndividualStats() {
    const individuals = this.cache.individuals.length > 0 ? this.cache.individuals : this.mockData.individuals;
    const totalIndividuals = individuals.length;
    const approvedIndividuals = individuals.filter(individual => individual.status === 'approved').length;
    const totalDrones = individuals.reduce((sum, individual) => sum + individual.totalDrones, 0);
    const activeDrones = individuals.reduce((sum, individual) => sum + individual.activeDrones, 0);

    return {
      totalIndividuals,
      approvedIndividuals,
      totalDrones,
      activeDrones
    };
  }

  // Get combined statistics
  getCombinedStats() {
    const orgStats = this.getOrganizationStats();
    const indStats = this.getIndividualStats();

    return {
      totalOrganizations: orgStats.totalOrganizations,
      activeOrganizations: orgStats.activeOrganizations,
      totalIndividuals: indStats.totalIndividuals,
      approvedIndividuals: indStats.approvedIndividuals,
      totalDrones: orgStats.totalDrones + indStats.totalDrones,
      activeDrones: orgStats.activeDrones + indStats.activeDrones
    };
  }

  // Start real-time updates
  startRealTimeUpdates() {
    // Update drone positions every 5 seconds
    setInterval(() => {
      if (this.cache.dronesByOrg) {
        Object.keys(this.cache.dronesByOrg).forEach(orgId => {
          this.fetchOrganizationDrones(orgId).catch(() => {});
        });
      }
    }, 5000);

    // Update organization stats every 30 seconds
    setInterval(() => {
      if (this.cache.organizations && this.cache.organizations.length > 0) {
        this.cache.organizations = this.cache.organizations.map(org => ({
          ...org,
          activeDrones: Math.max(0, Math.min(org.totalDrones,
            org.activeDrones + Math.floor((Math.random() - 0.5) * 2)
          ))
        }));
        this.notify('organizations', this.cache.organizations);
      }
    }, 30000);
  }

  // Get current data
  getCurrentData() {
    return this.cache;
  }
}

// Create singleton instance
const mapDataService = new MapDataService();

export default mapDataService;
