import api from './api';

/**
 * Organization service for handling all organization-related API calls
 */
class OrganizationService {
  /**
   * Get all organizations with optional filtering and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Organizations list with pagination
   */
  async getAllOrganizations(params = {}) {
    try {
      const response = await api.get('/organizations', { params });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch organizations');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch organizations');
    }
  }

  /**
   * Get organization by ID
   * @param {string} id - Organization ID
   * @returns {Promise<Object>} Organization data
   */
  async getOrganizationById(id) {
    try {
      const response = await api.get(`/organizations/${id}`);
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch organization');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch organization');
    }
  }

  /**
   * Create new organization
   * @param {Object} organizationData - Organization data
   * @returns {Promise<Object>} Created organization
   */
  async createOrganization(organizationData) {
    try {
      const response = await api.post('/organizations', organizationData);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to create organization');
      }
    } catch (error) {
      // Handle different error types
      if (error.response?.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('Access denied. You do not have permission to create organizations.');
      } else if (error.response?.status === 400) {
        const errorMessage = error.response.data.message || 'Validation failed';
        throw new Error(errorMessage);
      } else if (error.response?.status === 500) {
        const errorMessage = error.response.data.message || 'Server error occurred';
        throw new Error(`Server Error: ${errorMessage}`);
      }

      throw new Error(error.response?.data?.message || error.message || 'Failed to create organization');
    }
  }

  /**
   * Update organization
   * @param {string} id - Organization ID
   * @param {Object} organizationData - Updated organization data
   * @returns {Promise<Object>} Updated organization
   */
  async updateOrganization(id, organizationData) {
    try {
      const response = await api.put(`/organizations/${id}`, organizationData);
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to update organization');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update organization');
    }
  }

  /**
   * Update organization status
   * @param {string} id - Organization ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated organization
   */
  async updateOrganizationStatus(id, status) {
    try {
      const response = await api.patch(`/organizations/${id}/status`, { status });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to update organization status');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update organization status');
    }
  }

  /**
   * Verify organization
   * @param {string} id - Organization ID
   * @returns {Promise<Object>} Verified organization
   */
  async verifyOrganization(id) {
    try {
      const response = await api.patch(`/organizations/${id}/verify`);
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to verify organization');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to verify organization');
    }
  }

  /**
   * Delete organization (soft delete)
   * @param {string} id - Organization ID
   * @returns {Promise<Object>} Deletion response
   */
  async deleteOrganization(id) {
    try {
      const response = await api.delete(`/organizations/${id}`);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to delete organization');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete organization');
    }
  }

  /**
   * Get organization statistics
   * @returns {Promise<Object>} Organization statistics
   */
  async getOrganizationStats() {
    try {
      const response = await api.get('/organizations/stats');
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch organization statistics');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch organization statistics');
    }
  }

  /**
   * Add note to organization
   * @param {string} id - Organization ID
   * @param {string} content - Note content
   * @param {string} type - Note type
   * @returns {Promise<Object>} Updated organization with notes
   */
  async addOrganizationNote(id, content, type = 'general') {
    try {
      const response = await api.post(`/organizations/${id}/notes`, { content, type });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to add note');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to add note');
    }
  }

  /**
   * Get users belonging to organization
   * @param {string} id - Organization ID
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Organization users
   */
  async getOrganizationUsers(id, params = {}) {
    try {
      const response = await api.get(`/organizations/${id}/users`, { params });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch organization users');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch organization users');
    }
  }

  /**
   * Get drones belonging to organization
   * @param {string} id - Organization ID
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Organization drones
   */
  async getOrganizationDrones(id, params = {}) {
    try {
      const response = await api.get(`/organizations/${id}/drones`, { params });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch organization drones');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch organization drones');
    }
  }

  /**
   * Search organizations
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Search results
   */
  async searchOrganizations(query, filters = {}) {
    try {
      const params = { search: query, ...filters };
      return await this.getAllOrganizations(params);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get deleted organizations
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Deleted organizations list
   */
  async getDeletedOrganizations(params = {}) {
    try {
      const response = await api.get('/organizations/deleted', { params });

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch deleted organizations');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch deleted organizations');
    }
  }

  /**
   * Restore deleted organization
   * @param {string} id - Organization ID
   * @returns {Promise<Object>} Restore response
   */
  async restoreOrganization(id) {
    try {
      const response = await api.post(`/organizations/${id}/restore`);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to restore organization');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to restore organization');
    }
  }

  /**
   * Get organization types for dropdown
   * @returns {Array} Organization types
   */
  getOrganizationTypes() {
    return [
      { value: 'government', label: 'Government' },
      { value: 'private', label: 'Private' },
      { value: 'ngo', label: 'NGO' },
      { value: 'research', label: 'Research' },
      { value: 'military', label: 'Military' },
      { value: 'other', label: 'Other' }
    ];
  }

  /**
   * Get organization statuses for dropdown
   * @returns {Array} Organization statuses
   */
  getOrganizationStatuses() {
    return [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'suspended', label: 'Suspended' },
      { value: 'pending', label: 'Pending' }
    ];
  }
}

// Export singleton instance
const organizationService = new OrganizationService();
export default organizationService;
