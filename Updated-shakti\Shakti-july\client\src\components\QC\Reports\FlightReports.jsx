import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  FileText,
  Plus,
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Plane,
  BarChart3,
  Target,
  Battery
} from 'lucide-react';

const FlightReports = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [flightReports, setFlightReports] = useState([]);

  // Initialize with sample flight report data
  React.useEffect(() => {
    if (flightReports.length === 0) {
      setFlightReports([
        {
          id: 'FR-2024-001',
          flightId: 'FLT-2024-0156',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          pilot: '<PERSON>',
          date: '2024-01-15',
          time: '09:30',
          duration: '2.5 hours',
          location: 'Survey Area Alpha',
          missionType: 'Mapping Survey',
          status: 'Completed',
          reportType: 'Post-Flight Analysis',
          generatedBy: 'Auto-Generated',
          generatedAt: '2024-01-15 12:15',
          flightData: {
            totalDistance: '45.2 km',
            maxAltitude: '120m',
            avgSpeed: '18 km/h',
            batteryUsed: '85%',
            dataCollected: '12.4 GB',
            waypoints: 156,
            photos: 1247,
            videoTime: '45 minutes'
          },
          performance: {
            flightEfficiency: 94,
            batteryPerformance: 89,
            dataQuality: 96,
            missionSuccess: 100,
            safetyScore: 100
          },
          weather: {
            temperature: '22°C',
            windSpeed: '8 km/h',
            visibility: '10 km',
            conditions: 'Clear'
          },
          issues: [],
          summary: 'Successful mapping mission with excellent data quality and full objective completion.',
          recommendations: ['Continue current flight patterns', 'Monitor battery performance'],
          attachments: ['flight_log.csv', 'telemetry_data.json', 'mission_photos.zip']
        },
        {
          id: 'FR-2024-002',
          flightId: 'FLT-2024-0154',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          pilot: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:20',
          duration: '1.8 hours',
          location: 'Industrial Site B',
          missionType: 'Infrastructure Inspection',
          status: 'Completed',
          reportType: 'Inspection Report',
          generatedBy: 'Sarah Johnson',
          generatedAt: '2024-01-14 16:45',
          flightData: {
            totalDistance: '28.7 km',
            maxAltitude: '95m',
            avgSpeed: '16 km/h',
            batteryUsed: '78%',
            dataCollected: '8.9 GB',
            waypoints: 89,
            photos: 892,
            videoTime: '32 minutes'
          },
          performance: {
            flightEfficiency: 87,
            batteryPerformance: 82,
            dataQuality: 91,
            missionSuccess: 95,
            safetyScore: 100
          },
          weather: {
            temperature: '18°C',
            windSpeed: '12 km/h',
            visibility: '8 km',
            conditions: 'Partly Cloudy'
          },
          issues: [
            'Minor signal interference near power lines',
            'Battery temperature slightly elevated'
          ],
          summary: 'Infrastructure inspection completed successfully with minor technical issues noted.',
          recommendations: ['Avoid flight paths near high-voltage lines', 'Monitor battery temperature'],
          attachments: ['inspection_report.pdf', 'thermal_images.zip', 'flight_data.csv']
        },
        {
          id: 'FR-2024-003',
          flightId: 'FLT-2024-0152',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          pilot: 'Mike Wilson',
          date: '2024-01-13',
          time: '11:15',
          duration: '3.2 hours',
          location: 'Agricultural Zone C',
          missionType: 'Crop Monitoring',
          status: 'Completed',
          reportType: 'Agricultural Analysis',
          generatedBy: 'Auto-Generated',
          generatedAt: '2024-01-13 14:45',
          flightData: {
            totalDistance: '62.1 km',
            maxAltitude: '110m',
            avgSpeed: '19 km/h',
            batteryUsed: '92%',
            dataCollected: '18.7 GB',
            waypoints: 234,
            photos: 1856,
            videoTime: '78 minutes'
          },
          performance: {
            flightEfficiency: 96,
            batteryPerformance: 94,
            dataQuality: 98,
            missionSuccess: 100,
            safetyScore: 100
          },
          weather: {
            temperature: '25°C',
            windSpeed: '6 km/h',
            visibility: '12 km',
            conditions: 'Clear'
          },
          issues: [],
          summary: 'Comprehensive crop monitoring mission with exceptional data quality and coverage.',
          recommendations: ['Excellent performance - maintain current procedures', 'Consider extending coverage area'],
          attachments: ['crop_analysis.pdf', 'ndvi_maps.tiff', 'flight_telemetry.json']
        },
        {
          id: 'FR-2024-004',
          flightId: 'FLT-2024-0148',
          droneId: 'DRN-002',
          droneName: 'Inspector Delta',
          pilot: 'Emily Chen',
          date: '2024-01-12',
          time: '08:45',
          duration: '1.5 hours',
          location: 'Construction Site D',
          missionType: 'Progress Monitoring',
          status: 'Under Review',
          reportType: 'Progress Report',
          generatedBy: 'Emily Chen',
          generatedAt: '2024-01-12 10:30',
          flightData: {
            totalDistance: '22.3 km',
            maxAltitude: '85m',
            avgSpeed: '15 km/h',
            batteryUsed: '65%',
            dataCollected: '6.8 GB',
            waypoints: 67,
            photos: 634,
            videoTime: '28 minutes'
          },
          performance: {
            flightEfficiency: 91,
            batteryPerformance: 88,
            dataQuality: 89,
            missionSuccess: 98,
            safetyScore: 100
          },
          weather: {
            temperature: '20°C',
            windSpeed: '10 km/h',
            visibility: '9 km',
            conditions: 'Overcast'
          },
          issues: [
            'Slight image blur in windy conditions'
          ],
          summary: 'Construction progress monitoring completed with good overall results.',
          recommendations: ['Adjust camera settings for windy conditions', 'Schedule follow-up inspection'],
          attachments: ['progress_photos.zip', 'comparison_analysis.pdf', 'site_map.jpg']
        },
        {
          id: 'FR-2024-005',
          flightId: 'FLT-2024-0145',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          pilot: 'David Rodriguez',
          date: '2024-01-11',
          time: '15:30',
          duration: '2.1 hours',
          location: 'Coastal Survey Area',
          missionType: 'Environmental Survey',
          status: 'Draft',
          reportType: 'Environmental Assessment',
          generatedBy: 'David Rodriguez',
          generatedAt: '2024-01-11 17:45',
          flightData: {
            totalDistance: '38.9 km',
            maxAltitude: '100m',
            avgSpeed: '18 km/h',
            batteryUsed: '81%',
            dataCollected: '14.2 GB',
            waypoints: 145,
            photos: 1123,
            videoTime: '52 minutes'
          },
          performance: {
            flightEfficiency: 93,
            batteryPerformance: 90,
            dataQuality: 94,
            missionSuccess: 97,
            safetyScore: 100
          },
          weather: {
            temperature: '19°C',
            windSpeed: '15 km/h',
            visibility: '7 km',
            conditions: 'Windy'
          },
          issues: [
            'Strong coastal winds affected flight stability',
            'Some data gaps due to weather conditions'
          ],
          summary: 'Environmental survey completed despite challenging weather conditions.',
          recommendations: ['Reschedule flights during calmer weather', 'Use wind-resistant flight patterns'],
          attachments: ['environmental_data.csv', 'coastal_images.zip', 'weather_log.txt']
        }
      ]);
    }
  }, [flightReports.length]);

  // Form state for new flight report
  const [newFlightReport, setNewFlightReport] = useState({
    flightId: '',
    droneId: '',
    droneName: '',
    pilot: '',
    date: '',
    time: '',
    location: '',
    missionType: 'Mapping Survey',
    reportType: 'Post-Flight Analysis',
    notes: ''
  });

  // Add new flight report function
  const handleAddFlightReport = (e) => {
    e.preventDefault();
    
    const newReport = {
      id: `FR-2024-${String(flightReports.length + 1).padStart(3, '0')}`,
      ...newFlightReport,
      status: 'Draft',
      generatedBy: newFlightReport.pilot,
      generatedAt: new Date().toISOString().replace('T', ' ').substring(0, 16),
      duration: '0 hours',
      flightData: {
        totalDistance: '0 km',
        maxAltitude: '0m',
        avgSpeed: '0 km/h',
        batteryUsed: '0%',
        dataCollected: '0 GB',
        waypoints: 0,
        photos: 0,
        videoTime: '0 minutes'
      },
      performance: {
        flightEfficiency: 0,
        batteryPerformance: 0,
        dataQuality: 0,
        missionSuccess: 0,
        safetyScore: 0
      },
      weather: {
        temperature: 'N/A',
        windSpeed: 'N/A',
        visibility: 'N/A',
        conditions: 'Unknown'
      },
      issues: [],
      summary: 'Report pending completion.',
      recommendations: [],
      attachments: []
    };
    
    setFlightReports([...flightReports, newReport]);
    setNewFlightReport({
      flightId: '',
      droneId: '',
      droneName: '',
      pilot: '',
      date: '',
      time: '',
      location: '',
      missionType: 'Mapping Survey',
      reportType: 'Post-Flight Analysis',
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete flight report function
  const handleDeleteFlightReport = (id) => {
    setFlightReports(flightReports.filter(report => report.id !== id));
  };

  // Update flight report status
  const handleUpdateStatus = (id, newStatus) => {
    setFlightReports(flightReports.map(report => 
      report.id === id ? { ...report, status: newStatus } : report
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'Under Review': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Draft': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Archived': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed': return <CheckCircle className="w-4 h-4" />;
      case 'Under Review': return <Clock className="w-4 h-4" />;
      case 'Draft': return <Edit className="w-4 h-4" />;
      case 'Archived': return <XCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredFlightReports = flightReports.filter(report => {
    const matchesSearch = report.flightId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.pilot.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || report.status.toLowerCase() === filterStatus.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  const headerActions = (
    <div className="flex items-center justify-between gap-4">
      {/* Left Side - Search */}
      <div className="flex items-center gap-4 flex-1">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Quick Filters */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="completed">Completed</option>
          <option value="under review">Under Review</option>
          <option value="draft">Draft</option>
        </select>
      </div>

      {/* Right Side - Actions */}
      <div className="flex items-center gap-3">
        <button
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Report
        </button>
      </div>
    </div>
  );

  return (
    <QCLayout
      title="Flight Reports"
      subtitle="Comprehensive flight mission reports and analysis"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{filteredFlightReports.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <FileText className="w-3 h-3" />
                  Generated
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <FileText className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredFlightReports.filter(r => r.status === 'Completed').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Finalized
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Under Review</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredFlightReports.filter(r => r.status === 'Under Review').length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Clock className="w-3 h-3" />
                  Pending
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Clock className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Draft</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredFlightReports.filter(r => r.status === 'Draft').length}
                </p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <Edit className="w-3 h-3" />
                  In progress
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <Edit className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Flight Reports Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Flight Report Records</h3>
                <p className="text-sm text-gray-600 mt-1">Comprehensive flight mission reports and analysis</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Flight Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mission Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status & Issues
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFlightReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                    {/* Flight Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Plane className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{report.id}</h4>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                              {report.flightId}
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{report.droneId} - {report.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {report.pilot}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {report.date} at {report.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {report.duration}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Mission Info */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{report.missionType}</p>
                          <p className="text-xs text-gray-600">{report.reportType}</p>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <MapPin className="w-3 h-3" />
                          <span>{report.location}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Distance: {report.flightData.totalDistance}</p>
                          <p>Data: {report.flightData.dataCollected}</p>
                        </div>
                      </div>
                    </td>

                    {/* Performance */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Target className="w-3 h-3" />
                            Efficiency
                          </span>
                          <span className={`text-xs font-medium ${getPerformanceColor(report.performance.flightEfficiency)}`}>
                            {report.performance.flightEfficiency}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Battery className="w-3 h-3" />
                            Battery
                          </span>
                          <span className={`text-xs font-medium ${getPerformanceColor(report.performance.batteryPerformance)}`}>
                            {report.performance.batteryPerformance}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <BarChart3 className="w-3 h-3" />
                            Data Quality
                          </span>
                          <span className={`text-xs font-medium ${getPerformanceColor(report.performance.dataQuality)}`}>
                            {report.performance.dataQuality}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <CheckCircle className="w-3 h-3" />
                            Success
                          </span>
                          <span className={`text-xs font-medium ${getPerformanceColor(report.performance.missionSuccess)}`}>
                            {report.performance.missionSuccess}%
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Status & Issues */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                          {getStatusIcon(report.status)}
                          <span className="ml-1">{report.status}</span>
                        </span>

                        {report.issues.length > 0 ? (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Issues:</p>
                            <div className="space-y-1">
                              {report.issues.slice(0, 2).map((issue, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                                  <span className="text-xs text-yellow-700">{issue}</span>
                                </div>
                              ))}
                              {report.issues.length > 2 && (
                                <p className="text-xs text-gray-500">+{report.issues.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-green-700">No issues</span>
                          </div>
                        )}

                        <div className="text-xs text-gray-500">
                          <p>Generated: {report.generatedAt}</p>
                          <p>By: {report.generatedBy}</p>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-reports/flight-reports/${report.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={report.status}
                          onChange={(e) => handleUpdateStatus(report.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Draft">Draft</option>
                          <option value="Under Review">Under Review</option>
                          <option value="Completed">Completed</option>
                          <option value="Archived">Archived</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteFlightReport(report.id)}
                          title="Delete Report"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Flight Report Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">New Flight Report</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new flight mission report</p>
              </div>

              <form onSubmit={handleAddFlightReport} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Flight ID</label>
                    <input
                      type="text"
                      value={newFlightReport.flightId}
                      onChange={(e) => setNewFlightReport({...newFlightReport, flightId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., FLT-2024-0157"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID</label>
                    <input
                      type="text"
                      value={newFlightReport.droneId}
                      onChange={(e) => setNewFlightReport({...newFlightReport, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name</label>
                    <input
                      type="text"
                      value={newFlightReport.droneName}
                      onChange={(e) => setNewFlightReport({...newFlightReport, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Pilot</label>
                    <input
                      type="text"
                      value={newFlightReport.pilot}
                      onChange={(e) => setNewFlightReport({...newFlightReport, pilot: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input
                      type="date"
                      value={newFlightReport.date}
                      onChange={(e) => setNewFlightReport({...newFlightReport, date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newFlightReport.time}
                      onChange={(e) => setNewFlightReport({...newFlightReport, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={newFlightReport.location}
                      onChange={(e) => setNewFlightReport({...newFlightReport, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Survey Area Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mission Type</label>
                    <select
                      value={newFlightReport.missionType}
                      onChange={(e) => setNewFlightReport({...newFlightReport, missionType: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Mapping Survey">Mapping Survey</option>
                      <option value="Infrastructure Inspection">Infrastructure Inspection</option>
                      <option value="Crop Monitoring">Crop Monitoring</option>
                      <option value="Progress Monitoring">Progress Monitoring</option>
                      <option value="Environmental Survey">Environmental Survey</option>
                      <option value="Search and Rescue">Search and Rescue</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                  <select
                    value={newFlightReport.reportType}
                    onChange={(e) => setNewFlightReport({...newFlightReport, reportType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Post-Flight Analysis">Post-Flight Analysis</option>
                    <option value="Inspection Report">Inspection Report</option>
                    <option value="Agricultural Analysis">Agricultural Analysis</option>
                    <option value="Progress Report">Progress Report</option>
                    <option value="Environmental Assessment">Environmental Assessment</option>
                    <option value="Incident Report">Incident Report</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={newFlightReport.notes}
                    onChange={(e) => setNewFlightReport({...newFlightReport, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the flight report..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Flight Report
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default FlightReports;
