import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ClipboardCheck,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Battery,
  Thermometer,
  Activity,
  Zap
} from 'lucide-react';

const PostFlightInspection = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [inspections, setInspections] = useState([]);

  // Initialize with sample inspection data
  React.useEffect(() => {
    if (inspections.length === 0) {
      setInspections([
        {
          id: 'POI-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          inspector: '<PERSON>',
          date: '2024-01-15',
          time: '17:30',
          location: 'Landing Pad A',
          status: 'Passed',
          flightDuration: '45 minutes',
          flightDistance: '12.5 km',
          checklist: {
            battery: { status: 'Pass', voltage: '24.8V', temperature: '32°C', cycles: '156' },
            motors: { status: 'Pass', temperature: '45°C', vibration: 'Normal', efficiency: '98%' },
            sensors: { status: 'Pass', calibration: 'Stable', accuracy: 'High', drift: 'None' },
            structure: { status: 'Pass', condition: 'Excellent', wear: 'Minimal', damage: 'None' },
            dataLog: { status: 'Pass', errors: '0', warnings: '2', storage: '85%' }
          },
          issues: [],
          findings: ['Minor GPS drift during flight', 'Battery performance excellent'],
          notes: 'Successful mission completion. All systems performed within normal parameters.',
          nextMaintenance: '2024-01-20'
        },
        {
          id: 'POI-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          inspector: 'Sarah Johnson',
          date: '2024-01-15',
          time: '16:45',
          location: 'Field Station B',
          status: 'Warning',
          flightDuration: '32 minutes',
          flightDistance: '8.2 km',
          checklist: {
            battery: { status: 'Warning', voltage: '23.9V', temperature: '38°C', cycles: '203' },
            motors: { status: 'Pass', temperature: '42°C', vibration: 'Normal', efficiency: '96%' },
            sensors: { status: 'Pass', calibration: 'Stable', accuracy: 'High', drift: 'Minimal' },
            structure: { status: 'Pass', condition: 'Good', wear: 'Normal', damage: 'None' },
            dataLog: { status: 'Pass', errors: '0', warnings: '5', storage: '92%' }
          },
          issues: ['Battery voltage lower than expected', 'Increased warning count in logs'],
          findings: ['Battery showing signs of degradation', 'Flight performance still acceptable'],
          notes: 'Battery requires monitoring. Consider replacement soon.',
          nextMaintenance: '2024-01-17'
        },
        {
          id: 'POI-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          inspector: 'Mike Wilson',
          date: '2024-01-14',
          time: '18:15',
          location: 'Hangar A',
          status: 'Failed',
          flightDuration: '28 minutes',
          flightDistance: '6.8 km',
          checklist: {
            battery: { status: 'Pass', voltage: '25.1V', temperature: '35°C', cycles: '89' },
            motors: { status: 'Fail', temperature: '68°C', vibration: 'High', efficiency: '89%' },
            sensors: { status: 'Pass', calibration: 'Stable', accuracy: 'High', drift: 'None' },
            structure: { status: 'Warning', condition: 'Fair', wear: 'Moderate', damage: 'Minor' },
            dataLog: { status: 'Warning', errors: '3', warnings: '12', storage: '78%' }
          },
          issues: ['Motor #2 overheating', 'Excessive vibration detected', 'Multiple system errors'],
          findings: ['Motor bearing failure suspected', 'Structural stress indicators present'],
          notes: 'Drone grounded pending motor replacement and structural inspection.',
          nextMaintenance: '2024-01-15'
        }
      ]);
    }
  }, [inspections.length]);

  // Form state for new inspection
  const [newInspection, setNewInspection] = useState({
    droneId: '',
    droneName: '',
    inspector: '',
    date: '',
    time: '',
    location: '',
    flightDuration: '',
    flightDistance: '',
    checklist: {
      battery: { status: 'Pass', voltage: '', temperature: '', cycles: '' },
      motors: { status: 'Pass', temperature: '', vibration: '', efficiency: '' },
      sensors: { status: 'Pass', calibration: '', accuracy: '', drift: '' },
      structure: { status: 'Pass', condition: '', wear: '', damage: '' },
      dataLog: { status: 'Pass', errors: '', warnings: '', storage: '' }
    },
    issues: [],
    findings: [],
    notes: ''
  });

  // Add new inspection function
  const handleAddInspection = (e) => {
    e.preventDefault();
    
    // Determine overall status based on checklist
    const hasFailures = Object.values(newInspection.checklist).some(item => item.status === 'Fail');
    const hasWarnings = Object.values(newInspection.checklist).some(item => item.status === 'Warning');
    
    let overallStatus = 'Passed';
    if (hasFailures) overallStatus = 'Failed';
    else if (hasWarnings) overallStatus = 'Warning';

    const newInsp = {
      id: `POI-2024-${String(inspections.length + 1).padStart(3, '0')}`,
      ...newInspection,
      status: overallStatus,
      nextMaintenance: new Date(Date.now() + 5*24*60*60*1000).toISOString().split('T')[0]
    };
    
    setInspections([...inspections, newInsp]);
    setNewInspection({
      droneId: '',
      droneName: '',
      inspector: '',
      date: '',
      time: '',
      location: '',
      flightDuration: '',
      flightDistance: '',
      checklist: {
        battery: { status: 'Pass', voltage: '', temperature: '', cycles: '' },
        motors: { status: 'Pass', temperature: '', vibration: '', efficiency: '' },
        sensors: { status: 'Pass', calibration: '', accuracy: '', drift: '' },
        structure: { status: 'Pass', condition: '', wear: '', damage: '' },
        dataLog: { status: 'Pass', errors: '', warnings: '', storage: '' }
      },
      issues: [],
      findings: [],
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete inspection function
  const handleDeleteInspection = (id) => {
    setInspections(inspections.filter(inspection => inspection.id !== id));
  };

  // Update inspection status
  const handleUpdateStatus = (id, newStatus) => {
    setInspections(inspections.map(inspection => 
      inspection.id === id ? { ...inspection, status: newStatus } : inspection
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Passed': return 'bg-green-100 text-green-700 border-green-200';
      case 'Failed': return 'bg-red-100 text-red-700 border-red-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Passed': return <CheckCircle className="w-4 h-4" />;
      case 'Failed': return <XCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      case 'In Progress': return <Clock className="w-4 h-4" />;
      default: return <ClipboardCheck className="w-4 h-4" />;
    }
  };

  const getChecklistItemColor = (status) => {
    switch (status) {
      case 'Pass': return 'text-green-600';
      case 'Fail': return 'text-red-600';
      case 'Warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = inspection.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inspection.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inspection.inspector.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || inspection.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesDrone = filterDrone === 'all' || inspection.droneId === filterDrone;
    return matchesSearch && matchesStatus && matchesDrone;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search inspections..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="passed">Passed</option>
        <option value="failed">Failed</option>
        <option value="warning">Warning</option>
        <option value="in progress">In Progress</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Inspection</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Post-Flight Inspections"
      subtitle="Conduct and manage post-flight safety inspections"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Today's Inspections</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.date === new Date().toISOString().split('T')[0]).length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <ClipboardCheck className="w-3 h-3" />
                  Completed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <ClipboardCheck className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Passed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.status === 'Passed').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  No issues
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Warnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.status === 'Warning').length}
                </p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Monitor closely
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.status === 'Failed').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <XCircle className="w-3 h-3" />
                  Needs repair
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <XCircle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Inspections Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Post-Flight Inspection Records</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage all post-flight safety inspections</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Inspection Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Flight Data
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    System Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issues & Findings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInspections.map((inspection) => (
                  <tr key={inspection.id} className="hover:bg-gray-50 transition-colors">
                    {/* Inspection Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <ClipboardCheck className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{inspection.id}</h4>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.status)}`}>
                              {getStatusIcon(inspection.status)}
                              <span className="ml-1">{inspection.status}</span>
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{inspection.droneId} - {inspection.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {inspection.inspector}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {inspection.date} at {inspection.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {inspection.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Flight Data */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="w-3 h-3 text-gray-400" />
                          <span className="text-sm text-gray-900">Duration: {inspection.flightDuration}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-3 h-3 text-gray-400" />
                          <span className="text-sm text-gray-900">Distance: {inspection.flightDistance}</span>
                        </div>
                      </div>
                    </td>

                    {/* System Status */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Battery className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.battery.status)}`}>
                            Battery: {inspection.checklist.battery.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Zap className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.motors.status)}`}>
                            Motors: {inspection.checklist.motors.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Activity className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.sensors.status)}`}>
                            Sensors: {inspection.checklist.sensors.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Thermometer className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.structure.status)}`}>
                            Structure: {inspection.checklist.structure.status}
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Issues & Findings */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {inspection.issues.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Issues:</p>
                            <div className="space-y-1">
                              {inspection.issues.slice(0, 2).map((issue, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{issue}</span>
                                </div>
                              ))}
                              {inspection.issues.length > 2 && (
                                <p className="text-xs text-gray-500">+{inspection.issues.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {inspection.findings.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Findings:</p>
                            <div className="space-y-1">
                              {inspection.findings.slice(0, 2).map((finding, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <CheckCircle className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-blue-700">{finding}</span>
                                </div>
                              ))}
                              {inspection.findings.length > 2 && (
                                <p className="text-xs text-gray-500">+{inspection.findings.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-inspections/post-flight/${inspection.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={inspection.status}
                          onChange={(e) => handleUpdateStatus(inspection.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Passed">Passed</option>
                          <option value="Failed">Failed</option>
                          <option value="Warning">Warning</option>
                          <option value="In Progress">In Progress</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteInspection(inspection.id)}
                          title="Delete Inspection"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default PostFlightInspection;
