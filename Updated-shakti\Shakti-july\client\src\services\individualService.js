import api from './api';

/**
 * Individual service for handling all individual-related API calls
 */
class IndividualService {
  /**
   * Get all individuals with optional filtering and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Individuals list with pagination
   */
  async getAllIndividuals(params = {}) {
    try {
      const response = await api.get('/individuals', { params });
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch individuals');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch individuals');
    }
  }

  /**
   * Get individual by ID
   * @param {string} id - Individual ID
   * @returns {Promise<Object>} Individual data
   */
  async getIndividualById(id) {
    try {
      const response = await api.get(`/individuals/${id}`);
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch individual');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch individual');
    }
  }

  /**
   * Create new individual
   * @param {Object} individualData - Individual data
   * @returns {Promise<Object>} Created individual
   */
  async createIndividual(individualData) {
    try {
      const response = await api.post('/individuals', individualData);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to create individual');
      }
    } catch (error) {
      // Handle different error types - same pattern as organizationService
      if (error.response?.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('Access denied. You do not have permission to create individuals.');
      } else if (error.response?.status === 400) {
        const errorMessage = error.response.data.message || 'Validation failed';
        throw new Error(errorMessage);
      } else if (error.response?.status === 409) {
        const errorMessage = error.response.data.message || 'Individual already exists';
        throw new Error(errorMessage);
      } else if (error.response?.status === 500) {
        const errorMessage = error.response.data.message || 'Server error occurred';
        throw new Error(`Server Error: ${errorMessage}`);
      }

      throw new Error(error.response?.data?.message || error.message || 'Failed to create individual');
    }
  }

  /**
   * Update individual
   * @param {string} id - Individual ID
   * @param {Object} individualData - Updated individual data
   * @returns {Promise<Object>} Updated individual
   */
  async updateIndividual(id, individualData) {
    try {
      const response = await api.put(`/individuals/${id}`, individualData);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to update individual');
      }
    } catch (error) {
      if (error.response?.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('Access denied. You do not have permission to update individuals.');
      } else if (error.response?.status === 400) {
        const errorMessage = error.response.data.message || 'Validation failed';
        throw new Error(errorMessage);
      } else if (error.response?.status === 404) {
        throw new Error('Individual not found');
      } else if (error.response?.status === 500) {
        const errorMessage = error.response.data.message || 'Server error occurred';
        throw new Error(`Server Error: ${errorMessage}`);
      }

      throw new Error(error.response?.data?.message || error.message || 'Failed to update individual');
    }
  }

  /**
   * Update individual status
   * @param {string} id - Individual ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated individual
   */
  async updateIndividualStatus(id, status) {
    try {
      const response = await api.patch(`/individuals/${id}/status`, { status });

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to update individual status');
      }
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Individual not found');
      }
      throw new Error(error.response?.data?.message || error.message || 'Failed to update individual status');
    }
  }

  /**
   * Delete individual
   * @param {string} id - Individual ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteIndividual(id) {
    try {
      const response = await api.delete(`/individuals/${id}`);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to delete individual');
      }
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Individual not found');
      }
      throw new Error(error.response?.data?.message || error.message || 'Failed to delete individual');
    }
  }

  /**
   * Get individual statistics
   * @returns {Promise<Object>} Individual statistics
   */
  async getIndividualStats() {
    try {
      const response = await api.get('/individuals/stats/overview');

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch individual statistics');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch individual statistics');
    }
  }

  /**
   * Search individuals
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Search results
   */
  async searchIndividuals(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      };

      return await this.getAllIndividuals(params);
    } catch (error) {
      throw new Error(error.message || 'Failed to search individuals');
    }
  }

  /**
   * Verify individual
   * @param {string} id - Individual ID
   * @returns {Promise<Object>} Verification result
   */
  async verifyIndividual(id) {
    try {
      return await this.updateIndividualStatus(id, 'approved');
    } catch (error) {
      throw new Error(error.message || 'Failed to verify individual');
    }
  }

  /**
   * Reject individual
   * @param {string} id - Individual ID
   * @returns {Promise<Object>} Rejection result
   */
  async rejectIndividual(id) {
    try {
      return await this.updateIndividualStatus(id, 'rejected');
    } catch (error) {
      throw new Error(error.message || 'Failed to reject individual');
    }
  }
}

// Export singleton instance
const individualService = new IndividualService();
export default individualService;
