import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { <PERSON><PERSON>hart3, <PERSON>fresh<PERSON><PERSON>, Loader2, AlertCircle } from "lucide-react";

const DroneCharts = () => {
  const [data, setData] = useState([]);
  const [stats, setStats] = useState({
    totalInventory: 0,
    totalDeployed: 0,
    totalMaintenance: 0
  });
  const [chartType, setChartType] = useState('bar');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch drone analytics data
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5000/api/drones/analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch drone analytics data');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data.monthlyData);
        setStats(result.data.stats);
      } else {
        throw new Error(result.message || 'Failed to fetch drone analytics data');
      }
    } catch (err) {
      console.error('Error fetching drone analytics data:', err);
      setError(err.message);
      // Set fallback data
      setData([]);
      setStats({
        totalInventory: 0,
        totalDeployed: 0,
        totalMaintenance: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();

    // Set up periodic refresh every 30 seconds
    const interval = setInterval(() => {
      setIsUpdating(true);
      fetchAnalyticsData().finally(() => {
        setIsUpdating(false);
      });
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`Month: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="month" stroke="#64748b" fontSize={12} />
            <YAxis stroke="#64748b" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="inventory"
              stroke="#3b82f6"
              strokeWidth={3}
              name="Inventory"
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="deployed"
              stroke="#10b981"
              strokeWidth={3}
              name="Deployed"
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="maintenance"
              stroke="#f59e0b"
              strokeWidth={3}
              name="Maintenance"
              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="month" stroke="#64748b" fontSize={12} />
            <YAxis stroke="#64748b" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area
              type="monotone"
              dataKey="inventory"
              stackId="1"
              stroke="#3b82f6"
              fill="#3b82f6"
              fillOpacity={0.6}
              name="Inventory"
            />
            <Area
              type="monotone"
              dataKey="deployed"
              stackId="1"
              stroke="#10b981"
              fill="#10b981"
              fillOpacity={0.6}
              name="Deployed"
            />
          </AreaChart>
        );

      default:
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="month" stroke="#64748b" fontSize={12} />
            <YAxis stroke="#64748b" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey="inventory"
              fill="#3b82f6"
              name="Inventory"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="deployed"
              fill="#10b981"
              name="Deployed"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="maintenance"
              fill="#f59e0b"
              name="Maintenance"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        );
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Drone Analytics Dashboard
              </h2>
              <p className="text-sm text-gray-500">
                Monthly inventory, deployment & maintenance trends
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 flex items-center justify-center h-[400px]">
          <div className="flex items-center gap-3 text-gray-500">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading drone analytics data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Drone Analytics Dashboard
              </h2>
              <p className="text-sm text-gray-500">
                Monthly inventory, deployment & maintenance trends
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 flex items-center justify-center h-[400px]">
          <div className="flex flex-col items-center gap-3 text-gray-500">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <span className="text-center">
              Failed to load drone analytics data
              <br />
              <button
                onClick={fetchAnalyticsData}
                className="text-blue-600 hover:text-blue-700 underline mt-2"
              >
                Try again
              </button>
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Drone Analytics Dashboard
              </h2>
              <p className="text-sm text-gray-500">
                Monthly inventory, deployment & maintenance trends
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setChartType('bar')}
                className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                  chartType === 'bar'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Bar
              </button>
              <button
                onClick={() => setChartType('line')}
                className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                  chartType === 'line'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Line
              </button>
              <button
                onClick={() => setChartType('area')}
                className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                  chartType === 'area'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Area
              </button>
            </div>

            {isUpdating && (
              <div className="flex items-center gap-1 text-blue-600">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span className="text-xs">Updating...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-6">
        <ResponsiveContainer width="100%" height={350}>
          {renderChart()}
        </ResponsiveContainer>
      </div>

      <div className="px-6 pb-6">
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {stats.totalInventory}
            </div>
            <div className="text-xs text-blue-600 font-medium">Total Inventory</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {stats.totalDeployed}
            </div>
            <div className="text-xs text-green-600 font-medium">Total Deployed</div>
          </div>
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-amber-600">
              {stats.totalMaintenance}
            </div>
            <div className="text-xs text-amber-600 font-medium">In Maintenance</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DroneCharts;
