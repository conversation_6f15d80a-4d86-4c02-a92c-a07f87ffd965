import React, { useState, useMemo, useEffect } from "react";
import {
  Bell,
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw,
  TrendingUp,
  Activity,
  AlertTriangle,
  CheckCircle,
  Settings,
  MoreVertical
} from 'lucide-react';

// React Icons imports
import {
  FaPlane,
  FaBatteryFull,
  FaBatteryHalf,
  FaBatteryQuarter,
  FaTemperatureHigh,
  FaMapMarkerAlt,
  FaRocket,
  FaCog,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaEye,
  FaEdit,
  FaTrash,
  FaPlay,
  FaPause,
  FaStop,
  FaChartLine,
  FaBox,
  FaTools,
  FaCloudUploadAlt
} from 'react-icons/fa';

import { useNavigate } from 'react-router-dom';
import DroneTable from "./DroneTable";
import AdminSidebar from "../common/AdminSidebar";

const Inventory = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDrone, setSelectedDrone] = useState(null);

  // Mock inventory analytics data
  const inventoryStats = useMemo(() => ({
    totalDrones: 156,
    activeDrones: 98,
    deployedDrones: 45,
    maintenanceDrones: 13,
    availableDrones: 53,
    batteryHealthAvg: 87,
    lastUpdated: new Date().toLocaleTimeString()
  }), []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  // Quick Actions handlers
  const handleBulkUpload = () => {
    console.log('Inventory: Navigating to bulk upload');
    navigate('/inventory-bulk-upload');
  };

  const handleViewAnalytics = () => {
    console.log('Inventory: Navigating to analytics dashboard');
    navigate('/inventory-analytics');
  };

  const handleScheduleMaintenance = () => {
    console.log('Inventory: Navigating to maintenance scheduler');
    navigate('/inventory-maintenance');
  };

  const handleInventorySettings = () => {
    console.log('Inventory: Navigating to inventory settings');
    navigate('/inventory-settings');
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />

      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header Section */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <FaPlane className="text-blue-600" />
                Drone Inventory
              </h2>
              <p className="text-gray-600 mt-1">Manage and monitor all drone assets</p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw size={18} className={refreshing ? 'animate-spin' : ''} />
                <span className="hidden sm:inline">Refresh</span>
              </button>
              <button
                onClick={() => {
                  console.log('Inventory: Navigating to add drone form');
                  navigate('/adddrone');
                }}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus size={18} />
                <span className="hidden sm:inline">Add Drone</span>
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Download size={18} />
                <span className="hidden sm:inline">Export</span>
              </button>
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                <Bell size={18} />
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Dashboard */}
        <div className="px-4 lg:px-6 py-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Drones</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{inventoryStats.totalDrones}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FaPlane className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-600 font-medium">+8%</span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-3xl font-bold text-green-600 mt-2">{inventoryStats.activeDrones}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FaCheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Ready for deployment</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Deployed</p>
                  <p className="text-3xl font-bold text-blue-600 mt-2">{inventoryStats.deployedDrones}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FaRocket className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Currently in field</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Maintenance</p>
                  <p className="text-3xl font-bold text-orange-600 mt-2">{inventoryStats.maintenanceDrones}</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <FaTools className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Under repair</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Available</p>
                  <p className="text-3xl font-bold text-purple-600 mt-2">{inventoryStats.availableDrones}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FaBox className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Ready to deploy</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Battery Health</p>
                  <p className="text-3xl font-bold text-green-600 mt-2">{inventoryStats.batteryHealthAvg}%</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FaBatteryFull className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4 text-sm">
                <span className="text-gray-500">Average across fleet</span>
              </div>
            </div>
          </div>

          {/* Search and Filter Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search drones by name, ID, or status..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="All">All Status</option>
                  <option value="Active">Active</option>
                  <option value="Deployed">Deployed</option>
                  <option value="Maintenance">Maintenance</option>
                  <option value="Inactive">Inactive</option>
                </select>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Filter size={16} />
                  Filters
                </button>
              </div>
            </div>
          </div>

          {/* Enhanced Drone Table */}
          <DroneTable
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSort={(field) => {
              if (sortBy === field) {
                setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
              } else {
                setSortBy(field);
                setSortOrder('asc');
              }
            }}
          />

          {/* Quick Actions Panel */}
          <div className="mt-6 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaCog className="text-blue-600" />
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={handleBulkUpload}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 group"
              >
                <FaCloudUploadAlt className="text-blue-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-blue-700">Bulk Upload</div>
                  <div className="text-sm text-gray-500">Import drone data</div>
                </div>
              </button>
              <button
                onClick={handleViewAnalytics}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-300 transition-all duration-200 group"
              >
                <FaChartLine className="text-green-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-green-700">Analytics</div>
                  <div className="text-sm text-gray-500">View detailed reports</div>
                </div>
              </button>
              <button
                onClick={handleScheduleMaintenance}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-orange-50 hover:border-orange-300 transition-all duration-200 group"
              >
                <FaTools className="text-orange-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-orange-700">Maintenance</div>
                  <div className="text-sm text-gray-500">Schedule maintenance</div>
                </div>
              </button>
              <button
                onClick={handleInventorySettings}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 group"
              >
                <Settings className="text-purple-600 text-xl group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 group-hover:text-purple-700">Settings</div>
                  <div className="text-sm text-gray-500">Configure inventory</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inventory;
