# Soft Delete System for Organizations

## Overview
The soft delete system allows organizations to be "deleted" with a 10-day grace period before permanent deletion. During this period, organizations can be restored. This provides safety against accidental deletions while maintaining data integrity.

## How It Works

### 1. **Soft Delete Process**
When you delete an organization:
- Organization is marked as `isDeleted: true`
- `deletedAt` timestamp is set
- `permanentDeleteAt` is set to 10 days from deletion
- `deletedBy` tracks who performed the deletion
- Organization remains in database but is hidden from regular users

### 2. **Visual Indicators in UI**
- **Status Column**: Shows "Deleted (X days left)" with orange styling
- **Timer Display**: Shows "⏰ X days left" in a red badge
- **Restore Button**: Green "Restore" button with rotate icon
- **Same Table**: Everything happens in the same organizations table

### 3. **User Experience**
```
Before Delete: [Organization Name] [Status: Approved] [Actions: Edit | Delete]
After Delete:  [Organization Name] [Status: Deleted (9 days left)] [⏰ 9 days left] [Restore]
After Restore: [Organization Name] [Status: Approved] [Actions: Edit | Delete]
```

## API Endpoints

### Delete Organization (Soft Delete)
```http
DELETE /api/organizations/:id
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "message": "Organization deleted successfully",
  "data": {
    "organization": {
      "id": "...",
      "isDeleted": true,
      "daysUntilPermanentDelete": 10,
      "deletedAt": "2025-07-31T...",
      "permanentDeleteAt": "2025-08-10T..."
    }
  }
}
```

### Restore Organization
```http
POST /api/organizations/:id/restore
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "message": "Organization restored successfully",
  "data": {
    "organization": {
      "id": "...",
      "isDeleted": false,
      "deletedAt": null,
      "permanentDeleteAt": null
    }
  }
}
```

### Get Organizations (includes deleted for admin)
```http
GET /api/organizations
Authorization: Bearer <admin_token>
```
- **Admin users**: See all organizations including deleted ones
- **Regular users**: Only see active organizations

## Database Schema

### Organization Model Fields
```javascript
{
  // ... existing fields ...
  
  // Soft delete fields
  isDeleted: { type: Boolean, default: false },
  deletedAt: { type: Date, default: null },
  deletedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  permanentDeleteAt: { type: Date, default: null }
}
```

### Virtual Fields
```javascript
// Calculated field for UI display
daysUntilPermanentDelete: {
  get: function() {
    if (!this.permanentDeleteAt) return null;
    const now = new Date();
    const diffTime = this.permanentDeleteAt - now;
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }
}

canRestore: {
  get: function() {
    return this.isDeleted && this.daysUntilPermanentDelete > 0;
  }
}
```

## Automatic Cleanup

### Background Job
- **Schedule**: Runs daily at 2:00 AM UTC
- **Function**: Permanently deletes organizations where `permanentDeleteAt < now`
- **Location**: `server/jobs/organizationCleanup.js`
- **Logging**: Logs all permanent deletions for audit trail

### Manual Cleanup (if needed)
```http
POST /api/organizations/cleanup
Authorization: Bearer <admin_token>
```

## Frontend Implementation

### Organization Page Updates
1. **Status Display**: Enhanced to show deletion countdown
2. **Action Buttons**: Conditional rendering based on deletion status
3. **Styling**: Orange badges for deleted status, green restore buttons
4. **Real-time Updates**: Automatic refresh after delete/restore operations

### Service Layer
```javascript
// Enhanced delete service
async deleteOrganization(id) {
  const response = await api.delete(`/organizations/${id}`);
  return {
    success: true,
    data: response.data.data, // Includes timer info
    message: response.data.message
  };
}

// Restore service
async restoreOrganization(id) {
  const response = await api.post(`/organizations/${id}/restore`);
  return response.data;
}
```

## Security & Permissions

### Access Control
- **Delete**: Admin users only
- **Restore**: Admin users only
- **View Deleted**: Admin users only (regular users don't see deleted orgs)

### Validation
- Cannot delete organization with active users
- Cannot restore already active organizations
- Cannot restore expired organizations (past grace period)

## Testing

### Manual Testing
1. Login as admin
2. Delete an organization → See timer and restore button
3. Restore organization → See normal status restored
4. Verify regular users don't see deleted organizations

### Automated Testing
Run the test script:
```bash
node test-soft-delete.js
```

## Benefits

1. **Safety**: 10-day grace period prevents accidental data loss
2. **User-Friendly**: Clear visual indicators and simple restore process
3. **Audit Trail**: Tracks who deleted what and when
4. **Automatic Cleanup**: No manual intervention needed for permanent deletion
5. **Seamless UX**: Everything happens in the same table view
6. **Data Integrity**: Maintains referential integrity during grace period

## Configuration

### Grace Period
To change the 10-day grace period, modify:
```javascript
// In Organization model softDelete method
const gracePeriodDays = 10; // Change this value
```

### Cleanup Schedule
To change cleanup schedule, modify:
```javascript
// In server/jobs/organizationCleanup.js
cron.schedule('0 2 * * *', ...) // Currently 2:00 AM daily
```

This soft delete system provides a perfect balance between data safety and user experience, ensuring that accidental deletions can be easily recovered while maintaining a clean, automated cleanup process.
