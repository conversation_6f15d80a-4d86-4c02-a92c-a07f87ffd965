import React, { useState, useEffect } from 'react';
import {
  Activity as ActivityIcon,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Plane,
  Building,
  Settings,
  Filter
} from 'lucide-react';

const Activity = () => {
  const [activities, setActivities] = useState([
    {
      id: 1,
      type: 'deployment',
      text: "SkyFarm Co. has deployed drone PAPL-DRN-012-ALPHA in Maharashtra.",
      time: "22min ago",
      status: 'success',
      icon: <Plane className="w-4 h-4" />
    },
    {
      id: 2,
      type: 'approval',
      text: "SkyFarm Co. has been approved by the admin.",
      time: "48min ago",
      status: 'success',
      icon: <CheckCircle className="w-4 h-4" />
    },
    {
      id: 3,
      type: 'rejection',
      text: "GreenField Drones's registration has been rejected due to invalid documentation.",
      time: "53min ago",
      status: 'error',
      icon: <XCircle className="w-4 h-4" />
    },
    {
      id: 4,
      type: 'registration',
      text: "AgroTech Solutions has registered and is pending approval.",
      time: "1hour ago",
      status: 'pending',
      icon: <Clock className="w-4 h-4" />
    },
    {
      id: 5,
      type: 'active',
      text: "Drone PRYMAJ024012 is currently active over a farmland in Haryana.",
      time: "1hour ago",
      status: 'info',
      icon: <ActivityIcon className="w-4 h-4" />
    },
    {
      id: 6,
      type: 'update',
      text: "BlueSky Innovations has updated their profile information.",
      time: "1hour ago",
      status: 'info',
      icon: <Building className="w-4 h-4" />
    },
    {
      id: 7,
      type: 'maintenance',
      text: "Drone MAINT-001 has been scheduled for maintenance.",
      time: "2hours ago",
      status: 'warning',
      icon: <Settings className="w-4 h-4" />
    },
    {
      id: 8,
      type: 'deployment',
      text: "TechCrop Ltd. has deployed drone TECH-DRN-045-BETA in Punjab.",
      time: "2hours ago",
      status: 'success',
      icon: <Plane className="w-4 h-4" />
    },
    {
      id: 9,
      type: 'approval',
      text: "AgriDrone Solutions has been approved by the admin.",
      time: "3hours ago",
      status: 'success',
      icon: <CheckCircle className="w-4 h-4" />
    },
    {
      id: 10,
      type: 'registration',
      text: "FarmTech Innovations has registered and is pending approval.",
      time: "3hours ago",
      status: 'pending',
      icon: <Clock className="w-4 h-4" />
    },
    {
      id: 11,
      type: 'maintenance',
      text: "Drone SERV-002 maintenance completed successfully.",
      time: "4hours ago",
      status: 'success',
      icon: <Settings className="w-4 h-4" />
    },
    {
      id: 12,
      type: 'active',
      text: "Drone CROP-SCAN-001 is monitoring wheat fields in Uttar Pradesh.",
      time: "4hours ago",
      status: 'info',
      icon: <ActivityIcon className="w-4 h-4" />
    },
    {
      id: 13,
      type: 'deployment',
      text: "SmartFarm Co. has deployed drone SMART-DRN-078-GAMMA in Rajasthan.",
      time: "5hours ago",
      status: 'success',
      icon: <Plane className="w-4 h-4" />
    },
    {
      id: 14,
      type: 'update',
      text: "CropGuard Technologies has updated their drone inventory.",
      time: "5hours ago",
      status: 'info',
      icon: <Building className="w-4 h-4" />
    },
    {
      id: 15,
      type: 'maintenance',
      text: "Drone MAINT-003 requires urgent maintenance attention.",
      time: "6hours ago",
      status: 'warning',
      icon: <AlertTriangle className="w-4 h-4" />
    }
  ]);

  const [filter, setFilter] = useState('all');

  // Simulate real-time activity updates
  useEffect(() => {
    const interval = setInterval(() => {
      const newActivity = {
        id: Date.now(),
        type: 'deployment',
        text: `Drone ${Math.random().toString(36).substr(2, 9).toUpperCase()} has been deployed.`,
        time: "Just now",
        status: 'success',
        icon: <Plane className="w-4 h-4" />
      };

      setActivities(prev => [newActivity, ...prev.slice(0, 19)]); // Keep up to 20 activities
    }, 30000); // Add new activity every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'warning': return 'text-amber-600 bg-amber-50';
      case 'pending': return 'text-blue-600 bg-blue-50';
      case 'info': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const filteredActivities = filter === 'all'
    ? activities
    : activities.filter(activity => activity.type === filter);

  return (
    <div className="bg-white rounded-xl shadow-lg h-[600px] flex flex-col overflow-hidden">
      {/* Fixed Header */}
      <div className="p-6 border-b border-gray-100 flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <ActivityIcon className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-bold text-gray-800">Recent Activity</h2>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              className="border border-gray-200 text-sm rounded-lg px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-300"
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
            >
              <option value="all">All Activities</option>
              <option value="deployment">Deployments</option>
              <option value="approval">Approvals</option>
              <option value="registration">Registrations</option>
              <option value="maintenance">Maintenance</option>
            </select>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto p-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <div className="space-y-4">
            {filteredActivities.map((activity, index) => (
              <div
                key={activity.id}
                className="flex gap-3 items-start p-3 rounded-lg hover:bg-gray-50 transition-colors border-l-2 border-transparent hover:border-blue-300"
              >
                <div className={`p-2 rounded-full ${getStatusColor(activity.status)}`}>
                  {activity.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-700 leading-relaxed">{activity.text}</p>
                  <span className="text-xs text-gray-400 mt-1 block">{activity.time}</span>
                </div>
              </div>
            ))}
          </div>

          {filteredActivities.length === 0 && (
            <div className="text-center py-8">
              <ActivityIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No activities found for the selected filter.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Activity;
