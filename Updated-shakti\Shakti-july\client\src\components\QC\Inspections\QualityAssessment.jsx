import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  Award,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Star,
  Target,
  Camera,
  Zap
} from 'lucide-react';

const QualityAssessment = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRating, setFilterRating] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [assessments, setAssessments] = useState([]);

  // Initialize with sample assessment data
  React.useEffect(() => {
    if (assessments.length === 0) {
      setAssessments([
        {
          id: 'QA-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          assessor: '<PERSON>',
          date: '2024-01-15',
          time: '14:30',
          location: 'Quality Lab A',
          missionType: 'Aerial Survey',
          overallRating: 9.2,
          criteria: {
            imageQuality: { score: 9.5, notes: 'Excellent clarity and resolution' },
            flightStability: { score: 9.0, notes: 'Smooth flight path with minimal drift' },
            dataAccuracy: { score: 9.3, notes: 'GPS coordinates highly accurate' },
            systemReliability: { score: 8.8, notes: 'Minor communication lag observed' },
            missionCompletion: { score: 9.5, notes: 'All waypoints covered successfully' }
          },
          strengths: ['Exceptional image quality', 'Precise navigation', 'Complete data capture'],
          improvements: ['Communication system optimization needed'],
          recommendations: ['Continue current maintenance schedule', 'Monitor communication system'],
          nextAssessment: '2024-02-15'
        },
        {
          id: 'QA-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          assessor: 'Sarah Johnson',
          date: '2024-01-14',
          time: '11:15',
          location: 'Field Test Area',
          missionType: 'Reconnaissance',
          overallRating: 7.8,
          criteria: {
            imageQuality: { score: 8.2, notes: 'Good quality with minor blur in windy conditions' },
            flightStability: { score: 7.5, notes: 'Some instability in high wind conditions' },
            dataAccuracy: { score: 8.0, notes: 'Acceptable accuracy with minor deviations' },
            systemReliability: { score: 7.8, notes: 'Occasional sensor recalibration required' },
            missionCompletion: { score: 7.5, notes: 'Mission completed with some route adjustments' }
          },
          strengths: ['Adaptable to weather conditions', 'Reliable data collection'],
          improvements: ['Wind resistance needs improvement', 'Sensor stability enhancement'],
          recommendations: ['Upgrade stabilization system', 'Recalibrate sensors monthly'],
          nextAssessment: '2024-02-14'
        },
        {
          id: 'QA-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          assessor: 'Mike Wilson',
          date: '2024-01-13',
          time: '16:45',
          location: 'Quality Lab B',
          missionType: 'Mapping',
          overallRating: 8.7,
          criteria: {
            imageQuality: { score: 9.0, notes: 'High resolution mapping data captured' },
            flightStability: { score: 8.8, notes: 'Excellent stability during mapping runs' },
            dataAccuracy: { score: 8.5, notes: 'Mapping accuracy within acceptable tolerances' },
            systemReliability: { score: 8.3, notes: 'Consistent performance throughout mission' },
            missionCompletion: { score: 9.0, notes: 'Complete area coverage achieved' }
          },
          strengths: ['Superior mapping capabilities', 'Consistent performance', 'High data quality'],
          improvements: ['Battery life optimization', 'Data processing speed'],
          recommendations: ['Consider battery upgrade', 'Optimize flight patterns for efficiency'],
          nextAssessment: '2024-02-13'
        }
      ]);
    }
  }, [assessments.length]);

  // Form state for new assessment
  const [newAssessment, setNewAssessment] = useState({
    droneId: '',
    droneName: '',
    assessor: '',
    date: '',
    time: '',
    location: '',
    missionType: '',
    criteria: {
      imageQuality: { score: 8, notes: '' },
      flightStability: { score: 8, notes: '' },
      dataAccuracy: { score: 8, notes: '' },
      systemReliability: { score: 8, notes: '' },
      missionCompletion: { score: 8, notes: '' }
    },
    strengths: [],
    improvements: [],
    recommendations: [],
    notes: ''
  });

  // Add new assessment function
  const handleAddAssessment = (e) => {
    e.preventDefault();
    
    // Calculate overall rating
    const scores = Object.values(newAssessment.criteria).map(c => c.score);
    const overallRating = (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(1);

    const newAssess = {
      id: `QA-2024-${String(assessments.length + 1).padStart(3, '0')}`,
      ...newAssessment,
      overallRating: parseFloat(overallRating),
      nextAssessment: new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0]
    };
    
    setAssessments([...assessments, newAssess]);
    setNewAssessment({
      droneId: '',
      droneName: '',
      assessor: '',
      date: '',
      time: '',
      location: '',
      missionType: '',
      criteria: {
        imageQuality: { score: 8, notes: '' },
        flightStability: { score: 8, notes: '' },
        dataAccuracy: { score: 8, notes: '' },
        systemReliability: { score: 8, notes: '' },
        missionCompletion: { score: 8, notes: '' }
      },
      strengths: [],
      improvements: [],
      recommendations: [],
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete assessment function
  const handleDeleteAssessment = (id) => {
    setAssessments(assessments.filter(assessment => assessment.id !== id));
  };

  const getRatingColor = (rating) => {
    if (rating >= 9) return 'text-green-600';
    if (rating >= 8) return 'text-blue-600';
    if (rating >= 7) return 'text-yellow-600';
    if (rating >= 6) return 'text-orange-600';
    return 'text-red-600';
  };

  const getRatingBadgeColor = (rating) => {
    if (rating >= 9) return 'bg-green-100 text-green-700 border-green-200';
    if (rating >= 8) return 'bg-blue-100 text-blue-700 border-blue-200';
    if (rating >= 7) return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    if (rating >= 6) return 'bg-orange-100 text-orange-700 border-orange-200';
    return 'bg-red-100 text-red-700 border-red-200';
  };

  const getStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating / 2);
    const hasHalfStar = (rating % 2) >= 1;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />);
    }
    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-3 h-3 fill-yellow-200 text-yellow-400" />);
    }
    const remainingStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-300" />);
    }
    return stars;
  };

  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = assessment.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         assessment.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         assessment.assessor.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRating = filterRating === 'all' || 
                         (filterRating === 'excellent' && assessment.overallRating >= 9) ||
                         (filterRating === 'good' && assessment.overallRating >= 8 && assessment.overallRating < 9) ||
                         (filterRating === 'fair' && assessment.overallRating >= 7 && assessment.overallRating < 8) ||
                         (filterRating === 'poor' && assessment.overallRating < 7);
    const matchesDrone = filterDrone === 'all' || assessment.droneId === filterDrone;
    return matchesSearch && matchesRating && matchesDrone;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search assessments..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterRating}
        onChange={(e) => setFilterRating(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Ratings</option>
        <option value="excellent">Excellent (9.0+)</option>
        <option value="good">Good (8.0-8.9)</option>
        <option value="fair">Fair (7.0-7.9)</option>
        <option value="poor">Poor (&lt;7.0)</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Assessment</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Quality Assessment"
      subtitle="Evaluate and track drone performance quality metrics"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Assessments</p>
                <p className="text-2xl font-bold text-gray-900">{filteredAssessments.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Award className="w-3 h-3" />
                  Completed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Award className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Rating</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAssessments.length > 0 
                    ? (filteredAssessments.reduce((sum, a) => sum + a.overallRating, 0) / filteredAssessments.length).toFixed(1)
                    : '0.0'
                  }
                </p>
                <div className="flex items-center gap-1 mt-1">
                  {getStarRating(filteredAssessments.length > 0 
                    ? filteredAssessments.reduce((sum, a) => sum + a.overallRating, 0) / filteredAssessments.length
                    : 0
                  )}
                </div>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <Star className="w-6 h-6 text-yellow-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Excellent (9.0+)</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAssessments.filter(a => a.overallRating >= 9).length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  High quality
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Needs Improvement</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAssessments.filter(a => a.overallRating < 8).length}
                </p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Action required
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Assessments Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Quality Assessment Records</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage drone performance quality evaluations</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assessment Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Overall Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quality Criteria
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Key Findings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAssessments.map((assessment) => (
                  <tr key={assessment.id} className="hover:bg-gray-50 transition-colors">
                    {/* Assessment Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Award className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{assessment.id}</h4>
                            <span className="text-xs text-gray-500">({assessment.missionType})</span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{assessment.droneId} - {assessment.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {assessment.assessor}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {assessment.date} at {assessment.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {assessment.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Overall Rating */}
                    <td className="px-6 py-4">
                      <div className="text-center">
                        <div className={`text-2xl font-bold ${getRatingColor(assessment.overallRating)}`}>
                          {assessment.overallRating}
                        </div>
                        <div className="flex items-center justify-center gap-1 mt-1">
                          {getStarRating(assessment.overallRating)}
                        </div>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border mt-2 ${getRatingBadgeColor(assessment.overallRating)}`}>
                          {assessment.overallRating >= 9 ? 'Excellent' :
                           assessment.overallRating >= 8 ? 'Good' :
                           assessment.overallRating >= 7 ? 'Fair' : 'Poor'}
                        </span>
                      </div>
                    </td>

                    {/* Quality Criteria */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Camera className="w-3 h-3" />
                            Image Quality
                          </span>
                          <span className={`text-xs font-medium ${getRatingColor(assessment.criteria.imageQuality.score)}`}>
                            {assessment.criteria.imageQuality.score}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Target className="w-3 h-3" />
                            Flight Stability
                          </span>
                          <span className={`text-xs font-medium ${getRatingColor(assessment.criteria.flightStability.score)}`}>
                            {assessment.criteria.flightStability.score}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            Data Accuracy
                          </span>
                          <span className={`text-xs font-medium ${getRatingColor(assessment.criteria.dataAccuracy.score)}`}>
                            {assessment.criteria.dataAccuracy.score}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Zap className="w-3 h-3" />
                            System Reliability
                          </span>
                          <span className={`text-xs font-medium ${getRatingColor(assessment.criteria.systemReliability.score)}`}>
                            {assessment.criteria.systemReliability.score}
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Key Findings */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {assessment.strengths.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Strengths:</p>
                            <div className="space-y-1">
                              {assessment.strengths.slice(0, 2).map((strength, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                                  <span className="text-xs text-green-700">{strength}</span>
                                </div>
                              ))}
                              {assessment.strengths.length > 2 && (
                                <p className="text-xs text-gray-500">+{assessment.strengths.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {assessment.improvements.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Improvements:</p>
                            <div className="space-y-1">
                              {assessment.improvements.slice(0, 2).map((improvement, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                                  <span className="text-xs text-yellow-700">{improvement}</span>
                                </div>
                              ))}
                              {assessment.improvements.length > 2 && (
                                <p className="text-xs text-gray-500">+{assessment.improvements.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-inspections/quality-assessment/${assessment.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors rounded-lg hover:bg-green-50"
                          title="Edit Assessment"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteAssessment(assessment.id)}
                          title="Delete Assessment"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default QualityAssessment;
