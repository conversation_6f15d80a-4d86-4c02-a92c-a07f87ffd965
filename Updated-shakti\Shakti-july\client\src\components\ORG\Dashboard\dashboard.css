/* Dashboard Animations and Styles */

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from left */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from right */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from bottom */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from top */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Scale animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.8s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.6s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.4s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.5s ease-out;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #F7FAFC;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #F7FAFC;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #CBD5E0, #A0AEC0);
  border-radius: 4px;
  border: 1px solid #E2E8F0;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #A0AEC0, #718096);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #F7FAFC;
}

/* Professional scrollbar for activity feed */
.activity-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #3B82F6 #F1F5F9;
}

.activity-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.activity-scrollbar::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 3px;
}

.activity-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  border-radius: 3px;
}

.activity-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1D4ED8, #1E40AF);
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
.btn-hover {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover:hover::before {
  left: 100%;
}

/* Loading spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3B82F6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Gradient backgrounds */
.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .animate-slideInLeft,
  .animate-slideInRight {
    animation: fadeIn 0.6s ease-out;
  }

  /* Mobile-specific adjustments */
  .card-hover:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Reduce motion for mobile */
  .hover-lift:hover {
    transform: translateY(-1px);
  }

  /* Mobile text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile scrollbar - thinner */
  .activity-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Large screen optimizations */
@media (min-width: 1024px) {
  /* Desktop-specific enhancements */
  .hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover-lift:hover,
  .card-hover:hover,
  .btn-hover:hover {
    transform: none;
    box-shadow: initial;
  }

  /* Larger touch targets */
  button, .interactive-element {
    min-height: 44px;
    min-width: 44px;
  }

  /* Disable animations that might cause performance issues */
  .animate-slideInLeft,
  .animate-slideInRight,
  .animate-slideInUp,
  .animate-slideInDown {
    animation: none;
  }
}

/* Custom popup styles for map */
.custom-popup .leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: none;
}

.custom-popup .leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-active {
  background: linear-gradient(135deg, #10B981, #059669);
  color: white;
}

.status-inactive {
  background: linear-gradient(135deg, #6B7280, #4B5563);
  color: white;
}

.status-maintenance {
  background: linear-gradient(135deg, #F59E0B, #D97706);
  color: white;
}

.status-crashed {
  background: linear-gradient(135deg, #EF4444, #DC2626);
  color: white;
}

/* Progress bar animations */
.progress-bar {
  transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Notification badge */
.notification-badge {
  animation: pulse 2s infinite;
}

/* Chart container styles */
.chart-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Interactive elements */
.interactive-element {
  cursor: pointer;
  transition: all 0.2s ease;
}

.interactive-element:hover {
  transform: scale(1.02);
}

.interactive-element:active {
  transform: scale(0.98);
}

/* Layout fixes to prevent overlapping */
.dashboard-section {
  position: relative;
  z-index: 1;
  margin-bottom: 1.5rem;
}

/* Equal height grid containers */
.dashboard-section .grid {
  align-items: stretch;
}

/* Content containers with proper height management */
.content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.content-container .recharts-wrapper {
  height: 100% !important;
  flex: 1;
}

.content-container .recharts-surface {
  height: 100% !important;
}

.performance-container {
  min-height: auto;
  height: auto;
}

/* Prevent chart overflow */
.recharts-wrapper {
  overflow: visible !important;
}

/* Ensure proper spacing between sections */
.dashboard-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1280px) {
  .dashboard-grid {
    grid-template-columns: 2fr 1fr;
  }
}

/* Fix for responsive containers */
.responsive-container {
  width: 100% !important;
  height: 100% !important;
}

/* Prevent content from breaking out of containers */
.content-container {
  overflow: hidden;
  position: relative;
}

/* Ensure proper stacking context */
.dashboard-card {
  position: relative;
  z-index: 1;
  isolation: isolate;
}

/* Fix for mobile layout issues */
@media (max-width: 768px) {
  .dashboard-section {
    margin-bottom: 1rem;
  }

  .content-container {
    min-height: 300px;
  }
}

/* Ensure proper grid alignment */
.dashboard-section .grid {
  display: grid;
  align-items: stretch;
}

/* Professional Map and Activity Feed Layout - Equal Heights */
.dashboard-section .grid > div {
  display: flex;
  flex-direction: column;
  height: 480px; /* Fixed height for both containers */
}

/* Map container - fills available space */
.dashboard-section .grid > div:first-child {
  height: 480px;
}

/* Activity feed - with scrollable content */
.dashboard-section .grid > div:last-child {
  height: 480px;
  overflow: hidden;
}

/* Professional scrollbar styling */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Activity Feed Scrollbar - Professional Dynamic Style */
.activity-feed-content {
  overflow-y: auto;
  height: calc(100% - 60px); /* Account for header */
  padding-right: 8px;
}

.activity-feed-content::-webkit-scrollbar {
  width: 10px;
}

.activity-feed-content::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.8);
  border-radius: 6px;
  margin: 4px 0;
}

.activity-feed-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.activity-feed-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
  transform: scale(1.05);
}

.activity-feed-content::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #1d4ed8, #1e3a8a);
}

/* Sprayed Hectares Section Styling */
.sprayed-hectares-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 1.5rem;
  padding: 1.5rem;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Professional hover effects for stat cards */
.stat-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Progress bar animations */
.progress-bar-animated {
  transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pulse animation for live indicators */
@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive adjustments */
@media (max-width: 1279px) {
  .dashboard-section .grid > div {
    height: 450px;
  }

  .dashboard-section .grid > div:first-child,
  .dashboard-section .grid > div:last-child {
    height: 450px;
  }

  .activity-feed-content {
    height: calc(100% - 120px); /* Adjust for smaller screens */
  }
}

@media (max-width: 768px) {
  .dashboard-section .grid > div {
    height: 400px;
  }

  .dashboard-section .grid > div:first-child,
  .dashboard-section .grid > div:last-child {
    height: 400px;
  }

  .activity-feed-content {
    height: calc(100% - 110px); /* Adjust for mobile */
  }
}

/* Professional animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideInDown {
  animation: slideInDown 0.3s ease-out;
}

/* Professional hover effects */
.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Professional focus states */
select:focus,
button:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
