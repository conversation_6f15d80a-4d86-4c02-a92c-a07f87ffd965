import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Settings,
  Bell,
  Shield,
  Database,
  CheckCircle,
  Save,
  RefreshCw,
  MapPin,
  Clock,
  Users,
  Zap
} from 'lucide-react';
import {
  FaCog,
  FaBell,
  FaShieldAlt,
  FaDatabase,
  FaCheckCircle,
  FaSave,
  FaMapMarkerAlt,
  FaClock,
  FaUsers,
  FaBolt,
  FaRocket
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const DeploymentSettings = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // General Settings State
  const [generalSettings, setGeneralSettings] = useState({
    autoAssignDrones: true,
    defaultDeploymentDuration: 60,
    maxConcurrentDeployments: 10,
    enableGeofencing: true,
    requireApproval: false,
    autoReturnHome: true,
    emergencyLanding: true
  });

  // Notification Settings State
  const [notificationSettings, setNotificationSettings] = useState({
    deploymentStart: true,
    deploymentComplete: true,
    emergencyAlerts: true,
    batteryWarnings: true,
    weatherAlerts: true,
    maintenanceReminders: true,
    emailNotifications: true,
    smsNotifications: false
  });

  // Safety Settings State
  const [safetySettings, setSafetySettings] = useState({
    minBatteryLevel: 25,
    maxWindSpeed: 15,
    maxAltitude: 120,
    noFlyZoneBuffer: 100,
    weatherCheckInterval: 15,
    emergencyLandingEnabled: true
  });

  const tabs = [
    { id: 'general', label: 'General', icon: <FaCog className="w-4 h-4" /> },
    { id: 'notifications', label: 'Notifications', icon: <FaBell className="w-4 h-4" /> },
    { id: 'safety', label: 'Safety', icon: <FaShieldAlt className="w-4 h-4" /> }
  ];

  const handleSave = async () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
      console.log('Deployment settings saved:', {
        general: generalSettings,
        notifications: notificationSettings,
        safety: safetySettings
      });
    }, 1500);
  };

  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset all settings to default values?')) {
      setGeneralSettings({
        autoAssignDrones: true,
        defaultDeploymentDuration: 60,
        maxConcurrentDeployments: 10,
        enableGeofencing: true,
        requireApproval: false,
        autoReturnHome: true,
        emergencyLanding: true
      });
      
      setNotificationSettings({
        deploymentStart: true,
        deploymentComplete: true,
        emergencyAlerts: true,
        batteryWarnings: true,
        weatherAlerts: true,
        maintenanceReminders: true,
        emailNotifications: true,
        smsNotifications: false
      });
      
      setSafetySettings({
        minBatteryLevel: 25,
        maxWindSpeed: 15,
        maxAltitude: 120,
        noFlyZoneBuffer: 100,
        weatherCheckInterval: 15,
        emergencyLandingEnabled: true
      });
    }
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <FaRocket className="text-green-600" />
            Deployment Settings
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Auto-assign Drones</label>
              <input
                type="checkbox"
                checked={generalSettings.autoAssignDrones}
                onChange={(e) => setGeneralSettings({...generalSettings, autoAssignDrones: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Default Duration (minutes)
              </label>
              <input
                type="number"
                min="10"
                max="480"
                value={generalSettings.defaultDeploymentDuration}
                onChange={(e) => setGeneralSettings({...generalSettings, defaultDeploymentDuration: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Concurrent Deployments
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={generalSettings.maxConcurrentDeployments}
                onChange={(e) => setGeneralSettings({...generalSettings, maxConcurrentDeployments: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Enable Geofencing</label>
              <input
                type="checkbox"
                checked={generalSettings.enableGeofencing}
                onChange={(e) => setGeneralSettings({...generalSettings, enableGeofencing: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Settings className="w-5 h-5 text-gray-600" />
            Control Settings
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Require Approval</label>
              <input
                type="checkbox"
                checked={generalSettings.requireApproval}
                onChange={(e) => setGeneralSettings({...generalSettings, requireApproval: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Auto Return Home</label>
              <input
                type="checkbox"
                checked={generalSettings.autoReturnHome}
                onChange={(e) => setGeneralSettings({...generalSettings, autoReturnHome: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Emergency Landing</label>
              <input
                type="checkbox"
                checked={generalSettings.emergencyLanding}
                onChange={(e) => setGeneralSettings({...generalSettings, emergencyLanding: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Bell className="w-5 h-5 text-yellow-600" />
            Alert Types
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Deployment Start</label>
              <input
                type="checkbox"
                checked={notificationSettings.deploymentStart}
                onChange={(e) => setNotificationSettings({...notificationSettings, deploymentStart: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Deployment Complete</label>
              <input
                type="checkbox"
                checked={notificationSettings.deploymentComplete}
                onChange={(e) => setNotificationSettings({...notificationSettings, deploymentComplete: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Emergency Alerts</label>
              <input
                type="checkbox"
                checked={notificationSettings.emergencyAlerts}
                onChange={(e) => setNotificationSettings({...notificationSettings, emergencyAlerts: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Battery Warnings</label>
              <input
                type="checkbox"
                checked={notificationSettings.batteryWarnings}
                onChange={(e) => setNotificationSettings({...notificationSettings, batteryWarnings: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-600" />
            Delivery Methods
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Email Notifications</label>
              <input
                type="checkbox"
                checked={notificationSettings.emailNotifications}
                onChange={(e) => setNotificationSettings({...notificationSettings, emailNotifications: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">SMS Notifications</label>
              <input
                type="checkbox"
                checked={notificationSettings.smsNotifications}
                onChange={(e) => setNotificationSettings({...notificationSettings, smsNotifications: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSafetySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Zap className="w-5 h-5 text-red-600" />
            Safety Thresholds
          </h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Battery Level (%)
              </label>
              <input
                type="number"
                min="10"
                max="50"
                value={safetySettings.minBatteryLevel}
                onChange={(e) => setSafetySettings({...safetySettings, minBatteryLevel: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Wind Speed (m/s)
              </label>
              <input
                type="number"
                min="5"
                max="30"
                value={safetySettings.maxWindSpeed}
                onChange={(e) => setSafetySettings({...safetySettings, maxWindSpeed: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Altitude (m)
              </label>
              <input
                type="number"
                min="50"
                max="400"
                value={safetySettings.maxAltitude}
                onChange={(e) => setSafetySettings({...safetySettings, maxAltitude: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-purple-600" />
            Zone Settings
          </h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                No-Fly Zone Buffer (m)
              </label>
              <input
                type="number"
                min="50"
                max="500"
                value={safetySettings.noFlyZoneBuffer}
                onChange={(e) => setSafetySettings({...safetySettings, noFlyZoneBuffer: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Weather Check Interval (min)
              </label>
              <input
                type="number"
                min="5"
                max="60"
                value={safetySettings.weatherCheckInterval}
                onChange={(e) => setSafetySettings({...safetySettings, weatherCheckInterval: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Emergency Landing Enabled</label>
              <input
                type="checkbox"
                checked={safetySettings.emergencyLandingEnabled}
                onChange={(e) => setSafetySettings({...safetySettings, emergencyLandingEnabled: e.target.checked})}
                className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'safety':
        return renderSafetySettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admindeployment')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Deployment</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaCog className="text-green-600" />
                  Deployment Settings
                </h2>
                <p className="text-gray-600 mt-1">Configure deployment system preferences and safety parameters</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {showSuccess && (
                <div className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg">
                  <CheckCircle size={16} />
                  <span className="text-sm">Settings saved successfully!</span>
                </div>
              )}
              
              <button
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw size={18} />
                <span className="hidden sm:inline">Reset</span>
              </button>
              
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {isSaving ? (
                  <RefreshCw size={18} className="animate-spin" />
                ) : (
                  <Save size={18} />
                )}
                <span className="hidden sm:inline">{isSaving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6" aria-label="Tabs">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {renderActiveTab()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeploymentSettings;
