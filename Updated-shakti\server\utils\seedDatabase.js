require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const connectDB = require('../config/database');

const defaultUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    profile: {
      adminLevel: 'super'
    }
  },
  {
    username: 'salam<PERSON>an',
    email: '<EMAIL>',
    password: 'Org123!',
    role: 'org',
    profile: {
      organizationId: null,
      organizationName: '<PERSON><PERSON>'
    }
  },
  {
    username: 'maintenance',
    email: '<EMAIL>',
    password: 'Maint123!',
    role: 'maintenance',
    profile: {
      department: 'Quality Control',
      certifications: ['Drone Maintenance', 'Quality Assurance']
    }
  },
  {
    username: 'testorg',
    email: '<EMAIL>',
    password: 'Test123!',
    role: 'org',
    profile: {
      organizationId: null,
      organizationName: 'Test Organization'
    }
  }
];

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    
    // Clear existing users (optional - comment out if you want to keep existing data)
    console.log('🗑️ Clearing existing users...');
    await User.deleteMany({});
    
    // Create default users
    console.log('👥 Creating default users...');
    
    for (const userData of defaultUsers) {
      try {
        const user = new User(userData);
        await user.save();
        console.log(`✅ Created user: ${userData.username} (${userData.role})`);
      } catch (error) {
        console.error(`❌ Failed to create user ${userData.username}:`, error.message);
      }
    }
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📋 Default Login Credentials:');
    console.log('┌─────────────┬─────────────────────┬─────────────┬─────────────┐');
    console.log('│ Role        │ Username            │ Password    │ Email       │');
    console.log('├─────────────┼─────────────────────┼─────────────┼─────────────┤');
    console.log('│ Admin       │ admin               │ Admin123!   │ <EMAIL> │');
    console.log('│ Organization│ salamkisan          │ Org123!     │ <EMAIL> │');
    console.log('│ Maintenance │ maintenance         │ Maint123!   │ <EMAIL> │');
    console.log('│ Test Org    │ testorg             │ Test123!    │ <EMAIL> │');
    console.log('└─────────────┴─────────────────────┴─────────────┴─────────────┘');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
