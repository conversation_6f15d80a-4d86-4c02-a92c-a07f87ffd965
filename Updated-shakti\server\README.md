# SHAKTI Drone Management - Backend API

## 🚀 Overview

This is the backend API for the SHAKTI (Safety High Accuracy Aerial Kinematic Tracking Integration) Drone Management System. It provides authentication, user management, and API endpoints for the drone management frontend application.

## 🏗️ Architecture

- **Framework**: Node.js with Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: Helmet, CORS, Rate Limiting
- **Validation**: Express Validator
- **Password Hashing**: bcryptjs

## 📁 Project Structure

```
server/
├── config/
│   └── database.js          # MongoDB connection configuration
├── controllers/
│   └── authController.js    # Authentication logic
├── middleware/
│   ├── auth.js             # Authentication & authorization middleware
│   ├── security.js         # Security middleware (CORS, rate limiting)
│   └── validation.js       # Input validation middleware
├── models/
│   └── User.js             # User model with role-based access
├── routes/
│   ├── auth.js             # Authentication routes
│   └── index.js            # Main route handler
├── utils/
│   ├── jwt.js              # JWT utility functions
│   ├── response.js         # Standardized API responses
│   └── seedDatabase.js     # Database seeding utility
├── .env                    # Environment variables
├── .env.example            # Environment variables template
├── package.json            # Dependencies and scripts
└── server.js               # Main application entry point
```

## 🔧 Installation & Setup

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- npm or yarn package manager

### 1. Install Dependencies

```bash
cd server
npm install
```

### 2. Environment Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shakti

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRE=7d

# CORS Configuration
CLIENT_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
```

### 3. Database Setup

Make sure MongoDB is running, then seed the database with default users:

```bash
npm run seed
```

This will create default users for testing:

| Role         | Username    | Password  | Email                    |
|-------------|-------------|-----------|--------------------------|
| Admin       | admin       | Admin123! | <EMAIL>         |
| Organization| salamkisan  | Org123!   | <EMAIL>   |
| Maintenance | maintenance | Maint123! | <EMAIL>   |
| Test Org    | testorg     | Test123!  | <EMAIL>         |

### 4. Start the Server

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

The server will start on `http://localhost:5000`

## 📡 API Endpoints

### Health Check
- **GET** `/api/health` - Server health status

### Authentication
- **POST** `/api/auth/login` - User login
- **POST** `/api/auth/register` - Register new user (Admin only)
- **GET** `/api/auth/me` - Get current user profile
- **PUT** `/api/auth/profile` - Update user profile
- **PUT** `/api/auth/change-password` - Change password
- **POST** `/api/auth/logout` - User logout

## 🔐 Authentication & Authorization

### JWT Token Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### User Roles

1. **Admin** (`admin`)
   - Full system access
   - Can create/manage users
   - Access to all admin features

2. **Organization** (`org`)
   - Organization-specific access
   - Drone fleet management
   - Limited to organization data

3. **Maintenance** (`maintenance`)
   - Quality control access
   - Maintenance scheduling
   - Diagnostic tools

### Role-Based Access Control

Routes are protected using middleware:
- `authenticate` - Requires valid JWT token
- `adminOnly` - Admin role required
- `orgOnly` - Organization role required
- `maintenanceOnly` - Maintenance role required

## 🛡️ Security Features

### Rate Limiting
- General API: 100 requests per 15 minutes
- Authentication: 5 attempts per 15 minutes

### Password Security
- Minimum 6 characters
- bcrypt hashing with 12 salt rounds
- Account lockout after 5 failed attempts

### CORS Protection
- Configured for frontend origin
- Credentials support enabled

### Security Headers
- Helmet.js for security headers
- Content Security Policy
- HSTS enabled

## 🧪 Testing the API

### Using curl

#### Login
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123!",
    "role": "admin"
  }'
```

#### Get Profile (with token)
```bash
curl -X GET http://localhost:5000/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using Postman

1. Import the API collection (if available)
2. Set base URL to `http://localhost:5000/api`
3. For protected routes, add Authorization header with Bearer token

## 🚨 Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [...], // Detailed errors if applicable
  "timestamp": "2025-01-29T10:30:00.000Z"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## 🔄 Development

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with default users

### Adding New Routes

1. Create controller in `controllers/`
2. Add route in `routes/`
3. Apply appropriate middleware
4. Update documentation

### Database Models

User model includes:
- Basic authentication fields
- Role-based profile data
- Account security features
- Timestamps and indexing

## 🚀 Deployment

### Environment Variables for Production

```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/shakti_production
JWT_SECRET=very_secure_random_string_for_production
CLIENT_URL=https://your-frontend-domain.com
```

### Security Checklist

- [ ] Change default JWT secret
- [ ] Use strong MongoDB credentials
- [ ] Enable MongoDB authentication
- [ ] Configure proper CORS origins
- [ ] Set up SSL/TLS certificates
- [ ] Configure reverse proxy (nginx)
- [ ] Set up monitoring and logging

## 📞 Support

For issues and questions:
- Check the logs for error details
- Verify environment configuration
- Ensure MongoDB is running and accessible
- Check network connectivity between frontend and backend

## 🔄 Version History

- **v1.0.0** - Initial release with authentication system

## 📋 API Testing Examples

### Complete Login Flow Test

```bash
# 1. Test health endpoint
curl http://localhost:5000/api/health

# 2. Login as admin
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123!"
  }'

# 3. Use the returned token for authenticated requests
export TOKEN="your_jwt_token_here"

# 4. Get user profile
curl -X GET http://localhost:5000/api/auth/me \
  -H "Authorization: Bearer $TOKEN"

# 5. Update profile
curl -X PUT http://localhost:5000/api/auth/profile \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### Frontend Integration Test

```javascript
// Test the authService from browser console
import authService from './services/authService';

// Test login
authService.login('admin', 'Admin123!')
  .then(response => console.log('Login success:', response))
  .catch(error => console.error('Login error:', error));
```
