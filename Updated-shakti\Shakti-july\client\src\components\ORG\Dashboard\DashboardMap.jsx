import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { useNavigate } from "react-router-dom";
import {
  MapPin,
  Layers,
  Maximize2,
  RotateCcw,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react';

// Enhanced custom icons for different drone statuses
const createDroneIcon = (status) => {
  const colors = {
    'Active': '#10B981',
    'Inactive': '#6B7280',
    'Crashed': '#EF4444',
    'Maintenance': '#F59E0B',
    'Flying': '#3B82F6'
  };

  return new L.DivIcon({
    html: `
      <div style="
        width: 40px;
        height: 40px;
        background: ${colors[status] || '#6B7280'};
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: ${status === 'Active' ? 'pulse 2s infinite' : 'none'};
      ">
        <div style="color: white; font-size: 16px;">🚁</div>
      </div>
      <style>
        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 ${colors[status]}40; }
          70% { box-shadow: 0 0 0 10px ${colors[status]}00; }
          100% { box-shadow: 0 0 0 0 ${colors[status]}00; }
        }
      </style>
    `,
    className: 'custom-drone-marker',
    iconSize: [40, 40],
    iconAnchor: [20, 20],
    popupAnchor: [0, -20]
  });
};

// Enhanced drone data with more details
const initialDrones = [
  {
    id: 1,
    lat: 19.0760,
    lng: 72.8777,
    name: 'Drone Arjuna',
    status: 'Active',
    pilot: 'Deepchand Jaiswal',
    battery: 85,
    altitude: 120,
    speed: 45,
    mission: 'Crop Spraying',
    area: 'Mumbai, Maharashtra',
    lastUpdate: new Date()
  },
  {
    id: 2,
    lat: 28.6139,
    lng: 77.2090,
    name: 'Drone Tejas',
    status: 'Flying',
    pilot: 'Yuvraj Khade',
    battery: 67,
    altitude: 95,
    speed: 38,
    mission: 'Surveillance',
    area: 'Delhi',
    lastUpdate: new Date()
  },
  {
    id: 3,
    lat: 17.3850,
    lng: 78.4867,
    name: 'Drone Vikrant',
    status: 'Crashed',
    pilot: 'Vishal Shelke',
    battery: 0,
    altitude: 0,
    speed: 0,
    mission: 'Emergency Landing',
    area: 'Hyderabad, Telangana',
    lastUpdate: new Date()
  },
  {
    id: 4,
    lat: 13.0827,
    lng: 80.2707,
    name: 'Drone Karna',
    status: 'Maintenance',
    pilot: 'Om Unge',
    battery: 100,
    altitude: 0,
    speed: 0,
    mission: 'Scheduled Maintenance',
    area: 'Chennai, Tamil Nadu',
    lastUpdate: new Date()
  },
  {
    id: 5,
    lat: 21.1458,
    lng: 79.0882,
    name: 'Drone Bhima',
    status: 'Active',
    pilot: 'Prathamesh Jadhav',
    battery: 92,
    altitude: 110,
    speed: 42,
    mission: 'Fertilizer Spraying',
    area: 'Nagpur, Maharashtra',
    lastUpdate: new Date()
  }
];

const DashboardMap = () => {
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [drones, setDrones] = useState(initialDrones);
  const [mapView, setMapView] = useState('satellite');
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const navigate = useNavigate();
  const center = [20.5937, 78.9629];

  // Simulate real-time drone updates
  useEffect(() => {
    const interval = setInterval(() => {
      setDrones(prevDrones =>
        prevDrones.map(drone => ({
          ...drone,
          lat: drone.lat + (Math.random() - 0.5) * 0.01,
          lng: drone.lng + (Math.random() - 0.5) * 0.01,
          battery: Math.max(0, Math.min(100, drone.battery + (Math.random() - 0.5) * 5)),
          speed: drone.status === 'Active' || drone.status === 'Flying'
            ? Math.max(0, Math.min(60, drone.speed + (Math.random() - 0.5) * 10))
            : 0,
          lastUpdate: new Date()
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const filteredDrones = selectedStatus === 'All'
    ? drones
    : drones.filter((drone) => drone.status === selectedStatus);

  const getStatusIcon = (status) => {
    switch(status) {
      case 'Active': return <CheckCircle size={16} className="text-green-600" />;
      case 'Flying': return <Zap size={16} className="text-blue-600" />;
      case 'Crashed': return <AlertTriangle size={16} className="text-red-600" />;
      case 'Maintenance': return <Settings size={16} className="text-yellow-600" />;
      default: return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch(status) {
      case 'Active': return 'text-green-600 bg-green-100';
      case 'Flying': return 'text-blue-600 bg-blue-100';
      case 'Crashed': return 'text-red-600 bg-red-100';
      case 'Maintenance': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const tileLayerUrls = {
    satellite: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
    street: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    terrain: "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
  };

  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-100 h-full flex flex-col ${
      isFullscreen ? 'fixed inset-4 z-50' : ''
    }`}>
      {/* Professional Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <MapPin className="text-blue-600" size={20} />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-800">Live Drone Tracking</h2>
            <p className="text-sm text-gray-500">Real-time fleet monitoring</p>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          {/* Map View Toggle */}
          <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            {Object.keys(tileLayerUrls).map((view) => (
              <button
                key={view}
                onClick={() => setMapView(view)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors capitalize ${
                  mapView === view
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                {view}
              </button>
            ))}
          </div>

          {/* Status Filter */}
          <select
            className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="All">All Drones ({filteredDrones.length})</option>
            <option value="Active">Active ({drones.filter(d => d.status === 'Active').length})</option>
            <option value="Flying">Flying ({drones.filter(d => d.status === 'Flying').length})</option>
            <option value="Inactive">Inactive ({drones.filter(d => d.status === 'Inactive').length})</option>
            <option value="Maintenance">Maintenance ({drones.filter(d => d.status === 'Maintenance').length})</option>
            <option value="Crashed">Crashed ({drones.filter(d => d.status === 'Crashed').length})</option>
          </select>

          {/* Control Buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowHeatmap(!showHeatmap)}
              className={`p-2 rounded-lg transition-colors ${
                showHeatmap ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title="Toggle Heatmap"
            >
              <Layers size={18} />
            </button>

            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
              title="Toggle Fullscreen"
            >
              <Maximize2 size={18} />
            </button>

            <button
              onClick={() => navigate("/dronetrackingpage")}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Full Map View
            </button>
          </div>
        </div>
      </div>

      {/* Map Container - Auto-resize to fit content */}
      <div className="relative flex-1 overflow-hidden rounded-b-2xl">
        <MapContainer
          center={center}
          zoom={5}
          scrollWheelZoom={true}
          style={{
            width: '100%',
            height: '100%',
            minHeight: '420px',
            borderRadius: '0 0 1rem 1rem',
            zIndex: 1
          }}
        >
          <TileLayer
            url={tileLayerUrls[mapView]}
            attribution={
              mapView === 'satellite'
                ? 'Tiles &copy; <a href="https://www.esri.com/">Esri</a>'
                : mapView === 'terrain'
                ? '&copy; <a href="https://opentopomap.org">OpenTopoMap</a>'
                : '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
            }
          />

          {filteredDrones.map((drone) => (
            <Marker
              key={drone.id}
              position={[drone.lat, drone.lng]}
              icon={createDroneIcon(drone.status)}
            >
              <Popup className="custom-popup">
                <div className="p-2 min-w-[250px]">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-bold text-gray-800">{drone.name}</h3>
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(drone.status)}`}>
                      {getStatusIcon(drone.status)}
                      {drone.status}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-gray-500">Pilot:</span>
                      <p className="font-medium">{drone.pilot}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Mission:</span>
                      <p className="font-medium">{drone.mission}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Battery:</span>
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              drone.battery > 50 ? 'bg-green-500' :
                              drone.battery > 20 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${drone.battery}%` }}
                          ></div>
                        </div>
                        <span className="text-xs font-medium">{drone.battery}%</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">Speed:</span>
                      <p className="font-medium">{drone.speed} km/h</p>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{drone.area}</span>
                      <span>Updated: {drone.lastUpdate.toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
              </Popup>
            </Marker>
          ))}

          {/* Add coverage circles for active drones */}
          {showHeatmap && filteredDrones
            .filter(drone => drone.status === 'Active' || drone.status === 'Flying')
            .map(drone => (
              <Circle
                key={`circle-${drone.id}`}
                center={[drone.lat, drone.lng]}
                radius={5000}
                pathOptions={{
                  color: drone.status === 'Active' ? '#10B981' : '#3B82F6',
                  fillColor: drone.status === 'Active' ? '#10B981' : '#3B82F6',
                  fillOpacity: 0.1,
                  weight: 2
                }}
              />
            ))
          }
        </MapContainer>

        {/* Live Status Indicator */}
        <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">Live Tracking</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {filteredDrones.filter(d => d.status === 'Active' || d.status === 'Flying').length} active drones
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardMap;
