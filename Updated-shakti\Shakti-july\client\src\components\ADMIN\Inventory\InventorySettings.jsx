import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Settings,
  Bell,
  Shield,
  Database,
  AlertTriangle,
  CheckCircle,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Mail,
  Smartphone,
  Clock,
  BarChart3,
  Users,
  Zap
} from 'lucide-react';
import {
  FaCog,
  FaBell,
  FaShieldAlt,
  FaDatabase,
  FaExclamationTriangle,
  FaCheckCircle,
  FaSave,
  FaEye,
  FaEyeSlash,
  FaEnvelope,
  FaMobile,
  FaClock,
  FaChartBar,
  FaUsers,
  FaBolt
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const InventorySettings = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // General Settings State
  const [generalSettings, setGeneralSettings] = useState({
    autoRefresh: true,
    refreshInterval: 30,
    defaultView: 'grid',
    itemsPerPage: 25,
    showInactiveItems: false,
    enableBulkActions: true,
    compactMode: false
  });

  // Notification Settings State
  const [notificationSettings, setNotificationSettings] = useState({
    lowStockAlerts: true,
    maintenanceDue: true,
    batteryWarnings: true,
    systemUpdates: false,
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    notificationSound: true
  });

  // Threshold Settings State
  const [thresholdSettings, setThresholdSettings] = useState({
    lowBatteryThreshold: 20,
    criticalBatteryThreshold: 10,
    maintenanceWarningDays: 7,
    inactivityThreshold: 30,
    temperatureWarning: 60,
    flightHoursWarning: 100
  });

  // Security Settings State
  const [securitySettings, setSecuritySettings] = useState({
    requireApprovalForChanges: true,
    enableAuditLog: true,
    sessionTimeout: 60,
    twoFactorAuth: false,
    passwordComplexity: 'medium',
    dataEncryption: true
  });

  // Data Management Settings State
  const [dataSettings, setDataSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    retentionPeriod: 365,
    exportFormat: 'csv',
    compressionEnabled: true,
    cloudSync: false
  });

  const tabs = [
    { id: 'general', label: 'General', icon: <FaCog className="w-4 h-4" /> },
    { id: 'notifications', label: 'Notifications', icon: <FaBell className="w-4 h-4" /> },
    { id: 'thresholds', label: 'Thresholds', icon: <FaExclamationTriangle className="w-4 h-4" /> },
    { id: 'security', label: 'Security', icon: <FaShieldAlt className="w-4 h-4" /> },
    { id: 'data', label: 'Data Management', icon: <FaDatabase className="w-4 h-4" /> }
  ];

  const handleSave = async () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
      console.log('Settings saved:', {
        general: generalSettings,
        notifications: notificationSettings,
        thresholds: thresholdSettings,
        security: securitySettings,
        data: dataSettings
      });
    }, 1500);
  };

  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset all settings to default values?')) {
      // Reset to default values
      setGeneralSettings({
        autoRefresh: true,
        refreshInterval: 30,
        defaultView: 'grid',
        itemsPerPage: 25,
        showInactiveItems: false,
        enableBulkActions: true,
        compactMode: false
      });
      
      setNotificationSettings({
        lowStockAlerts: true,
        maintenanceDue: true,
        batteryWarnings: true,
        systemUpdates: false,
        emailNotifications: true,
        smsNotifications: false,
        pushNotifications: true,
        notificationSound: true
      });
      
      setThresholdSettings({
        lowBatteryThreshold: 20,
        criticalBatteryThreshold: 10,
        maintenanceWarningDays: 7,
        inactivityThreshold: 30,
        temperatureWarning: 60,
        flightHoursWarning: 100
      });
      
      setSecuritySettings({
        requireApprovalForChanges: true,
        enableAuditLog: true,
        sessionTimeout: 60,
        twoFactorAuth: false,
        passwordComplexity: 'medium',
        dataEncryption: true
      });
      
      setDataSettings({
        autoBackup: true,
        backupFrequency: 'daily',
        retentionPeriod: 365,
        exportFormat: 'csv',
        compressionEnabled: true,
        cloudSync: false
      });
    }
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-blue-600" />
            Display Settings
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Auto Refresh</label>
              <input
                type="checkbox"
                checked={generalSettings.autoRefresh}
                onChange={(e) => setGeneralSettings({...generalSettings, autoRefresh: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Refresh Interval (seconds)
              </label>
              <input
                type="number"
                min="10"
                max="300"
                value={generalSettings.refreshInterval}
                onChange={(e) => setGeneralSettings({...generalSettings, refreshInterval: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Default View
              </label>
              <select
                value={generalSettings.defaultView}
                onChange={(e) => setGeneralSettings({...generalSettings, defaultView: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="grid">Grid View</option>
                <option value="list">List View</option>
                <option value="table">Table View</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Items Per Page
              </label>
              <select
                value={generalSettings.itemsPerPage}
                onChange={(e) => setGeneralSettings({...generalSettings, itemsPerPage: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Settings className="w-5 h-5 text-gray-600" />
            Behavior Settings
          </h4>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Show Inactive Items</label>
              <input
                type="checkbox"
                checked={generalSettings.showInactiveItems}
                onChange={(e) => setGeneralSettings({...generalSettings, showInactiveItems: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Enable Bulk Actions</label>
              <input
                type="checkbox"
                checked={generalSettings.enableBulkActions}
                onChange={(e) => setGeneralSettings({...generalSettings, enableBulkActions: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Compact Mode</label>
              <input
                type="checkbox"
                checked={generalSettings.compactMode}
                onChange={(e) => setGeneralSettings({...generalSettings, compactMode: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Bell className="w-5 h-5 text-yellow-600" />
            Alert Types
          </h4>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Low Stock Alerts</label>
              <input
                type="checkbox"
                checked={notificationSettings.lowStockAlerts}
                onChange={(e) => setNotificationSettings({...notificationSettings, lowStockAlerts: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Maintenance Due</label>
              <input
                type="checkbox"
                checked={notificationSettings.maintenanceDue}
                onChange={(e) => setNotificationSettings({...notificationSettings, maintenanceDue: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Battery Warnings</label>
              <input
                type="checkbox"
                checked={notificationSettings.batteryWarnings}
                onChange={(e) => setNotificationSettings({...notificationSettings, batteryWarnings: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">System Updates</label>
              <input
                type="checkbox"
                checked={notificationSettings.systemUpdates}
                onChange={(e) => setNotificationSettings({...notificationSettings, systemUpdates: e.target.checked})}
                className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Smartphone className="w-5 h-5 text-green-600" />
            Delivery Methods
          </h4>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Email Notifications
              </label>
              <input
                type="checkbox"
                checked={notificationSettings.emailNotifications}
                onChange={(e) => setNotificationSettings({...notificationSettings, emailNotifications: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Smartphone className="w-4 h-4" />
                SMS Notifications
              </label>
              <input
                type="checkbox"
                checked={notificationSettings.smsNotifications}
                onChange={(e) => setNotificationSettings({...notificationSettings, smsNotifications: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Push Notifications</label>
              <input
                type="checkbox"
                checked={notificationSettings.pushNotifications}
                onChange={(e) => setNotificationSettings({...notificationSettings, pushNotifications: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Notification Sound</label>
              <input
                type="checkbox"
                checked={notificationSettings.notificationSound}
                onChange={(e) => setNotificationSettings({...notificationSettings, notificationSound: e.target.checked})}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderThresholdSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Zap className="w-5 h-5 text-orange-600" />
            Battery Thresholds
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Low Battery Warning (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={thresholdSettings.lowBatteryThreshold}
                onChange={(e) => setThresholdSettings({...thresholdSettings, lowBatteryThreshold: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Critical Battery Warning (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={thresholdSettings.criticalBatteryThreshold}
                onChange={(e) => setThresholdSettings({...thresholdSettings, criticalBatteryThreshold: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Clock className="w-5 h-5 text-purple-600" />
            Time-based Thresholds
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maintenance Warning (days)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={thresholdSettings.maintenanceWarningDays}
                onChange={(e) => setThresholdSettings({...thresholdSettings, maintenanceWarningDays: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Inactivity Threshold (days)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={thresholdSettings.inactivityThreshold}
                onChange={(e) => setThresholdSettings({...thresholdSettings, inactivityThreshold: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'thresholds':
        return renderThresholdSettings();
      case 'security':
        return (
          <div className="text-center py-12">
            <Shield className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Security Settings</h3>
            <p className="mt-1 text-sm text-gray-500">Security configuration panel coming soon.</p>
          </div>
        );
      case 'data':
        return (
          <div className="text-center py-12">
            <Database className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Data Management</h3>
            <p className="mt-1 text-sm text-gray-500">Data management settings coming soon.</p>
          </div>
        );
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />

      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/inventory')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Inventory</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaCog className="text-purple-600" />
                  Inventory Settings
                </h2>
                <p className="text-gray-600 mt-1">Configure inventory system preferences and thresholds</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {showSuccess && (
                <div className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg">
                  <CheckCircle size={16} />
                  <span className="text-sm">Settings saved successfully!</span>
                </div>
              )}

              <button
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw size={18} />
                <span className="hidden sm:inline">Reset</span>
              </button>

              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                {isSaving ? (
                  <RefreshCw size={18} className="animate-spin" />
                ) : (
                  <Save size={18} />
                )}
                <span className="hidden sm:inline">{isSaving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6" aria-label="Tabs">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {renderActiveTab()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventorySettings;
