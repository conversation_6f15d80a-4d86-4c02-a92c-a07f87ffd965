import { useEffect, useState } from 'react';
import DroneCharts from './DroneCharts';
import OrganizationCharts from './OrganizationCharts';
import DroneMap from './DroneMap';
import Activity from './Activity';

import {
  Building,
  Boxes,
  Truck,
  Bot,
  Bell,
  Search,
  Plus,
  RefreshCw,
  Loader2,
  Settings,
  User,
  LogOut,
  X,
  Eye,
  EyeOff
} from 'lucide-react';

import { useNavigate } from 'react-router-dom';
import AdminSidebar from '../common/AdminSidebar';
import api from '../../../services/api';
import {
  StatCardSkeleton,
  ChartSkeleton,
  MapSkeleton,
  ActivitySkeleton,
  ErrorDisplay
} from './LoadingComponents';
import dashboardDataService from './DashboardDataService';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState({
    dronesInInventory: 0,
    registeredOrgs: 0,
    deployedDrones: 0,
    maintenanceDrones: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [showProfile, setShowProfile] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [loadingStates, setLoadingStates] = useState({
    stats: true,
    map: true,
    droneCharts: true,
    orgCharts: true,
    activity: true
  });

  // Password change form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');

  // Initialize dashboard and set up real-time subscriptions
  useEffect(() => {
    document.body.style.overflow = "auto";
    document.body.style.display = "block";
    document.body.style.justifyContent = "unset";
    document.body.style.alignItems = "unset";
    document.body.style.height = "auto";
    document.body.style.background = "#f5f5f5";

    // Fetch initial dashboard data
    fetchDashboardData();

    // Subscribe to real-time data updates
    const unsubscribeStats = dashboardDataService.subscribe('stats', (stats) => {
      setDashboardData({
        dronesInInventory: stats.dronesInInventory,
        registeredOrgs: stats.registeredOrgs,
        deployedDrones: stats.deployedDrones,
        maintenanceDrones: stats.maintenanceDrones
      });
    });

    // Staggered loading of different components
    const timers = [
      setTimeout(() => setLoadingStates(prev => ({ ...prev, stats: false })), 1200),
      setTimeout(() => setLoadingStates(prev => ({ ...prev, map: false })), 2000),
      setTimeout(() => setLoadingStates(prev => ({ ...prev, droneCharts: false })), 2500),
      setTimeout(() => setLoadingStates(prev => ({ ...prev, orgCharts: false })), 3000),
      setTimeout(() => setLoadingStates(prev => ({ ...prev, activity: false })), 1800)
    ];

    return () => {
      timers.forEach(timer => clearTimeout(timer));
      unsubscribeStats();
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showProfile && !event.target.closest('.profile-dropdown')) {
        setShowProfile(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProfile]);

  const fetchDashboardData = async () => {
    setRefreshing(true);
    setError(null);

    try {
      const stats = await dashboardDataService.fetchDashboardStats();
      setDashboardData({
        dronesInInventory: stats.dronesInInventory,
        registeredOrgs: stats.registeredOrgs,
        deployedDrones: stats.deployedDrones,
        maintenanceDrones: stats.maintenanceDrones
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchDashboardData();
    setLoadingStates({
      stats: true,
      map: true,
      droneCharts: true,
      orgCharts: true,
      activity: true
    });

    // Staggered loading of different components
    setTimeout(() => setLoadingStates(prev => ({ ...prev, stats: false })), 800);
    setTimeout(() => setLoadingStates(prev => ({ ...prev, map: false })), 1500);
    setTimeout(() => setLoadingStates(prev => ({ ...prev, droneCharts: false })), 2000);
    setTimeout(() => setLoadingStates(prev => ({ ...prev, orgCharts: false })), 2500);
    setTimeout(() => setLoadingStates(prev => ({ ...prev, activity: false })), 1200);
  };

  const handleLogout = () => {
    // Clear any stored authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    localStorage.removeItem('droneDraft');

    // Navigate to login page
    navigate('/');
  };

  const handleSettings = () => {
    setShowSettings(true);
    setShowProfile(false);
    // Reset form when opening settings
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordError('');
    setPasswordSuccess('');
  };

  const handlePasswordChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear errors when user starts typing
    if (passwordError) setPasswordError('');
    if (passwordSuccess) setPasswordSuccess('');
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const validatePasswordForm = () => {
    if (!passwordForm.currentPassword) {
      setPasswordError('Current password is required');
      return false;
    }
    if (!passwordForm.newPassword) {
      setPasswordError('New password is required');
      return false;
    }
    if (passwordForm.newPassword.length < 8) {
      setPasswordError('New password must be at least 8 characters long');
      return false;
    }
    // Check for uppercase, lowercase, and number
    const hasUppercase = /[A-Z]/.test(passwordForm.newPassword);
    const hasLowercase = /[a-z]/.test(passwordForm.newPassword);
    const hasNumber = /\d/.test(passwordForm.newPassword);

    if (!hasUppercase || !hasLowercase || !hasNumber) {
      setPasswordError('New password must contain at least one uppercase letter, one lowercase letter, and one number');
      return false;
    }
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError('New passwords do not match');
      return false;
    }
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      setPasswordError('New password must be different from current password');
      return false;
    }
    return true;
  };

  const handlePasswordSubmit = async () => {
    if (!validatePasswordForm()) return;

    setPasswordLoading(true);
    setPasswordError('');

    try {
      const response = await api.put('/auth/change-password', {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      });

      if (response.data.success) {
        setPasswordSuccess('Password changed successfully!');
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        // Auto close modal after 2 seconds
        setTimeout(() => {
          setShowSettings(false);
          setPasswordSuccess('');
        }, 2000);
      } else {
        setPasswordError(response.data.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Password change error:', error);
      setPasswordError(error.response?.data?.message || 'Network error. Please try again.');
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleCloseSettings = () => {
    setShowSettings(false);
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordError('');
    setPasswordSuccess('');
    setShowPasswords({
      current: false,
      new: false,
      confirm: false
    });
  };

  return (
    <div className="flex w-full bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black min-h-screen">
      {/* Sidebar */}
      <AdminSidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col w-full lg:pl-[250px] transition-all duration-300">
        {/* Professional Header - Consistent with Organization Dashboard */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Dashboard Title Section */}
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900 text-left">Admin Dashboard</h2>
                <p className="text-sm text-gray-600 mt-1 text-left">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>

              {/* Search Section */}
              <div className="flex-1 max-w-md mx-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <input
                    type="text"
                    placeholder="Search drones, organizations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className={`p-2 rounded-lg hover:bg-gray-100 transition-colors ${refreshing ? 'animate-spin' : ''}`}
                  title="Refresh Dashboard"
                >
                  {refreshing ? <Loader2 size={18} className="text-gray-600" /> : <RefreshCw size={18} className="text-gray-600" />}
                </button>

                <button
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative"
                  title="Notifications"
                >
                  <Bell size={18} className="text-gray-600" />
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    3
                  </span>
                </button>

                {/* Quick Action Buttons */}
                <button
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-sm text-sm font-medium flex items-center gap-2 hover:bg-blue-700 transition-colors"
                  onClick={() => navigate('/organizationform')}
                >
                  <Plus size={16} /> Add Organization
                </button>
                <button
                  className="bg-green-600 text-white px-4 py-2 rounded-lg shadow-sm text-sm font-medium flex items-center gap-2 hover:bg-green-700 transition-colors"
                  onClick={() => navigate('/adddrone')}
                >
                  <Plus size={16} /> Add Drone
                </button>

                {/* Profile Menu */}
                <div className="relative profile-dropdown">
                  <button
                    onClick={() => setShowProfile(!showProfile)}
                    className="flex items-center gap-2 p-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{background: 'linear-gradient(to bottom right, #3b82f6, #1d4ed8)'}}>
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <span className="hidden md:block text-sm font-medium">Admin</span>
                  </button>

                  {/* Profile Dropdown */}
                  {showProfile && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                      <div className="p-4 border-b border-gray-200">
                        <p className="font-medium text-gray-900">Administrator</p>
                        <p className="text-sm text-gray-600">Admin Department</p>
                      </div>

                      <div className="py-2">
                        <button
                          onClick={handleSettings}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                        >
                          <Settings className="w-4 h-4" />
                          Settings
                        </button>
                        <button
                          onClick={handleLogout}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                        >
                          <LogOut className="w-4 h-4" />
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section with Gap */}
        <div className="pt-6">
          {error ? (
            <div className="px-4 sm:px-6">
              <ErrorDisplay
                title="Failed to load dashboard data"
                message={error}
                onRetry={handleRefresh}
              />
            </div>
          ) : loadingStates.stats ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 px-4 sm:px-6 text-black text-left">
              {[1, 2, 3, 4].map((i) => (
                <StatCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 px-4 sm:px-6 text-black text-left">
            <StatCard
              title="Drones In Inventory"
              value={dashboardData.dronesInInventory}
              color="green"
              percentage="+24%"
              icon={<Boxes className="text-green-600" />}
            />
            <StatCard
              title="Registered Organizations"
              value={dashboardData.registeredOrgs}
              color="blue"
              percentage="+12%"
              icon={<Building className="text-blue-600" />}
            />
            <StatCard
              title="Deployed Drones"
              value={dashboardData.deployedDrones}
              color="purple"
              percentage="+19%"
              icon={<Truck className="text-purple-600" />}
            />
            <StatCard
              title="Maintenance"
              value={dashboardData.maintenanceDrones}
              color="amber"
              percentage="+12%"
              icon={<Bot className="text-amber-600" />}
            />
          </div>
        )}
        </div>

        {/* Main Dashboard Content */}
        <div className={`grid grid-cols-1 xl:grid-cols-3 gap-4 px-4 sm:px-6 pt-4 items-stretch ${showSettings ? 'relative z-0' : ''}`}>
          {/* Map Section - Takes 2/3 of the width on large screens */}
          <div className={`xl:col-span-2 order-2 xl:order-1 ${showSettings ? 'pointer-events-none opacity-50' : ''}`}>
            {loadingStates.map ? <MapSkeleton /> : <DroneMap />}
          </div>

          {/* Activity - Takes 1/3 of the width on large screens */}
          <div className="xl:col-span-1 order-1 xl:order-2">
            {loadingStates.activity ? <ActivitySkeleton /> : <Activity />}
          </div>
        </div>

        {/* Charts Section */}
        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-4 px-4 sm:px-6 pt-4 pb-6 ${showSettings ? 'relative z-0' : ''}`}>
          {/* Drone Charts */}
          <div className="order-2 lg:order-1">
            {loadingStates.droneCharts ? <ChartSkeleton /> : <DroneCharts />}
          </div>

          {/* Organization Charts */}
          <div className="order-1 lg:order-2">
            {loadingStates.orgCharts ? <ChartSkeleton /> : <OrganizationCharts />}
          </div>
        </div>
      </div>

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center modal-overlay">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 relative">
            {/* Close Button */}
            <button
              onClick={handleCloseSettings}
              className="absolute top-4 right-4 p-1 rounded-lg hover:bg-gray-100 transition-colors z-10"
              title="Close Settings"
            >
              <X size={20} className="text-gray-500" />
            </button>

            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Admin Settings</h3>
              <p className="text-sm text-gray-600 mt-1">Update your password</p>
            </div>

            {/* Form Content */}
            <div className="p-6 space-y-4">
              {/* Username (Read-only) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
                <input
                  type="text"
                  value="admin"
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
                  placeholder="admin"
                />
                <p className="text-xs text-gray-500 mt-1">Username cannot be changed</p>
              </div>

              {/* Current Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                <div className="relative">
                  <input
                    type={showPasswords.current ? "text" : "password"}
                    value={passwordForm.currentPassword}
                    onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter current password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('current')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.current ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              {/* New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                <div className="relative">
                  <input
                    type={showPasswords.new ? "text" : "password"}
                    value={passwordForm.newPassword}
                    onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter new password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('new')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.new ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">Password must be at least 8 characters with uppercase, lowercase, and number</p>
              </div>

              {/* Confirm New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                <div className="relative">
                  <input
                    type={showPasswords.confirm ? "text" : "password"}
                    value={passwordForm.confirmPassword}
                    onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Confirm new password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('confirm')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.confirm ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {passwordError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{passwordError}</p>
                </div>
              )}

              {/* Success Message */}
              {passwordSuccess && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-600">{passwordSuccess}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-200 flex gap-3">
              <button
                onClick={handleCloseSettings}
                disabled={passwordLoading}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handlePasswordSubmit}
                disabled={passwordLoading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                className="flex-1 px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {passwordLoading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update Password'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// ✅ StatCard with color-based background and icon
const StatCard = ({ title, value, color, percentage, icon }) => {
  const bgColorMap = {
    green: 'bg-gradient-to-br from-green-50 to-green-100 border-l-4 border-green-500',
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100 border-l-4 border-blue-500',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100 border-l-4 border-purple-500',
    amber: 'bg-gradient-to-br from-amber-50 to-amber-100 border-l-4 border-amber-500',
    red: 'bg-gradient-to-br from-red-50 to-red-100 border-l-4 border-red-500'
  };

  const textColorMap = {
    green: 'text-green-800',
    blue: 'text-blue-800',
    purple: 'text-purple-800',
    amber: 'text-amber-800',
    red: 'text-red-800'
  };

  const percentageColorMap = {
    green: 'text-green-600',
    blue: 'text-blue-600',
    purple: 'text-purple-600',
    amber: 'text-amber-600',
    red: 'text-red-600'
  };

  const bgClass = bgColorMap[color] || 'bg-white';
  const textClass = textColorMap[color] || 'text-gray-800';
  const percentageClass = percentageColorMap[color] || 'text-gray-600';

  return (
    <div className={`${bgClass} p-3 sm:p-4 md:p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 w-full`}>
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-1">{title}</h4>
          <div className="flex items-baseline gap-1 sm:gap-2">
            <span className={`text-lg sm:text-xl md:text-2xl font-bold ${textClass}`}>{value}</span>
            <span className={`text-[10px] sm:text-xs font-medium ${percentageClass}`}>{percentage}</span>
          </div>
        </div>
        <div className="p-1.5 sm:p-2 rounded-lg bg-white shadow-sm">
          <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6">
            {icon}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
