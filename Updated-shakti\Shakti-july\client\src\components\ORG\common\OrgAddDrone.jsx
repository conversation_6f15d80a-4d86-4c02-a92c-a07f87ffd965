import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Bell,
  Search,
  RefreshCw,
  Upload,
  FileText,
  MapPin,
  Calendar,
  Settings,
  CheckCircle,
  AlertCircle,
  Save,
  Send,
  ArrowLeft,
  Plus,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import OrgSidebar from './OrgSidebar';
import OrganizationPortalService from '../../../services/organizationPortalService';

const OrgAddDrone = () => {
  const navigate = useNavigate();

  // Form state management
  const [formData, setFormData] = useState({
    droneId: OrganizationPortalService.generateDroneId(),
    droneName: '',
    modelNumber: '',
    droneType: 'quadcopter',
    manufacturer: '',
    purchaseDate: '',
    purchasePrice: '',
    vendor: '',
    flightHours: '',
    batteryCapacity: '',
    weight: '',
    maxPayload: '',
    maxFlightTime: '',
    maxRange: '',
    maxAltitude: '',
    maxSpeed: '',
    cameraResolution: '',
    hasGimbal: false,
    hasGPS: true,
    hasObstacleAvoidance: false,
    quantity: 1,
    specifications: '',
    currentLocation: '',
    latitude: '',
    longitude: '',
    altitude: '',
    additionalNotes: '',
    confirmed: false
  });

  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errors, setErrors] = useState({});

  // Auto-generate new drone ID on component mount
  useEffect(() => {
    const newId = `DRN-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;
    setFormData(prev => ({ ...prev, droneId: newId }));
  }, []);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle file upload
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const validFiles = files.filter(file => {
      const isValidType = ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type);
      const isValidSize = file.size <= 3 * 1024 * 1024; // 3MB
      return isValidType && isValidSize;
    });

    setUploadedFiles(prev => [...prev, ...validFiles]);
  };

  // Remove uploaded file
  const removeFile = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.droneName.trim()) newErrors.droneName = 'Drone name is required';
    if (!formData.modelNumber.trim()) newErrors.modelNumber = 'Model number is required';
    if (!formData.droneType) newErrors.droneType = 'Drone type is required';
    if (!formData.manufacturer.trim()) newErrors.manufacturer = 'Manufacturer is required';
    if (!formData.purchaseDate) newErrors.purchaseDate = 'Purchase date is required';
    if (!formData.batteryCapacity) newErrors.batteryCapacity = 'Battery capacity is required';
    if (!formData.specifications.trim()) newErrors.specifications = 'Specifications are required';
    if (!formData.currentLocation.trim()) newErrors.currentLocation = 'Current location is required';
    if (uploadedFiles.length === 0) newErrors.files = 'At least one document is required';
    if (!formData.confirmed) newErrors.confirmed = 'Please confirm the information is accurate';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Format form data for API
      const droneData = {
        name: formData.droneName,
        model: formData.modelNumber,
        manufacturer: formData.manufacturer,
        serialNumber: formData.droneId,
        registrationNumber: `REG-${formData.droneId}`,
        specifications: {
          type: formData.droneType,
          weight: formData.weight ? parseFloat(formData.weight) : undefined,
          maxPayload: formData.maxPayload ? parseFloat(formData.maxPayload) : undefined,
          maxFlightTime: formData.maxFlightTime ? parseInt(formData.maxFlightTime) : undefined,
          maxRange: formData.maxRange ? parseFloat(formData.maxRange) : undefined,
          maxAltitude: formData.maxAltitude ? parseInt(formData.maxAltitude) : undefined,
          maxSpeed: formData.maxSpeed ? parseInt(formData.maxSpeed) : undefined,
          batteryCapacity: formData.batteryCapacity ? parseInt(formData.batteryCapacity) : undefined,
          cameraResolution: formData.cameraResolution,
          hasGimbal: formData.hasGimbal,
          hasGPS: formData.hasGPS,
          hasObstacleAvoidance: formData.hasObstacleAvoidance
        },
        purchase: {
          purchaseDate: formData.purchaseDate || new Date().toISOString(),
          purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : 0,
          vendor: formData.vendor || 'Unknown'
        },
        currentLocation: {
          latitude: formData.latitude ? parseFloat(formData.latitude) : 0,
          longitude: formData.longitude ? parseFloat(formData.longitude) : 0,
          altitude: formData.altitude ? parseFloat(formData.altitude) : 0
        },
        status: 'active',
        condition: 'excellent'
      };

      // Basic validation
      if (!droneData.name || !droneData.model || !droneData.manufacturer) {
        setErrors({ general: 'Please fill in all required fields (Name, Model, Manufacturer)' });
        return;
      }

      // Submit to backend
      console.log('🚁 Submitting drone data:', droneData);
      const response = await OrganizationPortalService.addDrone(droneData);

      console.log('✅ Response received:', response);

      if (response.success) {
        setShowSuccess(true);

        // Reset form after success
        setTimeout(() => {
          setFormData({
            droneId: `DRN-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
            droneName: '',
            modelNumber: '',
            droneType: 'quadcopter',
            manufacturer: '',
            purchaseDate: '',
            purchasePrice: '',
            vendor: '',
            flightHours: '',
            batteryCapacity: '',
            weight: '',
            maxPayload: '',
            maxFlightTime: '',
            maxRange: '',
            maxAltitude: '',
            maxSpeed: '',
            cameraResolution: '',
            hasGimbal: false,
            hasGPS: true,
            hasObstacleAvoidance: false,
            quantity: 1,
            specifications: '',
            currentLocation: '',
            latitude: '',
            longitude: '',
            altitude: '',
            additionalNotes: '',
            confirmed: false
          });
          setUploadedFiles([]);
          setShowSuccess(false);
          navigate('/orgdronepage'); // Navigate to drone list
        }, 2000);
      } else {
        setErrors({ general: response.message || 'Failed to add drone' });
      }

    } catch (error) {
      console.error('Submission error:', error);

      // Handle authentication errors
      if (error.response?.status === 401) {
        setErrors({
          general: 'Authentication failed. Please log in again as an organization user.'
        });

        // Optionally redirect to login after a delay
        setTimeout(() => {
          navigate('/');
        }, 3000);
      } else {
        setErrors({
          general: error.response?.data?.message || 'Failed to add drone. Please try again.'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Save as draft
  const handleSaveDraft = () => {
    localStorage.setItem('droneDraft', JSON.stringify({ formData, uploadedFiles: uploadedFiles.map(f => f.name) }));
    alert('Draft saved successfully!');
  };

  // Load draft
  const loadDraft = () => {
    const draft = localStorage.getItem('droneDraft');
    if (draft) {
      const { formData: draftData } = JSON.parse(draft);
      setFormData(draftData);
    }
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <OrgSidebar />

      {/* Main Content */}
      <main className="flex-1 ml-0 lg:ml-72 transition-all duration-300">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="px-4 lg:px-6 py-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4 lg:gap-0 lg:justify-between">
              <div className="flex items-center gap-3">
                <button
                  onClick={() => navigate('/orgdronepage')}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h2 className="text-xl lg:text-2xl font-bold text-gray-900">Add New Drone</h2>
                  <p className="text-sm text-gray-600">Submit a request to add a new drone to your fleet</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={loadDraft}
                  className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
                >
                  <FileText className="w-4 h-4" />
                  Load Draft
                </button>
                <div className="relative">
                  <button className="p-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                    <Bell className="w-5 h-5 text-gray-700" />
                  </button>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mx-4 lg:mx-6 mt-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div>
              <p className="text-green-800 font-medium">Request submitted successfully!</p>
              <p className="text-green-600 text-sm">Your drone addition request has been sent for review.</p>
            </div>
          </div>
        )}

        {/* Form Container */}
        <div className="p-4 lg:p-6">
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Bot className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
                    <p className="text-sm text-gray-600">Enter the basic details of the drone</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Drone ID */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone ID
                    </label>
                    <input
                      type="text"
                      value={formData.droneId}
                      disabled
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed focus:outline-none"
                    />
                    <p className="text-xs text-gray-500 mt-1">Auto-generated unique identifier</p>
                  </div>

                  {/* Drone Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.droneName}
                      onChange={(e) => handleInputChange('droneName', e.target.value)}
                      placeholder="Enter drone name"
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.droneName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.droneName && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.droneName}
                      </p>
                    )}
                  </div>

                  {/* Model Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Model Number <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.modelNumber}
                      onChange={(e) => handleInputChange('modelNumber', e.target.value)}
                      placeholder="e.g. DJI-M300-RTK"
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.modelNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.modelNumber && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.modelNumber}
                      </p>
                    )}
                  </div>

                  {/* Drone Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Drone Type <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={formData.droneType}
                      onChange={(e) => handleInputChange('droneType', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.droneType ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select drone type</option>
                      <option value="quadcopter">Quadcopter</option>
                      <option value="hexacopter">Hexacopter</option>
                      <option value="octocopter">Octocopter</option>
                      <option value="fixed-wing">Fixed Wing</option>
                      <option value="hybrid">Hybrid</option>
                      <option value="agriculture">Agriculture</option>
                      <option value="surveillance">Surveillance</option>
                      <option value="delivery">Delivery</option>
                      <option value="racing">Racing</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.droneType && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.droneType}
                      </p>
                    )}
                  </div>

                  {/* Manufacturer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Manufacturer <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.manufacturer}
                      onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                      placeholder="e.g. DJI, Parrot, Autel"
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.manufacturer ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.manufacturer && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.manufacturer}
                      </p>
                    )}
                  </div>

                  {/* Purchase Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Purchase Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={formData.purchaseDate}
                      onChange={(e) => handleInputChange('purchaseDate', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.purchaseDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.purchaseDate && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.purchaseDate}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Technical Specifications Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Settings className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Technical Specifications</h2>
                    <p className="text-sm text-gray-600">Provide technical details and specifications</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Flight Hours */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Flight Hours
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={formData.flightHours}
                      onChange={(e) => handleInputChange('flightHours', e.target.value)}
                      placeholder="0.0"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    />
                    <p className="text-xs text-gray-500 mt-1">Total flight hours (if used)</p>
                  </div>

                  {/* Battery Capacity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Battery Capacity (mAh) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      value={formData.batteryCapacity}
                      onChange={(e) => handleInputChange('batteryCapacity', e.target.value)}
                      placeholder="e.g. 5000"
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.batteryCapacity ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.batteryCapacity && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.batteryCapacity}
                      </p>
                    )}
                  </div>

                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    />
                    <p className="text-xs text-gray-500 mt-1">Number of drones to add</p>
                  </div>

                  {/* Current Location */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Location <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.currentLocation}
                      onChange={(e) => handleInputChange('currentLocation', e.target.value)}
                      placeholder="e.g. Warehouse A, Field Station 3"
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        errors.currentLocation ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.currentLocation && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.currentLocation}
                      </p>
                    )}
                  </div>

                  {/* Specifications */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Detailed Specifications <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={formData.specifications}
                      onChange={(e) => handleInputChange('specifications', e.target.value)}
                      placeholder="Enter detailed specifications (weight, dimensions, max altitude, range, payload capacity, etc.)"
                      rows={4}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors resize-none ${
                        errors.specifications ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.specifications && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.specifications}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Documentation Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Documentation</h2>
                    <p className="text-sm text-gray-600">Upload required certificates and documents</p>
                  </div>
                </div>

                {/* File Upload */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Upload Documents <span className="text-red-500">*</span>
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      errors.files ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-blue-400'
                    }`}>
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600 mb-2">Drag and drop files here or</p>
                      <label className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors">
                        <Plus className="w-4 h-4" />
                        Browse Files
                        <input
                          type="file"
                          multiple
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-2">
                        Supported formats: PDF, JPG, PNG (Max: 3MB each)
                      </p>
                    </div>
                    {errors.files && (
                      <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {errors.files}
                      </p>
                    )}
                  </div>

                  {/* Uploaded Files List */}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Uploaded Files:</p>
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <FileText className="w-4 h-4 text-gray-500" />
                            <span className="text-sm text-gray-700">{file.name}</span>
                            <span className="text-xs text-gray-500">
                              ({(file.size / 1024 / 1024).toFixed(2)} MB)
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="p-1 text-red-500 hover:bg-red-100 rounded transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Additional Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Notes (Optional)
                    </label>
                    <textarea
                      value={formData.additionalNotes}
                      onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                      placeholder="Any additional information about this drone request..."
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Confirmation and Submit Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="space-y-6">
                  {/* Confirmation Checkbox */}
                  <div className="flex items-start gap-3">
                    <input
                      type="checkbox"
                      id="confirmation"
                      checked={formData.confirmed}
                      onChange={(e) => handleInputChange('confirmed', e.target.checked)}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="confirmation" className="text-sm text-gray-700">
                      I confirm that all provided information is accurate and this drone meets all regulatory
                      requirements for operation. I understand that false information may result in rejection
                      of this request.
                    </label>
                  </div>
                  {errors.confirmed && (
                    <p className="text-red-500 text-xs flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.confirmed}
                    </p>
                  )}

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3 sm:justify-between">
                    <div className="flex flex-col sm:flex-row gap-3">
                      <button
                        type="button"
                        onClick={handleSaveDraft}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
                      >
                        <Save className="w-4 h-4" />
                        Save as Draft
                      </button>
                      <button
                        type="button"
                        onClick={() => navigate('/orgdronepage')}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4" />
                          Submit Request
                        </>
                      )}
                    </button>
                  </div>

                  {/* Submission Note */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <p className="text-blue-800 font-medium text-sm">Review Process</p>
                        <p className="text-blue-700 text-sm mt-1">
                          Your submission will be reviewed by an administrator before approval.
                          You will receive a notification once the review is complete.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
};

export default OrgAddDrone;
