import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  Settings,
  Plus,
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Trash2,
  Wrench
} from 'lucide-react';

const MaintenanceReports = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [maintenanceReports, setMaintenanceReports] = useState([]);

  // Initialize with sample maintenance report data
  React.useEffect(() => {
    if (maintenanceReports.length === 0) {
      setMaintenanceReports([
        {
          id: 'MR-2024-001',
          workOrderId: 'WO-2024-0156',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          technician: '<PERSON>',
          date: '2024-01-15',
          time: '09:30',
          duration: '3.5 hours',
          location: 'Maintenance Bay A',
          maintenanceType: 'Scheduled Maintenance',
          priority: 'Medium',
          status: 'Completed',
          reportType: 'Routine Inspection',
          completedBy: 'John Smith',
          completedAt: '2024-01-15 13:00',
          workPerformed: [
            'Battery health check and calibration',
            'Motor inspection and lubrication',
            'Propeller balance verification',
            'Sensor calibration and testing',
            'Firmware update to v2.1.4',
            'Flight control system diagnostics'
          ],
          partsUsed: [
            { part: 'Propeller Set', quantity: 1, partNumber: 'PROP-001', cost: '$45.00' },
            { part: 'Motor Oil', quantity: '50ml', partNumber: 'OIL-002', cost: '$12.50' }
          ],
          findings: [
            'Minor wear on propeller #3 - replaced',
            'All systems operating within normal parameters',
            'Battery capacity at 94% - excellent condition'
          ],
          recommendations: [
            'Continue current maintenance schedule',
            'Monitor propeller wear patterns',
            'Next scheduled maintenance in 50 flight hours'
          ],
          costs: {
            labor: '$175.00',
            parts: '$57.50',
            total: '$232.50'
          },
          nextMaintenance: '2024-02-15',
          flightHours: 245.5,
          cyclesSinceMaintenance: 0,
          attachments: ['maintenance_log.pdf', 'parts_receipt.jpg', 'test_results.csv']
        },
        {
          id: 'MR-2024-002',
          workOrderId: 'WO-2024-0154',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          technician: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:20',
          duration: '5.2 hours',
          location: 'Field Service Unit',
          maintenanceType: 'Emergency Repair',
          priority: 'High',
          status: 'Completed',
          reportType: 'Repair Report',
          completedBy: 'Sarah Johnson',
          completedAt: '2024-01-14 19:32',
          workPerformed: [
            'Motor #2 replacement due to overheating',
            'ESC #2 replacement',
            'Thermal management system upgrade',
            'Complete system recalibration',
            'Extended flight testing',
            'Performance validation'
          ],
          partsUsed: [
            { part: 'Brushless Motor', quantity: 1, partNumber: 'MTR-002', cost: '$125.00' },
            { part: 'ESC Controller', quantity: 1, partNumber: 'ESC-002', cost: '$85.00' },
            { part: 'Thermal Pad', quantity: 2, partNumber: 'THM-001', cost: '$15.00' }
          ],
          findings: [
            'Motor #2 bearing failure caused overheating',
            'ESC thermal protection activated correctly',
            'No damage to adjacent components',
            'Root cause: excessive vibration from unbalanced propeller'
          ],
          recommendations: [
            'Implement enhanced vibration monitoring',
            'Increase propeller balance check frequency',
            'Consider motor upgrade for high-duty applications',
            'Schedule follow-up inspection in 2 weeks'
          ],
          costs: {
            labor: '$260.00',
            parts: '$225.00',
            total: '$485.00'
          },
          nextMaintenance: '2024-01-28',
          flightHours: 189.3,
          cyclesSinceMaintenance: 0,
          attachments: ['repair_report.pdf', 'motor_analysis.jpg', 'test_flight_log.csv']
        },
        {
          id: 'MR-2024-003',
          workOrderId: 'WO-2024-0152',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          technician: 'Mike Wilson',
          date: '2024-01-13',
          time: '11:15',
          duration: '2.8 hours',
          location: 'Maintenance Bay B',
          maintenanceType: 'Preventive Maintenance',
          priority: 'Low',
          status: 'Completed',
          reportType: 'Preventive Care',
          completedBy: 'Mike Wilson',
          completedAt: '2024-01-13 14:03',
          workPerformed: [
            'Comprehensive visual inspection',
            'Battery performance testing',
            'Gimbal calibration and adjustment',
            'Camera sensor cleaning',
            'GPS accuracy verification',
            'Software update and configuration'
          ],
          partsUsed: [
            { part: 'Cleaning Kit', quantity: 1, partNumber: 'CLN-001', cost: '$8.50' },
            { part: 'Calibration Target', quantity: 1, partNumber: 'CAL-001', cost: '$25.00' }
          ],
          findings: [
            'All systems performing optimally',
            'Camera lens had minor dust accumulation - cleaned',
            'GPS lock time within specifications',
            'Battery health excellent at 96%'
          ],
          recommendations: [
            'Excellent maintenance - continue current procedures',
            'Consider extending maintenance intervals',
            'Monitor camera lens protection',
            'Next maintenance in 75 flight hours'
          ],
          costs: {
            labor: '$140.00',
            parts: '$33.50',
            total: '$173.50'
          },
          nextMaintenance: '2024-03-13',
          flightHours: 156.2,
          cyclesSinceMaintenance: 0,
          attachments: ['inspection_checklist.pdf', 'battery_test.csv', 'calibration_report.pdf']
        },
        {
          id: 'MR-2024-004',
          workOrderId: 'WO-2024-0148',
          droneId: 'DRN-002',
          droneName: 'Inspector Delta',
          technician: 'Emily Chen',
          date: '2024-01-12',
          time: '08:45',
          duration: '4.1 hours',
          location: 'Maintenance Bay A',
          maintenanceType: 'Upgrade Installation',
          priority: 'Medium',
          status: 'In Progress',
          reportType: 'Upgrade Report',
          completedBy: '',
          completedAt: '',
          workPerformed: [
            'Advanced sensor package installation',
            'Payload bay modification',
            'Wiring harness upgrade',
            'Flight control software update',
            'Initial system integration testing'
          ],
          partsUsed: [
            { part: 'LiDAR Sensor', quantity: 1, partNumber: 'LDR-001', cost: '$850.00' },
            { part: 'Mounting Bracket', quantity: 1, partNumber: 'MNT-003', cost: '$45.00' },
            { part: 'Wiring Harness', quantity: 1, partNumber: 'WIR-002', cost: '$65.00' }
          ],
          findings: [
            'Sensor integration proceeding as planned',
            'Minor payload bay modification required',
            'Flight control system accepting new sensor data',
            'Calibration in progress'
          ],
          recommendations: [
            'Complete calibration procedures',
            'Conduct comprehensive flight testing',
            'Update operational procedures',
            'Train operators on new capabilities'
          ],
          costs: {
            labor: '$205.00',
            parts: '$960.00',
            total: '$1,165.00'
          },
          nextMaintenance: '2024-02-12',
          flightHours: 134.7,
          cyclesSinceMaintenance: 15,
          attachments: ['upgrade_plan.pdf', 'integration_test.csv', 'sensor_specs.pdf']
        },
        {
          id: 'MR-2024-005',
          workOrderId: 'WO-2024-0145',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          technician: 'David Rodriguez',
          date: '2024-01-11',
          time: '15:30',
          duration: '1.5 hours',
          location: 'Field Service Unit',
          maintenanceType: 'Corrective Maintenance',
          priority: 'High',
          status: 'Completed',
          reportType: 'Field Repair',
          completedBy: 'David Rodriguez',
          completedAt: '2024-01-11 17:00',
          workPerformed: [
            'GPS antenna replacement',
            'Signal strength testing',
            'Navigation system recalibration',
            'Flight path accuracy verification',
            'System performance validation'
          ],
          partsUsed: [
            { part: 'GPS Antenna', quantity: 1, partNumber: 'GPS-001', cost: '$75.00' },
            { part: 'Coaxial Cable', quantity: '2m', partNumber: 'CBL-001', cost: '$12.00' }
          ],
          findings: [
            'GPS antenna damaged during transport',
            'Signal strength restored to full capacity',
            'Navigation accuracy within specifications',
            'No other system damage detected'
          ],
          recommendations: [
            'Improve transport protection procedures',
            'Monitor GPS performance closely',
            'Consider antenna protection upgrade',
            'Regular signal strength checks'
          ],
          costs: {
            labor: '$75.00',
            parts: '$87.00',
            total: '$162.00'
          },
          nextMaintenance: '2024-02-11',
          flightHours: 238.9,
          cyclesSinceMaintenance: 8,
          attachments: ['field_repair_log.pdf', 'gps_test_results.csv', 'antenna_photos.jpg']
        }
      ]);
    }
  }, [maintenanceReports.length]);

  // Form state for new maintenance report
  const [newMaintenanceReport, setNewMaintenanceReport] = useState({
    workOrderId: '',
    droneId: '',
    droneName: '',
    technician: '',
    date: '',
    time: '',
    location: '',
    maintenanceType: 'Scheduled Maintenance',
    priority: 'Medium',
    reportType: 'Routine Inspection',
    notes: ''
  });

  // Add new maintenance report function
  const handleAddMaintenanceReport = (e) => {
    e.preventDefault();
    
    const newReport = {
      id: `MR-2024-${String(maintenanceReports.length + 1).padStart(3, '0')}`,
      ...newMaintenanceReport,
      status: 'In Progress',
      completedBy: '',
      completedAt: '',
      duration: '0 hours',
      workPerformed: [],
      partsUsed: [],
      findings: [],
      recommendations: [],
      costs: {
        labor: '$0.00',
        parts: '$0.00',
        total: '$0.00'
      },
      nextMaintenance: '',
      flightHours: 0,
      cyclesSinceMaintenance: 0,
      attachments: []
    };
    
    setMaintenanceReports([...maintenanceReports, newReport]);
    setNewMaintenanceReport({
      workOrderId: '',
      droneId: '',
      droneName: '',
      technician: '',
      date: '',
      time: '',
      location: '',
      maintenanceType: 'Scheduled Maintenance',
      priority: 'Medium',
      reportType: 'Routine Inspection',
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete maintenance report function
  const handleDeleteMaintenanceReport = (id) => {
    setMaintenanceReports(maintenanceReports.filter(report => report.id !== id));
  };

  // Update maintenance report status
  const handleUpdateStatus = (id, newStatus) => {
    setMaintenanceReports(maintenanceReports.map(report => 
      report.id === id ? { ...report, status: newStatus } : report
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Pending': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'On Hold': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed': return <CheckCircle className="w-4 h-4" />;
      case 'In Progress': return <Clock className="w-4 h-4" />;
      case 'Pending': return <AlertTriangle className="w-4 h-4" />;
      case 'On Hold': return <XCircle className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-700 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const filteredMaintenanceReports = maintenanceReports.filter(report => {
    const matchesSearch = report.workOrderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.technician.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || report.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesPriority = filterPriority === 'all' || report.priority.toLowerCase() === filterPriority.toLowerCase();
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const headerActions = (
    <div className="flex items-center justify-between gap-4">
      {/* Left Side - Search */}
      <div className="flex items-center gap-4 flex-1">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Quick Filters */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="completed">Completed</option>
          <option value="in progress">In Progress</option>
          <option value="pending">Pending</option>
        </select>

        <select
          value={filterPriority}
          onChange={(e) => setFilterPriority(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Priority</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
      </div>

      {/* Right Side - Actions */}
      <div className="flex items-center gap-3">
        <button
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Report
        </button>
      </div>
    </div>
  );

  return (
    <QCLayout
      title="Maintenance Reports"
      subtitle="Comprehensive drone maintenance and repair documentation"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{filteredMaintenanceReports.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Settings className="w-3 h-3" />
                  Generated
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Settings className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredMaintenanceReports.filter(r => r.status === 'Completed').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Finished
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredMaintenanceReports.filter(r => r.status === 'In Progress').length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Clock className="w-3 h-3" />
                  Active
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Clock className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Priority</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredMaintenanceReports.filter(r => r.priority === 'High').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Urgent
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <AlertTriangle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Maintenance Reports Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Maintenance Report Records</h3>
                <p className="text-sm text-gray-600 mt-1">Comprehensive drone maintenance and repair documentation</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Maintenance Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Work Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status & Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Costs & Timeline
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredMaintenanceReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                    {/* Maintenance Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Wrench className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{report.id}</h4>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                              {report.workOrderId}
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{report.droneId} - {report.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {report.technician}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {report.date} at {report.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {report.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Work Info */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{report.maintenanceType}</p>
                          <p className="text-xs text-gray-600">{report.reportType}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Duration: {report.duration}</p>
                          <p>Flight Hours: {report.flightHours}h</p>
                          <p>Cycles: {report.cyclesSinceMaintenance}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Work Items: {report.workPerformed.length}</p>
                          <p>Parts Used: {report.partsUsed.length}</p>
                        </div>
                      </div>
                    </td>

                    {/* Status & Priority */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                          {getStatusIcon(report.status)}
                          <span className="ml-1">{report.status}</span>
                        </span>

                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(report.priority)}`}>
                          {report.priority}
                        </span>

                        {report.completedBy && (
                          <div className="text-xs text-gray-500">
                            <p>Completed by: {report.completedBy}</p>
                            <p>At: {report.completedAt}</p>
                          </div>
                        )}

                        {report.nextMaintenance && (
                          <div className="text-xs text-gray-500">
                            <p>Next: {report.nextMaintenance}</p>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Costs & Timeline */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div className="text-sm">
                          <p className="font-medium text-gray-900">Total: {report.costs.total}</p>
                          <div className="text-xs text-gray-500">
                            <p>Labor: {report.costs.labor}</p>
                            <p>Parts: {report.costs.parts}</p>
                          </div>
                        </div>

                        <div className="text-xs text-gray-500">
                          <p>Findings: {report.findings.length}</p>
                          <p>Recommendations: {report.recommendations.length}</p>
                        </div>

                        <div className="text-xs text-gray-500">
                          <p>Attachments: {report.attachments.length}</p>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-reports/maintenance-reports/${report.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={report.status}
                          onChange={(e) => handleUpdateStatus(report.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Pending">Pending</option>
                          <option value="In Progress">In Progress</option>
                          <option value="Completed">Completed</option>
                          <option value="On Hold">On Hold</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteMaintenanceReport(report.id)}
                          title="Delete Report"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Maintenance Report Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">New Maintenance Report</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new maintenance report record</p>
              </div>

              <form onSubmit={handleAddMaintenanceReport} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Work Order ID</label>
                    <input
                      type="text"
                      value={newMaintenanceReport.workOrderId}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, workOrderId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., WO-2024-0157"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID</label>
                    <input
                      type="text"
                      value={newMaintenanceReport.droneId}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name</label>
                    <input
                      type="text"
                      value={newMaintenanceReport.droneName}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Technician</label>
                    <input
                      type="text"
                      value={newMaintenanceReport.technician}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, technician: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input
                      type="date"
                      value={newMaintenanceReport.date}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newMaintenanceReport.time}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={newMaintenanceReport.location}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Maintenance Bay A"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select
                      value={newMaintenanceReport.priority}
                      onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, priority: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Maintenance Type</label>
                  <select
                    value={newMaintenanceReport.maintenanceType}
                    onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, maintenanceType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Scheduled Maintenance">Scheduled Maintenance</option>
                    <option value="Emergency Repair">Emergency Repair</option>
                    <option value="Preventive Maintenance">Preventive Maintenance</option>
                    <option value="Upgrade Installation">Upgrade Installation</option>
                    <option value="Corrective Maintenance">Corrective Maintenance</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                  <select
                    value={newMaintenanceReport.reportType}
                    onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, reportType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Routine Inspection">Routine Inspection</option>
                    <option value="Repair Report">Repair Report</option>
                    <option value="Preventive Care">Preventive Care</option>
                    <option value="Upgrade Report">Upgrade Report</option>
                    <option value="Field Repair">Field Repair</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={newMaintenanceReport.notes}
                    onChange={(e) => setNewMaintenanceReport({...newMaintenanceReport, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the maintenance work..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Maintenance Report
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default MaintenanceReports;
