require('dotenv').config();
const mongoose = require('mongoose');

const testConnection = async () => {
  try {
    console.log('🔍 Testing MongoDB connection...');
    console.log('📍 Connection string:', process.env.MONGODB_URI);
    
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connection successful!');
    
    // Test basic operations
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📊 Available collections:', collections.map(c => c.name));
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 MongoDB is not running locally. Please:');
      console.log('   1. Install MongoDB Community Server');
      console.log('   2. Start MongoDB service');
      console.log('   3. Or use MongoDB Atlas (cloud)');
    }
  } finally {
    await mongoose.connection.close();
    console.log('🔒 Connection closed');
  }
};

testConnection();
