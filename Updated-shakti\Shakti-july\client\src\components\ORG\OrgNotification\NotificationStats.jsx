import React from 'react';
import {
    <PERSON>,
    Al<PERSON><PERSON>riangle,
    CheckCircle2,
    Clock,
    Archive,
    TrendingUp,
    TrendingDown,
    Activity,
    Shield,
    Zap,
    Info,
    Users,
    Calendar
} from 'lucide-react';

const NotificationStats = ({ stats }) => {
    const StatCard = ({ 
        title, 
        value, 
        icon: Icon, 
        color, 
        bgColor, 
        trend, 
        trendValue, 
        description 
    }) => (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-200 text-black" style={{ color: '#000000', backgroundColor: '#ffffff', filter: 'none' }}>
            <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-lg ${bgColor}`} style={{ filter: 'none' }}>
                    <Icon className={`w-5 h-5 ${color}`} style={{ filter: 'none' }} />
                </div>
                {trend && (
                    <div className={`flex items-center gap-1 text-xs font-medium ${
                        trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                        {trend === 'up' ? (
                            <TrendingUp className="w-3 h-3" />
                        ) : (
                            <TrendingDown className="w-3 h-3" />
                        )}
                        {trendValue}
                    </div>
                )}
            </div>
            <div className="space-y-1">
                <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
                <p className="text-sm font-medium text-gray-700">{title}</p>
                {description && (
                    <p className="text-xs text-gray-500">{description}</p>
                )}
            </div>
        </div>
    );

    const CategoryCard = ({ 
        title, 
        value, 
        icon: Icon, 
        color, 
        bgColor, 
        percentage 
    }) => (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-200 text-black" style={{ color: '#000000', backgroundColor: '#ffffff', filter: 'none' }}>
            <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-lg ${bgColor}`} style={{ filter: 'none' }}>
                    <Icon className={`w-4 h-4 ${color}`} style={{ filter: 'none' }} />
                </div>
                <span className="text-xs font-medium text-gray-500">{percentage}%</span>
            </div>
            <div className="space-y-1">
                <h3 className="text-xl font-bold text-gray-900">{value}</h3>
                <p className="text-sm font-medium text-gray-700">{title}</p>
            </div>
            <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5" style={{ backgroundColor: '#e5e7eb', filter: 'none' }}>
                <div
                    className={`h-1.5 rounded-full ${bgColor.replace('bg-', 'bg-').replace('-100', '-500')}`}
                    style={{
                        width: `${percentage}%`,
                        filter: 'none',
                        backgroundColor: bgColor.includes('blue') ? '#3b82f6' :
                                       bgColor.includes('green') ? '#22c55e' :
                                       bgColor.includes('red') ? '#ef4444' :
                                       bgColor.includes('yellow') ? '#eab308' :
                                       bgColor.includes('purple') ? '#a855f7' :
                                       bgColor.includes('orange') ? '#f97316' : '#3b82f6'
                    }}
                ></div>
            </div>
        </div>
    );

    // Calculate percentages for categories
    const totalCategoryNotifications = Object.values(stats.categories).reduce((sum, count) => sum + count, 0);
    const categoryPercentages = {
        emergency: totalCategoryNotifications > 0 ? Math.round((stats.categories.emergency / totalCategoryNotifications) * 100) : 0,
        warning: totalCategoryNotifications > 0 ? Math.round((stats.categories.warning / totalCategoryNotifications) * 100) : 0,
        success: totalCategoryNotifications > 0 ? Math.round((stats.categories.success / totalCategoryNotifications) * 100) : 0,
        info: totalCategoryNotifications > 0 ? Math.round((stats.categories.info / totalCategoryNotifications) * 100) : 0
    };

    const mainStats = [
        {
            title: "Total Notifications",
            value: stats.total,
            icon: Bell,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            trend: "up",
            trendValue: "+12%",
            description: "All active notifications"
        },
        {
            title: "Unread",
            value: stats.unread,
            icon: Activity,
            color: "text-orange-600",
            bgColor: "bg-orange-100",
            trend: stats.unread > 5 ? "up" : "down",
            trendValue: stats.unread > 5 ? "+8%" : "-3%",
            description: "Require attention"
        },
        {
            title: "Critical Priority",
            value: stats.critical,
            icon: AlertTriangle,
            color: "text-red-600",
            bgColor: "bg-red-100",
            trend: stats.critical > 0 ? "up" : "down",
            trendValue: stats.critical > 0 ? "+2%" : "-15%",
            description: "Immediate action needed"
        },
        {
            title: "High Priority",
            value: stats.high,
            icon: Zap,
            color: "text-yellow-600",
            bgColor: "bg-yellow-100",
            trend: "down",
            trendValue: "-5%",
            description: "Important notifications"
        },
        {
            title: "Today's Alerts",
            value: stats.today,
            icon: Calendar,
            color: "text-green-600",
            bgColor: "bg-green-100",
            trend: "up",
            trendValue: "+18%",
            description: "Notifications today"
        },
        {
            title: "Response Rate",
            value: "94%",
            icon: CheckCircle2,
            color: "text-purple-600",
            bgColor: "bg-purple-100",
            trend: "up",
            trendValue: "+2%",
            description: "Average response time"
        }
    ];

    const categoryStats = [
        {
            title: "Emergency",
            value: stats.categories.emergency,
            icon: AlertTriangle,
            color: "text-red-600",
            bgColor: "bg-red-100",
            percentage: categoryPercentages.emergency
        },
        {
            title: "Warnings",
            value: stats.categories.warning,
            icon: Shield,
            color: "text-orange-600",
            bgColor: "bg-orange-100",
            percentage: categoryPercentages.warning
        },
        {
            title: "Success",
            value: stats.categories.success,
            icon: CheckCircle2,
            color: "text-green-600",
            bgColor: "bg-green-100",
            percentage: categoryPercentages.success
        },
        {
            title: "Information",
            value: stats.categories.info,
            icon: Info,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            percentage: categoryPercentages.info
        }
    ];

    return (
        <div className="space-y-6 bg-white text-black" style={{ color: '#000000', backgroundColor: '#ffffff', filter: 'none' }}>
            {/* Main Statistics */}
            <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 bg-white">
                    <Activity className="w-5 h-5 text-blue-600" style={{ color: '#2563eb', filter: 'none' }} />
                    Notification Overview
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 lg:gap-4">
                    {mainStats.map((stat, index) => (
                        <StatCard key={index} {...stat} />
                    ))}
                </div>
            </div>

            {/* Category Breakdown */}
            <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Users className="w-5 h-5 text-purple-600" style={{ color: '#9333ea', filter: 'none' }} />
                    Category Breakdown
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
                    {categoryStats.map((category, index) => (
                        <CategoryCard key={index} {...category} />
                    ))}
                </div>
            </div>

            {/* Quick Insights */}
            <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-black" style={{ color: '#000000' }}>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-blue-600" style={{ color: '#2563eb', filter: 'none' }} />
                    Quick Insights
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100 text-black" style={{ color: '#000000', backgroundColor: '#f9fafb', filter: 'none' }}>
                        <div className="flex items-center gap-2 mb-2">
                            <Clock className="w-4 h-4 text-blue-600" style={{ color: '#2563eb', filter: 'none' }} />
                            <span className="text-sm font-medium text-gray-700">Peak Hours</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900">9 AM - 11 AM</p>
                        <p className="text-xs text-gray-500">Most notifications received</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100 text-black" style={{ color: '#000000', backgroundColor: '#f9fafb', filter: 'none' }}>
                        <div className="flex items-center gap-2 mb-2">
                            <Users className="w-4 h-4 text-green-600" style={{ color: '#16a34a', filter: 'none' }} />
                            <span className="text-sm font-medium text-gray-700">Top Pilot</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900">Deepchand Jaiswal</p>
                        <p className="text-xs text-gray-500">Most active pilot today</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100 text-black" style={{ color: '#000000', backgroundColor: '#f9fafb', filter: 'none' }}>
                        <div className="flex items-center gap-2 mb-2">
                            <Archive className="w-4 h-4 text-purple-600" style={{ color: '#9333ea', filter: 'none' }} />
                            <span className="text-sm font-medium text-gray-700">Resolution Rate</span>
                        </div>
                        <p className="text-lg font-bold text-gray-900">87%</p>
                        <p className="text-xs text-gray-500">Issues resolved today</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default NotificationStats;
